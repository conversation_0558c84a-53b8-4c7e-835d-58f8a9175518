// lib/models/scan/scan_models.dart
import 'package:equatable/equatable.dart';

// Base scan request/response models
abstract class ScanRequest extends Equatable {
  const ScanRequest();
}

abstract class ScanResponse extends Equatable {
  const ScanResponse();
}

// SMS Scan Models
class SMSAnalysisRequest extends ScanRequest {
  final String message;

  const SMSAnalysisRequest({required this.message});

  Map<String, dynamic> toJson() => {'message': message};

  @override
  List<Object> get props => [message];
}

class SMSAnalysisResponse extends ScanResponse {
  final String prediction;
  final bool isPhishing;
  final double? confidence;
  final String? analysisTimestamp;

  const SMSAnalysisResponse({
    required this.prediction,
    required this.isPhishing,
    this.confidence,
    this.analysisTimestamp,
  });

  factory SMSAnalysisResponse.fromJson(Map<String, dynamic> json) {
    return SMSAnalysisResponse(
      prediction: json['prediction'] ?? '',
      isPhishing: json['is_phishing'] ?? false,
      confidence: json['confidence']?.toDouble(),
      analysisTimestamp: json['analysis_timestamp'],
    );
  }

  @override
  List<Object?> get props => [prediction, isPhishing, confidence, analysisTimestamp];
}

// URL Scan Models
class URLAnalysisRequest extends ScanRequest {
  final String url;

  const URLAnalysisRequest({required this.url});

  Map<String, dynamic> toJson() => {'url': url};

  @override
  List<Object> get props => [url];
}

class URLAnalysisResponse extends ScanResponse {
  final String url;
  final bool isSafe;
  final double confidence;
  final String prediction;
  final Map<String, dynamic>? urlAnalysis;

  const URLAnalysisResponse({
    required this.url,
    required this.isSafe,
    required this.confidence,
    required this.prediction,
    this.urlAnalysis,
  });

  factory URLAnalysisResponse.fromJson(Map<String, dynamic> json) {
    return URLAnalysisResponse(
      url: json['url'] ?? '',
      isSafe: json['is_safe'] ?? false,
      confidence: json['confidence']?.toDouble() ?? 0.0,
      prediction: json['prediction'] ?? '',
      urlAnalysis: json['url_analysis'],
    );
  }

  @override
  List<Object?> get props => [url, isSafe, confidence, prediction, urlAnalysis];
}

// Email Scan Models
class EmailAnalysisResponse extends ScanResponse {
  final Map<String, dynamic> headers;
  final String senderIp;
  final Map<String, dynamic> ipInfo;
  final List<EmailAttachment> attachments;
  final EmailPhishingDetection phishingDetection;
  final String analysisTimestamp;
  final String? screenshotUrl;

  const EmailAnalysisResponse({
    required this.headers,
    required this.senderIp,
    required this.ipInfo,
    required this.attachments,
    required this.phishingDetection,
    required this.analysisTimestamp,
    this.screenshotUrl,
  });

  factory EmailAnalysisResponse.fromJson(Map<String, dynamic> json) {
    return EmailAnalysisResponse(
      headers: json['headers'] ?? {},
      senderIp: json['sender_ip'] ?? '',
      ipInfo: json['ip_info'] ?? {},
      attachments: (json['attachments'] as List?)
          ?.map((e) => EmailAttachment.fromJson(e))
          .toList() ?? [],
      phishingDetection: EmailPhishingDetection.fromJson(
        json['phishing_detection'] ?? {},
      ),
      analysisTimestamp: json['analysis_timestamp'] ?? '',
      screenshotUrl: json['screenshot_url'],
    );
  }

  @override
  List<Object?> get props => [
    headers,
    senderIp,
    ipInfo,
    attachments,
    phishingDetection,
    analysisTimestamp,
    screenshotUrl,
  ];
}

class EmailAttachment extends Equatable {
  final String filename;
  final String path;

  const EmailAttachment({
    required this.filename,
    required this.path,
  });

  factory EmailAttachment.fromJson(Map<String, dynamic> json) {
    return EmailAttachment(
      filename: json['filename'] ?? '',
      path: json['path'] ?? '',
    );
  }

  @override
  List<Object> get props => [filename, path];
}

class EmailPhishingDetection extends Equatable {
  final String? prediction;

  const EmailPhishingDetection({this.prediction});

  factory EmailPhishingDetection.fromJson(Map<String, dynamic> json) {
    return EmailPhishingDetection(
      prediction: json['prediction'],
    );
  }

  @override
  List<Object?> get props => [prediction];
}

// QR Code Scan Models
class QRAnalysisRequest extends ScanRequest {
  final String content;

  const QRAnalysisRequest({required this.content});

  Map<String, dynamic> toJson() => {'content': content};

  @override
  List<Object> get props => [content];
}

class QRAnalysisResponse extends ScanResponse {
  final String content;
  final String contentType;
  final bool isUrl;
  final Map<String, dynamic>? urlAnalysis;
  final String analysisTimestamp;

  const QRAnalysisResponse({
    required this.content,
    required this.contentType,
    required this.isUrl,
    this.urlAnalysis,
    required this.analysisTimestamp,
  });

  factory QRAnalysisResponse.fromJson(Map<String, dynamic> json) {
    return QRAnalysisResponse(
      content: json['content'] ?? '',
      contentType: json['content_type'] ?? '',
      isUrl: json['is_url'] ?? false,
      urlAnalysis: json['url_analysis'],
      analysisTimestamp: json['analysis_timestamp'] ?? '',
    );
  }

  @override
  List<Object?> get props => [content, contentType, isUrl, urlAnalysis, analysisTimestamp];
}

// APK Scan Models
class APKAnalysisResponse extends ScanResponse {
  final String filename;
  final String scanId;
  final Map<String, dynamic> scanResults;
  final List<String> threatsDetected;
  final String scanTimestamp;
  final Map<String, dynamic>? malwareDetection;

  const APKAnalysisResponse({
    required this.filename,
    required this.scanId,
    required this.scanResults,
    required this.threatsDetected,
    required this.scanTimestamp,
    this.malwareDetection,
  });

  factory APKAnalysisResponse.fromJson(Map<String, dynamic> json) {
    return APKAnalysisResponse(
      filename: json['filename'] ?? '',
      scanId: json['scan_id'] ?? '',
      scanResults: json['scan_results'] ?? {},
      threatsDetected: List<String>.from(json['threats_detected'] ?? []),
      scanTimestamp: json['scan_timestamp'] ?? '',
      malwareDetection: json['malware_detection'],
    );
  }

  @override
  List<Object?> get props => [
    filename,
    scanId,
    scanResults,
    threatsDetected,
    scanTimestamp,
    malwareDetection,
  ];
}

// Scan Result Models for GraphQL Integration
class ScanResultSubmission extends Equatable {
  final String scanType;
  final String target;
  final double threatScore;
  final String threatLevel;
  final String sr;
  final List<Finding> findings;
  final double? confidence;

  const ScanResultSubmission({
    required this.scanType,
    required this.target,
    required this.threatScore,
    required this.threatLevel,
    required this.sr,
    required this.findings,
    this.confidence,
  });

  Map<String, dynamic> toJson() => {
    'scanType': scanType,
    'target': target,
    'threatScore': threatScore,
    'threatLevel': threatLevel,
    'sr': sr,
    'findings': findings.map((f) => f.toJson()).toList(),
    'confidence': confidence,
  };

  @override
  List<Object?> get props => [scanType, target, threatScore, threatLevel, sr, findings, confidence];
}

class Finding extends Equatable {
  final String type;
  final String severity;
  final String description;
  final Map<String, dynamic>? details;

  const Finding({
    required this.type,
    required this.severity,
    required this.description,
    this.details,
  });

  Map<String, dynamic> toJson() => {
    'type': type,
    'severity': severity,
    'description': description,
    'details': details,
  };

  @override
  List<Object?> get props => [type, severity, description, details];
}
