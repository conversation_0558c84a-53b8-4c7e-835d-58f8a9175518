# syntax=docker/dockerfile:1

# Use Python 3.11 as the base image
FROM python:3.11-slim AS base

# Prevents Python from writing pyc files
ENV PYTHONDONTWRITEBYTECODE=1
# Keeps Python from buffering stdout and stderr
ENV PYTHONUNBUFFERED=1
# Set Transformers cache location
ENV TRANSFORMERS_CACHE=/app/.cache/huggingface
# Tell transformers to use local models when available
ENV TRANSFORMERS_OFFLINE=1
# Suppress sklearn and pydantic warnings
ENV PYTHONWARNINGS=ignore
ENV SKLEARN_ENABLE_WARNINGS=false

# Accept build arguments for API keys
ARG VIRUSTOTAL_API_KEY
ARG URLSCAN_API_KEY

# Set environment variables for API keys
ENV VIRUSTOTAL_API_KEY=${VIRUSTOTAL_API_KEY}
ENV URLSCAN_API_KEY=${URLSCAN_API_KEY}

WORKDIR /app

# Install system dependencies and Chrome
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    wget \
    curl \
    gnupg \
    unzip \
    # Create keyrings directory if it doesn't exist
    && mkdir -p /etc/apt/keyrings \
    # Download and add the Google Chrome signing key
    && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor > /etc/apt/keyrings/google-chrome.gpg \
    && chmod 644 /etc/apt/keyrings/google-chrome.gpg \
    # Add the Google Chrome repository
    && echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/google-chrome.gpg] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Install ChromeDriver
RUN echo "Installing ChromeDriver..." \
    # Print Chrome version for debugging
    && echo "Chrome version:" && google-chrome --version \
    # Get Chrome version
    && CHROME_VERSION=$(google-chrome --version | awk '{print $3}' | cut -d'.' -f1) \
    && echo "Extracted Chrome version: $CHROME_VERSION" \
    # Download and install ChromeDriver using the official Chrome for Linux version
    && wget -q "https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/137.0.7151.68/linux64/chromedriver-linux64.zip" \
    && unzip chromedriver-linux64.zip \
    && mv chromedriver-linux64/chromedriver /usr/local/bin/chromedriver \
    && chmod +x /usr/local/bin/chromedriver \
    && rm -rf chromedriver-linux64.zip chromedriver-linux64 \
    && echo "ChromeDriver installation completed"

# Create a non-privileged user
ARG UID=10001
RUN adduser \
    --disabled-password \
    --gecos "" \
    --home "/nonexistent" \
    --shell "/sbin/nologin" \
    --no-create-home \
    --uid "${UID}" \
    appuser

# Copy requirements file
COPY requirements.txt .

# Install PyTorch separately
RUN pip install --no-cache-dir torch==2.3.0

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Create necessary directories with proper permissions
RUN mkdir -p /app/email_screenshots /app/url_screenshots /app/.cache/huggingface /app/app/models/Enhanced/transformers /app/.cache/selenium && \
    chown -R appuser:appuser /app/email_screenshots /app/url_screenshots /app/.cache /app/app && \
    chmod -R 755 /app/.cache/selenium

# Copy the source code (excluding models)
COPY --chown=appuser:appuser app/ ./app/

# Create volume directory for model persistence
VOLUME /app/app/models/Enhanced/transformers

# Switch to non-privileged user
USER appuser

# Pre-download transformer models and save locally (will only run once if volume is used)
RUN mkdir -p /app/app/models/Enhanced/transformers/bert-finetuned-phishing && \
    python -c "import os; from transformers import AutoTokenizer, AutoModelForSequenceClassification; model_name='ealvaradob/bert-finetuned-phishing'; save_path='/app/app/models/Enhanced/transformers/bert-finetuned-phishing'; not_exists = not os.path.exists(os.path.join(save_path, 'config.json')); print(f'Downloading model {model_name} to {save_path}') if not_exists else print(f'Model already exists at {save_path}'); tokenizer = AutoTokenizer.from_pretrained(model_name) if not_exists else None; model = AutoModelForSequenceClassification.from_pretrained(model_name) if not_exists else None; tokenizer.save_pretrained(save_path) if not_exists else None; model.save_pretrained(save_path) if not_exists else None; print('Model downloaded and saved successfully') if not_exists else None"

# Expose the port the app runs on
EXPOSE 8000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Command to run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]