import tldextract
import whois
import dns.resolver
import requests
import socket
import ssl
import urllib.parse
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, Any
import asyncio
import aiohttp
import socket
from urllib.parse import urlparse
import warnings
from pathlib import Path
import logging
from dotenv import load_dotenv
import os
import certifi
from fastapi import HTTPException

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Get API key from environment variables
URLSCAN_API_KEY = os.getenv('URLSCAN_API_KEY')
URLSCAN_SUBMIT_URL = "https://urlscan.io/api/v1/scan/"
URLSCAN_RESULT_URL = "https://urlscan.io/api/v1/result/{uuid}/"

if not URLSCAN_API_KEY:
    logger.warning("URLSCAN_API_KEY not found in environment variables")

warnings.filterwarnings("ignore", category=UserWarning)


class URLAnalyzer:
    def __init__(self, url: str):
        self.url = url
        self.headers = {
            "API-Key": URLSCAN_API_KEY,
            "Content-Type": "application/json"
        }
        # Create SSL context that loads default certificates
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())
        self.screenshot_dir = Path("url_screenshots")
        self.screenshot_dir.mkdir(exist_ok=True)
        self.parsed_url = urlparse(url)
        self.extracted = tldextract.extract(url)

    async def submit_scan(self) -> str:
        """Submit URL for scanning and return UUID"""
        if not self.url.startswith(('http://', 'https://')):
            self.url = f'http://{self.url}'

        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
            payload = {
                "url": self.url,
                "visibility": "unlisted",  # Using unlisted instead of public
                "tags": ["darkops-scan"]
            }
            try:
                async with session.post(URLSCAN_SUBMIT_URL, json=payload, headers=self.headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise HTTPException(
                            status_code=response.status, 
                            detail=f"Failed to submit URL scan: {error_text}"
                        )
                    result = await response.json()
                    return result.get("uuid")
            except aiohttp.ClientError as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Connection error while submitting URL scan: {str(e)}"
                )

    async def download_screenshot(self, uuid: str) -> str:
        """Download and save screenshot"""
        screenshot_url = f"https://urlscan.io/screenshots/{uuid}.png"
        screenshot_path = self.screenshot_dir / f"{uuid}.png"
        
        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(screenshot_url) as response:
                    if response.status == 200:
                        content = await response.read()
                        screenshot_path.write_bytes(content)
                        return str(screenshot_path)
        except Exception as e:
            logger.error(f"Failed to download screenshot: {e}")
        return ""  

    async def get_scan_results(self, uuid: str) -> Dict[str, Any]:
        """Get scan results using UUID"""
        max_retries = 30
        retry_delay = 3

        for _ in range(max_retries):
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(URLSCAN_RESULT_URL.format(uuid=uuid), headers=self.headers) as response:
                    if response.status == 404:
                        await asyncio.sleep(retry_delay)
                        continue
                    elif response.status == 200:
                        return await response.json()
                    else:
                        raise HTTPException(status_code=response.status, detail="Failed to get scan results")
        raise HTTPException(status_code=500, detail="Max retries exceeded for scan results")    

    async def analyze(self) -> Dict[str, Any]:
        """Perform URL analysis using URLScan.io"""
        try:
            uuid = await self.submit_scan()
            results = await self.get_scan_results(uuid)
            screenshot_path = await self.download_screenshot(uuid)
            dom_security = await self.analyze_dom_security(results)

            analysis = {
                "basic_info": {
                    "domain": results.get("page", {}).get("domain"),
                    "ip": results.get("page", {}).get("ip"),
                    "country": results.get("page", {}).get("country"),
                    "server": results.get("page", {}).get("server"),
                    "security_state": results.get("page", {}).get("securityState"),
                    "final_url": results.get("page", {}).get("url"),
                },
                "technologies": results.get("page", {}).get("technologies", []),
                "behavior": {
                    "requests": results.get("stats", {}).get("requests", 0),
                    "domains": results.get("stats", {}).get("domains", 0),
                    "resources": results.get("stats", {}).get("resourceTypes", {}),
                    "redirects": results.get("stats", {}).get("redirects", 0),
                    "mixed_content": results.get("stats", {}).get("mixedContent", 0),
                },
                "security": {
                    "malicious": results.get("verdicts", {}).get("overall", {}).get("malicious"),
                    "score": results.get("verdicts", {}).get("overall", {}).get("score"),
                    "categories": results.get("verdicts", {}).get("overall", {}).get("categories", []),
                    "brands": results.get("brands", []),
                    "threats": results.get("threats", []),
                    "dom_security": dom_security,
                    "certificates": results.get("lists", {}).get("certificates", []),
                    "security_headers": results.get("headers", {}).get("security", {}),
                },
                "console_messages": results.get("console", []),
                "cookies": results.get("cookies", []),
                "scan_info": {
                    "scan_id": uuid,
                    "scan_result_url": f"https://urlscan.io/result/{uuid}/",
                    "screenshot_url": f"https://urlscan.io/screenshots/{uuid}.png",
                    "screenshot_path": screenshot_path,
                    "scan_time": results.get("task", {}).get("time"),
                    "analysis_time": results.get("stats", {}).get("analysisTime"),
                }
            }
            
            return analysis
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error in URLScan analysis: {str(e)}")


    async def analyze_dom_security(self, results: Dict) -> Dict[str, Any]:
        """Analyze DOM-based security issues"""
        return {
            "vulnerable_js_libs": results.get("lists", {}).get("vulnerabilities", []),
            "external_scripts": len(results.get("lists", {}).get("scripts", [])),
            "forms": len(results.get("lists", {}).get("forms", [])),
            "password_fields": len([f for f in results.get("lists", {}).get("forms", []) 
                                  if any(i.get("type") == "password" for i in f.get("inputs", []))]),
            "suspicious_elements": results.get("lists", {}).get("suspiciousElements", []),
        }        

    def basic_analysis(self) -> Dict[str, Any]:
        """Analyze basic URL components"""
        return {
            'scheme': self.parsed_url.scheme,
            'netloc': self.parsed_url.netloc,
            'path': self.parsed_url.path,
            'params': self.parsed_url.params,
            'query': self.parse_query_parameters(),
            'fragment': self.parsed_url.fragment,
            'domain': {
                'subdomain': self.extracted.subdomain,
                'domain': self.extracted.domain,
                'suffix': self.extracted.suffix,
                'is_ip': self.is_ip_address(self.parsed_url.netloc),
            },
            'url_length': len(self.url),
            'special_characters': self.analyze_special_characters(),
        }

    def parse_query_parameters(self) -> Dict[str, str]:
        """Parse and analyze query parameters"""
        query_params = urllib.parse.parse_qs(self.parsed_url.query)
        return {k: v[0] if len(v) == 1 else v for k, v in query_params.items()}

    async def get_dns_info(self) -> Dict[str, Any]:
        """Get DNS information"""
        try:
            domain = self.parsed_url.netloc
            dns_info = {
                'a_records': [],
                'mx_records': [],
                'ns_records': [],
                'txt_records': [],
            }

            # Run DNS queries asynchronously
            loop = asyncio.get_event_loop()
            tasks = [
                loop.run_in_executor(None, dns.resolver.resolve, domain, 'A'),
                loop.run_in_executor(None, dns.resolver.resolve, domain, 'MX'),
                loop.run_in_executor(None, dns.resolver.resolve, domain, 'NS'),
                loop.run_in_executor(None, dns.resolver.resolve, domain, 'TXT'),
            ]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            if not isinstance(results[0], Exception):
                dns_info['a_records'] = [str(x) for x in results[0]]
            if not isinstance(results[1], Exception):
                dns_info['mx_records'] = [str(x.exchange) for x in results[1]]
            if not isinstance(results[2], Exception):
                dns_info['ns_records'] = [str(x) for x in results[2]]
            if not isinstance(results[3], Exception):
                dns_info['txt_records'] = [str(x) for x in results[3]]

            return dns_info
        except Exception as e:
            return {'error': str(e)}

    async def get_whois_info(self) -> Dict[str, Any]:
        """Get WHOIS information"""
        try:
            domain = self.extracted.registered_domain
            if not domain:
                return {'error': 'Invalid or missing domain name'}

            # Validate domain format
            if not self._is_valid_domain(domain):
                return {'error': 'Invalid domain format'}

            loop = asyncio.get_event_loop()
            try:
                whois_info = await loop.run_in_executor(None, whois.whois, domain)
                
                if not whois_info or not any([
                    whois_info.registrar,
                    whois_info.creation_date,
                    whois_info.expiration_date
                ]):
                    return {'error': 'No WHOIS information available'}

                return {
                    'registrar': whois_info.registrar,
                    'creation_date': str(whois_info.creation_date),
                    'expiration_date': str(whois_info.expiration_date),
                    'last_updated': str(whois_info.updated_date),
                    'status': whois_info.status,
                    'name_servers': whois_info.name_servers,
                }
            except whois.parser.PywhoisError as e:
                return {'error': f'WHOIS lookup failed: {str(e)}'}
            except socket.gaierror:
                return {'error': 'Failed to resolve domain name'}
            except Exception as e:
                return {'error': f'WHOIS lookup error: {str(e)}'}
        except Exception as e:
            return {'error': f'General error: {str(e)}'}
    def _is_valid_domain(self, domain: str) -> bool:
        """Validate domain name format"""
        if not domain:
            return False
        # Basic domain format validation
        domain_pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
        return bool(re.match(domain_pattern, domain))
    async def get_ssl_info(self) -> Dict[str, Any]:
        """Get SSL certificate information"""
        if self.parsed_url.scheme != 'https':
            return {'error': 'Not an HTTPS URL'}

        try:
            hostname = self.parsed_url.netloc
            
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(self.url) as response:
                    # Get the SSL certificate
                    try:
                        cert = response.connection.transport.get_extra_info('socket').getpeercert()
                        if not cert:
                            # If we can't get the certificate details through aiohttp, try direct SSL connection
                            cert = await self._get_cert_direct(hostname)
                        
                        return {
                            'issuer': dict(x[0] for x in cert['issuer']),
                            'subject': dict(x[0] for x in cert['subject']),
                            'version': cert['version'],
                            'serial_number': cert['serialNumber'],
                            'not_before': cert['notBefore'],
                            'not_after': cert['notAfter'],
                            'san': cert.get('subjectAltName', []),
                        }
                    except (KeyError, TypeError, AttributeError):
                        # Fallback to direct SSL connection if certificate info is incomplete
                        cert = await self._get_cert_direct(hostname)
                        return {
                            'issuer': dict(x[0] for x in cert['issuer']),
                            'subject': dict(x[0] for x in cert['subject']),
                            'version': cert['version'],
                            'serial_number': cert['serialNumber'],
                            'not_before': cert['notBefore'],
                            'not_after': cert['notAfter'],
                            'san': cert.get('subjectAltName', []),
                        }
        except Exception as e:
            return {'error': str(e)}

    async def _get_cert_direct(self, hostname: str) -> Dict:
        """Get certificate information directly using SSL connection"""
        try:
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((hostname, 443)) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    return cert
        except Exception as e:
            raise Exception(f"Failed to get certificate directly: {str(e)}")

    async def get_header_info(self) -> Dict[str, Any]:
        """Get HTTP header information"""
        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(self.url) as response:
                    return dict(response.headers)
        except Exception as e:
            return {'error': str(e)}

    async def analyze_content(self) -> Dict[str, Any]:
        """Analyze page content"""
        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(self.url) as response:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    return {
                        'title': soup.title.string if soup.title else None,
                        'meta_tags': self.extract_meta_tags(soup),
                        'links': self.analyze_links(soup),
                        'scripts': self.analyze_scripts(soup),
                        'forms': self.analyze_forms(soup),
                        'images': len(soup.find_all('img')),
                        'iframes': len(soup.find_all('iframe')),
                        'total_length': len(content),
                    }
        except Exception as e:
            return {'error': str(e)}

    def extract_meta_tags(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract meta tags from HTML"""
        meta_tags = {}
        for tag in soup.find_all('meta'):
            name = tag.get('name', tag.get('property', ''))
            content = tag.get('content', '')
            if name and content:
                meta_tags[name] = content
        return meta_tags

    def analyze_links(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze links in the page"""
        links = soup.find_all('a')
        return {
            'total': len(links),
            'external': len([l for l in links if self.is_external_link(l.get('href', ''))]),
            'internal': len([l for l in links if not self.is_external_link(l.get('href', ''))])
        }

    def analyze_scripts(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze script tags"""
        scripts = soup.find_all('script')
        return {
            'total': len(scripts),
            'external': len([s for s in scripts if s.get('src')])
        }

    def analyze_forms(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze form elements"""
        forms = soup.find_all('form')
        return {
            'total': len(forms),
            'with_password_fields': len([f for f in forms if f.find('input', {'type': 'password'})])
        }

    async def check_security_headers(self) -> Dict[str, bool]:
        """Check security-related HTTP headers"""
        security_headers = {
            'Strict-Transport-Security': False,
            'Content-Security-Policy': False,
            'X-Frame-Options': False,
            'X-Content-Type-Options': False,
            'X-XSS-Protection': False,
            'Referrer-Policy': False,
        }

        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(self.url) as response:
                    headers = response.headers
                    for header in security_headers:
                        security_headers[header] = header in headers
            return security_headers
        except Exception as e:
            return {'error': str(e)}

    async def analyze_redirects(self, max_redirects: int = 5) -> list:
        """Analyze redirect chain"""
        redirect_chain = []
        current_url = self.url
        
        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                for _ in range(max_redirects):
                    async with session.get(current_url, allow_redirects=False) as response:
                        redirect_chain.append({
                            'url': current_url,
                            'status': response.status,
                            'location': response.headers.get('Location')
                        })
                        
                        if response.status not in (301, 302, 303, 307, 308):
                            break
                            
                        current_url = response.headers.get('Location')
                        if not current_url:
                            break
            
            return redirect_chain
        except Exception as e:
            return {'error': str(e)}

    @staticmethod
    def is_ip_address(hostname: str) -> bool:
        """Check if the hostname is an IP address"""
        try:
            socket.inet_aton(hostname)
            return True
        except socket.error:
            return False

    def is_external_link(self, href: str) -> bool:
        """Check if a link is external"""
        if not href:
            return False
        return not href.startswith('/') and self.extracted.domain not in href

    def analyze_special_characters(self) -> Dict[str, int]:
        """Analyze special characters in URL"""
        special_chars = {
            'dots': self.url.count('.'),
            'hyphens': self.url.count('-'),
            'underscores': self.url.count('_'),
            'slashes': self.url.count('/'),
            'question_marks': self.url.count('?'),
            'equal_signs': self.url.count('='),
            'ampersands': self.url.count('&'),
            'percent': self.url.count('%'),
        }
        return special_chars


class EnhancedURLAnalyzer(URLAnalyzer):
    """Enhanced URL Analyzer with additional security checks"""
    
    async def analyze(self) -> Dict[str, Any]:
        """Perform enhanced URL analysis with fallback for blocked URLs"""
        try:
            # Try URLScan.io analysis first
            try:
                uuid = await self.submit_scan()
                results = await self.get_scan_results(uuid)
                screenshot_path = await self.download_screenshot(uuid)
                dom_security = await self.analyze_dom_security(results)

                analysis = {
                    "basic_info": {
                        "domain": results.get("page", {}).get("domain"),
                        "ip": results.get("page", {}).get("ip"),
                        "country": results.get("page", {}).get("country"),
                        "server": results.get("page", {}).get("server"),
                        "security_state": results.get("page", {}).get("securityState"),
                        "final_url": results.get("page", {}).get("url"),
                    },
                    "technologies": results.get("page", {}).get("technologies", []),
                    "behavior": {
                        "requests": results.get("stats", {}).get("requests", 0),
                        "domains": results.get("stats", {}).get("domains", 0),
                        "resources": results.get("stats", {}).get("resourceTypes", {}),
                        "redirects": results.get("stats", {}).get("redirects", 0),
                        "mixed_content": results.get("stats", {}).get("mixedContent", 0),
                    },
                    "security": {
                        "malicious": results.get("verdicts", {}).get("overall", {}).get("malicious"),
                        "score": results.get("verdicts", {}).get("overall", {}).get("score"),
                        "categories": results.get("verdicts", {}).get("overall", {}).get("categories", []),
                        "brands": results.get("brands", []),
                        "threats": results.get("threats", []),
                        "dom_security": dom_security,
                        "certificates": results.get("lists", {}).get("certificates", []),
                        "security_headers": results.get("headers", {}).get("security", {}),
                    },
                    "console_messages": results.get("console", []),
                    "cookies": results.get("cookies", []),
                    "scan_info": {
                        "scan_id": uuid,
                        "scan_result_url": f"https://urlscan.io/result/{uuid}/",
                        "screenshot_url": f"https://urlscan.io/screenshots/{uuid}.png",
                        "screenshot_path": screenshot_path,
                        "scan_time": results.get("task", {}).get("time"),
                        "analysis_time": results.get("stats", {}).get("analysisTime"),
                    }
                }
                return analysis
            except HTTPException as e:
                if "blocked from scanning" in str(e.detail):
                    # Fallback to alternative analysis for blocked URLs
                    return await self._perform_alternative_analysis()
                raise

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error in URL analysis: {str(e)}")

    async def _perform_alternative_analysis(self) -> Dict[str, Any]:
        """Perform alternative analysis when URLScan.io is blocked"""
        try:
            # Gather basic information
            basic_info = self.basic_analysis()
            dns_info = await self.get_dns_info()
            whois_info = await self.get_whois_info()
            ssl_info = await self.get_ssl_info()
            header_info = await self.get_header_info()
            security_headers = await self.check_security_headers()
            domain_age = await self.get_domain_age()
            ssl_validity = await self.check_ssl_validity()

            return {
                "basic_info": {
                    "domain": self.extracted.registered_domain,
                    "url": self.url,
                    "parsed_components": basic_info,
                },
                "dns_analysis": dns_info,
                "whois_info": whois_info,
                "ssl_analysis": {
                    **ssl_info,
                    "validity": ssl_validity
                },
                "headers": {
                    **header_info,
                    "security_headers": security_headers
                },
                "domain_metrics": {
                    "age": domain_age,
                    "is_ip": self.is_ip_address(self.parsed_url.netloc),
                },
                "scan_info": {
                    "analysis_type": "alternative",
                    "reason": "URL blocked from URLScan.io scanning",
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error in alternative URL analysis: {str(e)}"
            )

    async def check_enhanced_security(self) -> Dict[str, Any]:
        """Perform additional security checks"""
        try:
            security_checks = {
                'mixed_content': False,
                'vulnerable_libraries': [],
                'suspicious_redirects': False,
                'insecure_cookies': False
            }
            
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(self.url) as response:
                    content = await response.text()
                    
                    # Check for mixed content
                    security_checks['mixed_content'] = 'http:' in content and self.parsed_url.scheme == 'https'
                    
                    # Check cookies
                    cookies = response.cookies
                    security_checks['insecure_cookies'] = any(
                        not c.get('secure', False) or not c.get('httponly', False) 
                        for c in cookies.values()
                    )
                    
                    # Check redirects
                    redirect_chain = await self.analyze_redirects()
                    security_checks['suspicious_redirects'] = any(
                        'error' in r or r.get('status', 0) in (301, 302)
                        for r in (redirect_chain if isinstance(redirect_chain, list) else [])
                    )
            
            return security_checks
        except Exception as e:
            return {'error': str(e)}

    async def check_reputation(self) -> Dict[str, Any]:
        """Check domain reputation using various indicators"""
        try:
            return {
                'domain_age': await self.get_domain_age(),
                'ssl_validity': await self.check_ssl_validity(),
                'blacklist_status': 'Not implemented',  # Placeholder for blacklist checking
            }
        except Exception as e:
            return {'error': str(e)}

    async def detect_technologies(self) -> Dict[str, Any]:
        """Detect technologies used on the website"""

        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=self.ssl_context)) as session:
                async with session.get(self.url) as response:
                    content = await response.text()
                    headers = response.headers
                    
                    technologies = {
                        'server': headers.get('Server', 'Unknown'),
                        'frameworks': [],
                        'analytics': [],
                        'cms': 'Unknown'
                    }
                    
                    # Basic technology detection
                    if 'wordpress' in content.lower():
                        technologies['cms'] = 'WordPress'
                    elif 'drupal' in content.lower():
                        technologies['cms'] = 'Drupal'
                    
                    if 'google-analytics' in content:
                        technologies['analytics'].append('Google Analytics')
                    
                    if 'react' in content:
                        technologies['frameworks'].append('React')
                    elif 'vue' in content:
                        technologies['frameworks'].append('Vue.js')
                    elif 'angular' in content:
                        technologies['frameworks'].append('Angular')
                    
                    return technologies

        except Exception as e:
            return {'error': str(e)}

    async def get_domain_age(self) -> Dict[str, Any]:
        """Calculate domain age based on WHOIS information"""
        try:
            whois_info = await self.get_whois_info()
            if 'error' in whois_info:
                return {'error': 'Unable to determine domain age'}
            
            creation_date = whois_info.get('creation_date')
            if creation_date and creation_date != 'None':
                try:
                    created = datetime.strptime(creation_date.split('[')[0], '%Y-%m-%d %H:%M:%S')
                    age = datetime.now() - created
                    return {
                        'days': age.days,
                        'created': creation_date
                    }
                except (ValueError, AttributeError):
                    return {'error': 'Invalid date format'}
            return {'error': 'No creation date found'}
        except Exception as e:
            return {'error': str(e)}

    async def check_ssl_validity(self) -> Dict[str, bool]:
        """Check SSL certificate validity"""
        ssl_info = await self.get_ssl_info()
        if 'error' in ssl_info:
            return {'valid': False, 'reason': ssl_info['error']}
        
        try:
            not_after = datetime.strptime(ssl_info['not_after'], '%b %d %H:%M:%S %Y GMT')
            is_valid = datetime.now() < not_after
            return {
                'valid': is_valid,
                'expires': ssl_info['not_after'],
                'issuer': ssl_info['issuer'].get('commonName', 'Unknown')
            }

        except Exception as e:
            return {'valid': False, 'reason': str(e)}