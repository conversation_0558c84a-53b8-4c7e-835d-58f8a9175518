from typing import Dict, Any
from fastapi import HTTPException
from app.utils.feature_extraction import FeatureExtraction
try:
    from app.models.url_model import gbc
except ImportError as e:
    raise ImportError(f"Failed to load URL model: {str(e)}")
from app.services.url_analyzer import URLAnalyzer, EnhancedURLAnalyzer
import numpy as np

async def scan_url(url: str) -> Dict[str, Any]:
    """Scan URL for phishing detection"""
    try:
        obj = FeatureExtraction(url)
        features = np.array(obj.getFeaturesList()).reshape(1,30)

        y_pro_phishing = gbc.predict_proba(features)[0,0]
        y_pro_non_phishing = gbc.predict_proba(features)[0,1]
        prediction = gbc.predict(features)[0]
        is_safe = bool(prediction == 1)
        confidence = y_pro_non_phishing if is_safe else y_pro_phishing

        return {
            "url": url,
            "is_safe": is_safe,
            "confidence": float(confidence),
            "prediction": "Safe" if is_safe else "Phishing"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing URL: {str(e)}")

async def analyze_url(url: str) -> Dict[str, Any]:
    """Perform comprehensive URL analysis"""
    try:
        analyzer = EnhancedURLAnalyzer(url)
        analysis_results = await analyzer.analyze()
        return analysis_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing URL: {str(e)}")

async def full_analysis(url: str) -> Dict[str, Any]:
    """Perform both phishing detection and comprehensive URL analysis"""
    try:
        # Get phishing analysis
        phishing_result = await scan_url(url)
        
        # Get URL analysis with fallback for blocked URLs
        try:
            analyzer = EnhancedURLAnalyzer(url)
            url_analysis = await analyzer.analyze()
        except HTTPException as e:
            if "blocked from scanning" in str(e.detail):
                # Use alternative analysis
                analyzer = EnhancedURLAnalyzer(url)
                url_analysis = await analyzer._perform_alternative_analysis()
            else:
                raise

        return {
            "phishing_analysis": phishing_result,
            "url_analysis": url_analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error performing full analysis: {str(e)}")