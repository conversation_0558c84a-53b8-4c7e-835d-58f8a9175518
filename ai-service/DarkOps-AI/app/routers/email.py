from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import FileResponse
from app.services.email_service import EmailAnaly<PERSON>, detect_email_phishing
from pydantic import BaseModel
import os
from pathlib import Path
import tempfile
import time
from pathlib import Path
from email.parser import BytesParser
from email import policy

router = APIRouter(prefix="/email", tags=["Email Analysis"])

# Create screenshots directory
SCREENSHOTS_DIR = Path("email_screenshots")
SCREENSHOTS_DIR.mkdir(exist_ok=True)

class EmailIdInput(BaseModel):
    email_id: str

@router.post("/detect-phishing")
async def detect_phishing(email_file: UploadFile = File(...)):
    """
    Detect phishing in email file (.eml)
    """
    try:
        if not email_file.filename.endswith('.eml'):
            raise HTTPException(status_code=400, detail="Only .eml files are supported")

        # Save uploaded file temporarily
        temp_path = tempfile.mktemp(suffix='.eml')
        with open(temp_path, 'wb') as f:
            content = await email_file.read()
            f.write(content)

        # Parse email and extract text
        with open(temp_path, 'rb') as f:
            msg = BytesParser(policy=policy.default).parse(f)
        
        analyzer = EmailAnalyzer()
        email_text = analyzer.extract_text_from_email(msg)
        
        if not email_text:
            raise HTTPException(status_code=400, detail="No text content found in email")

        # Perform phishing detection
        result = await detect_email_phishing(email_text)
        
        # Cleanup
        os.remove(temp_path)
        
        return result
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze")
async def analyze_email(email_file: UploadFile = File(...)):
    """
    Analyze email file and return comprehensive analysis results
    """
    try:
        # Save uploaded file temporarily
        temp_path = tempfile.mktemp(suffix='.eml')
        with open(temp_path, 'wb') as f:
            content = await email_file.read()
            f.write(content)

        analyzer = EmailAnalyzer()
        results = await analyzer.analyze_email(temp_path)
        
        # Cleanup
        os.remove(temp_path)
        
        return results
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/scan-attachment")
async def scan_attachment(file: UploadFile = File(...)):
    """
    Scan an attachment using VirusTotal API
    """
    try:
        temp_path = tempfile.mktemp()
        with open(temp_path, 'wb') as f:
            content = await file.read()
            f.write(content)

        analyzer = EmailAnalyzer()
        scan_results = analyzer.scan_file_virustotal(temp_path)

        # Cleanup
        os.remove(temp_path)

        return scan_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/capture-screenshot")
async def capture_screenshot(email_file: UploadFile = File(...)):
    """
    Capture screenshot of email content and return as downloadable image
    """
    try:
        temp_path = tempfile.mktemp(suffix='.eml')
        with open(temp_path, 'wb') as f:
            content = await email_file.read()
            f.write(content)

        from email.parser import BytesParser
        from email import policy

        # Generate unique filename for screenshot
        screenshot_path = SCREENSHOTS_DIR / f"email_{int(time.time())}.jpg"

        # Capture screenshot
        analyzer = EmailAnalyzer()
        with open(temp_path, 'rb') as f:
            msg = BytesParser(policy=policy.default).parse(f)
        
        if analyzer.capture_screenshot(msg, str(screenshot_path)):
            # Cleanup
            os.remove(temp_path)
            
            return FileResponse(
                path=screenshot_path,
                filename=screenshot_path.name,
                media_type='image/jpeg'
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to capture screenshot")
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ai/capture-screenshot")
async def capture_screenshot_by_id(email_input: EmailIdInput):
    """
    Capture screenshot of email content using email ID and return as downloadable image
    """
    try:
        # TODO: Implement email retrieval by ID from your storage system
        # For now, we'll raise an error
        raise HTTPException(
            status_code=501,
            detail="Email retrieval by ID not implemented yet. Please use the file upload endpoint instead."
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))