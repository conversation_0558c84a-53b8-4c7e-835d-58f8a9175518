version: "3.8"

services:
  app:
    build:
      context: .
      args:
        VIRUSTOTAL_API_KEY: ${VIRUSTOTAL_API_KEY}
        URLSCAN_API_KEY: ${URLSCAN_API_KEY}
    ports:
      - "8000:8000"
    volumes:
      - ./app:/app/app
      - transformer_models:/app/app/models/Enhanced/transformers
    restart: unless-stopped
    env_file:
      - .env

volumes:
  transformer_models:
    # Named volume for transformer models
    # This ensures models are persisted even when containers are removed
