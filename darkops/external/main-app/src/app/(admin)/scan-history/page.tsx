"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import ScanHistoryTable from "@/components/scan-history/ScanHistoryTable";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { invalidateScanHistory } from "@/hooks/useScanHistory";
import GraphQLService from "@/services/graphql.service";
import { toast } from "sonner";
import {
  Download,
  CheckCircle2,
  AlertCircle,
  ChevronDown,
  Smartphone,
  Globe,
  Mail,
  QrCode,
  Plus,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function ScanHistoryPage() {
  const router = useRouter();
  const queryClient = useQueryClient();

  // State for pagination and filtering
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [scanTypeFilter, setScanTypeFilter] = useState<string | undefined>(
    undefined,
  );
  const [startDate, setStartDate] = useState<string | undefined>(undefined);
  const [endDate, setEndDate] = useState<string | undefined>(undefined);
  const [threatLevelFilter, setThreatLevelFilter] = useState<
    string | undefined
  >(undefined);
  const [srFilter, setSrFilter] = useState<string | undefined>(undefined);
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<string>("DESC");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Export mutation
  const exportMutation = useMutation({
    mutationFn: async () => {
      const variables = {
        scanType: scanTypeFilter,
        startDate,
        endDate,
        threatLevel: threatLevelFilter,
        sr: srFilter,
      };
      toast.loading("Preparing download...", {
        icon: <Download className="h-4 w-4 text-green-600" />,
      });
      return GraphQLService.exportScanHistory(variables);
    },
    onSuccess: (data) => {
      toast.dismiss(); // Dismiss the loading toast
      toast.success("Download starting...", {
        icon: <CheckCircle2 className="h-4 w-4 text-green-600" />,
        style: { background: "#f0fdf4", color: "#166534" },
      });

      const byteCharacters = atob(data.exportScanHistory.file);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = data.exportScanHistory.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Scan history exported successfully", {
        icon: <CheckCircle2 className="h-4 w-4 text-green-600" />,
        style: { background: "#f0fdf4", color: "#166534" },
      });
    },
    onError: (error) => {
      toast.dismiss(); // Dismiss the loading toast
      toast.error("Failed to export scan history", {
        icon: <AlertCircle className="h-4 w-4 text-red-600" />,
        style: { background: "#fef2f2", color: "#991b1b" },
      });
      console.error("Export error:", error);
    },
    onSettled: () => {
      setIsExporting(false);
    },
  });

  // Handle export button click
  const handleExport = () => {
    setIsExporting(true);
    exportMutation.mutate();
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // Handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  };

  // Handle scan type filter change
  const handleScanTypeFilterChange = (newScanType?: string) => {
    setScanTypeFilter(newScanType);
    setPage(1); // Reset to first page when changing filter
  };

  // Handle date range change
  const handleDateRangeChange = (
    newStartDate?: string,
    newEndDate?: string,
  ) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setPage(1); // Reset to first page when changing filter
  };

  // Handle threat level filter change
  const handleThreatLevelChange = (newThreatLevel?: string) => {
    setThreatLevelFilter(newThreatLevel);
    setPage(1); // Reset to first page when changing filter
  };

  // Handle scan result filter change
  const handleSrChange = (newSr?: string) => {
    setSrFilter(newSr);
    setPage(1); // Reset to first page when changing filter
  };

  // Handle sort change
  const handleSortChange = (field: string, order: string) => {
    setSortField(field);
    setSortOrder(order);
    setPage(1); // Reset to first page when changing sort
  };

  // Handle refresh button click
  const handleRefresh = () => {
    setIsRefreshing(true);

    // Get the current variables
    const variables = {
      page,
      limit,
      scanType: scanTypeFilter,
      startDate,
      endDate,
      threatLevel: threatLevelFilter,
      sr: srFilter,
      sortField,
      sortOrder,
      isApkScan: scanTypeFilter === "APK",
      isUrlScan: scanTypeFilter === "URL",
      isEmailScan: scanTypeFilter === "EMAIL",
      isSmsScan: scanTypeFilter === "SMS",
    };

    // Invalidate and refetch the current query
    invalidateScanHistory(queryClient, variables, true)
      .then(() => {
        // Set a timeout to ensure the loading state is visible for at least 500ms
        setTimeout(() => {
          setIsRefreshing(false);
        }, 500);
      })
      .catch(() => {
        setIsRefreshing(false);
      });
  };

  return (
    <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="w-full sm:w-auto">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Scan History
            </h1>
            <p className="mt-1 text-sm text-gray-600 sm:text-base dark:text-gray-300">
              View and manage your previous security scans
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <button
              onClick={handleExport}
              disabled={isExporting}
              className="inline-flex w-full items-center justify-center gap-2 rounded-lg bg-green-600 px-4 py-2 text-sm text-white hover:bg-green-700 disabled:cursor-not-allowed disabled:opacity-50 sm:w-auto dark:bg-green-600 dark:text-white dark:hover:bg-green-700"
            >
              {isExporting ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </>
              )}
            </button>
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="inline-flex w-full items-center justify-center gap-2 rounded-lg bg-gray-100 px-4 py-2 text-sm text-gray-700 hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50 sm:w-auto dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={isRefreshing ? "animate-spin" : ""}
              >
                <path d="M21 2v6h-6"></path>
                <path d="M3 12a9 9 0 0 1 15-6.7L21 8"></path>
                <path d="M3 22v-6h6"></path>
                <path d="M21 12a9 9 0 0 1-15 6.7L3 16"></path>
              </svg>
              {isRefreshing ? "Refreshing..." : "Refresh"}
            </button>
            <DropdownMenu>
              <DropdownMenuTrigger className="inline-flex w-full items-center justify-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-sm text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto dark:bg-indigo-600 dark:hover:bg-indigo-700">
                <Plus className="h-4 w-4" />
                New Scan
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="absolute right-0 z-10 mt-2 w-56 rounded-lg border border-gray-200 bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800">
                <div className="flex flex-col gap-2">
                  <DropdownMenuItem
                    onClick={() => router.push("/apk-scanner")}
                    className="flex w-full items-center gap-2 rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 dark:text-gray-200 dark:hover:bg-indigo-900/50"
                  >
                    <Smartphone className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    <span>APK Scanner</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push("/url-scanner")}
                    className="flex w-full items-center gap-2 rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 dark:text-gray-200 dark:hover:bg-indigo-900/50"
                  >
                    <Globe className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    <span>URL Scanner</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push("/email-analyzer")}
                    className="flex w-full items-center gap-2 rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 dark:text-gray-200 dark:hover:bg-indigo-900/50"
                  >
                    <Mail className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    <span>Email Analyzer</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push("/qr-scanner")}
                    className="flex w-full items-center gap-2 rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 dark:text-gray-200 dark:hover:bg-indigo-900/50"
                  >
                    <QrCode className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    <span>QR Scanner</span>
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <ScanHistoryTable
          page={page}
          limit={limit}
          scanTypeFilter={scanTypeFilter}
          startDate={startDate}
          endDate={endDate}
          threatLevelFilter={threatLevelFilter}
          srFilter={srFilter}
          sortField={sortField}
          sortOrder={sortOrder}
          isRefreshing={isRefreshing}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          onScanTypeFilterChange={handleScanTypeFilterChange}
          onDateRangeChange={handleDateRangeChange}
          onThreatLevelChange={handleThreatLevelChange}
          onSrChange={handleSrChange}
          onSortChange={handleSortChange}
        />
      </motion.div>
    </div>
  );
}
