"use client";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import EmailUploader from "@/components/email-analyzer/EmailUploader";
import EmailAnalysisResults from "@/components/email-analyzer/EmailAnalysisResults";
import {
  EmailAnalysisService,
  EmailAnalysisResponse,
} from "@/services/email.service";
import useAuthStore from "@/store/useAuthStore";

export default function EmailAnalyzerPage() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [analysisResults, setAnalysisResults] =
    useState<EmailAnalysisResponse | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { isAuthenticated, isLoading } = useAuthStore();
  const router = useRouter();

  // Check if user is authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast.error("Authentication required", {
        description: "Please sign in to use the Email Analyzer",
        duration: 5000,
      });
      router.replace("/signin");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleUpload = async (file: File) => {
    // Check if user is authenticated before proceeding
    if (!isAuthenticated) {
      toast.error("Authentication required", {
        description: "Please sign in to use the Email Analyzer",
        duration: 5000,
      });
      router.replace("/signin");
      return;
    }

    setUploadedFile(file);
    setIsAnalyzing(true);
    setError(null);

    try {
      // Check if auth token exists
      const token = localStorage.getItem("auth-token");
      if (!token) {
        setError("Authentication token not found. Please sign in again.");
        setIsAnalyzing(false);
        return;
      }

      // Call the email analysis service
      const results = await EmailAnalysisService.analyzeEmail(file);

      // If screenshot_url is not provided, try to capture a screenshot
      if (
        !results.screenshot_url &&
        results.headers &&
        results.headers["Message-ID"]
      ) {
        try {
          const screenshotUrl = await EmailAnalysisService.captureScreenshot(
            results.headers["Message-ID"],
            file,
          );
          if (screenshotUrl) {
            results.screenshot_url = screenshotUrl;
          }
        } catch (screenshotError) {
          console.error("Error capturing screenshot:", screenshotError);
          // Continue without screenshot
        }
      }

      setAnalysisResults(results);
      setIsAnalyzing(false);
      setAnalysisComplete(true);
    } catch (err: any) {
      console.error("Error analyzing email:", err);

      // Check for authentication errors
      if (err.response?.status === 401) {
        setError("Authentication failed. Please sign in again.");
        toast.error("Authentication failed", {
          description: "Your session may have expired. Please sign in again.",
          duration: 5000,
        });
      } else {
        setError(
          "An error occurred while analyzing the email. Please try again.",
        );
      }

      setIsAnalyzing(false);
    }
  };

  const handleAnalyzeAgain = () => {
    setAnalysisComplete(false);
    setAnalysisResults(null);
    setUploadedFile(null);
  };

  return (
    <div className="mx-auto max-w-4xl">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {!analysisComplete ? (
          <>
            <EmailUploader onUpload={handleUpload} isAnalyzing={isAnalyzing} />
            {error && (
              <div className="mt-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-600">
                {error}
              </div>
            )}
          </>
        ) : (
          <EmailAnalysisResults
            results={analysisResults!}
            fileName={uploadedFile?.name || "Unknown file"}
            onAnalyzeAgain={handleAnalyzeAgain}
          />
        )}
      </motion.div>
    </div>
  );
}
