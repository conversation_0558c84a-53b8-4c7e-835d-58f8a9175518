"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import QRScanner from "@/components/qr-checker/QRScanner";
import QRScanResult from "@/components/qr-checker/QRScanResult";
import { QRAnalysisService } from "@/services/qr.service";
import { useAuth } from "@/context/AuthContext";

export default function QRCheckerPage() {
  const { user } = useAuth();
  const [scanResult, setScanResult] = useState<string | null>(null);
  const [contentType, setContentType] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const detectContentType = (content: string): string => {
    if (content.startsWith("http://") || content.startsWith("https://")) {
      return "url";
    }
    if (content.startsWith("WIFI:")) {
      return "wifi";
    }
    if (content.startsWith("mailto:") || content.startsWith("MATMSG:")) {
      return "email";
    }
    if (content.startsWith("tel:")) {
      return "phone";
    }
    if (content.startsWith("sms:") || content.startsWith("SMSTO:")) {
      return "sms";
    }
    return "text";
  };

  const handleScan = async (result: string) => {
    if (!user) {
      toast.error("You must be logged in to scan QR codes");
      return;
    }

    setIsScanning(true);
    setError(null);

    try {
      // First, detect the content type locally
      const localContentType = detectContentType(result);
      setContentType(localContentType);

      // Then, send the QR content to the backend for analysis and tracking
      await QRAnalysisService.analyzeQR(result);

      // Set the scan result to display the QR content
      setScanResult(result);
      setIsScanning(false);

      // Show success toast
      toast.success("QR Code Scanned Successfully", {
        description: `Content type: ${localContentType.toUpperCase()}`,
        duration: 3000,
      });
    } catch (err: any) {
      console.error("Error analyzing QR code:", err);
      setIsScanning(false);

      // Display error message
      setError(
        err.response?.data?.message ||
          err.message ||
          "An error occurred while analyzing the QR code. Please try again.",
      );

      toast.error("QR Analysis Failed", {
        description:
          err.response?.data?.message ||
          err.message ||
          "Failed to analyze QR code. Please try again.",
        duration: 5000,
      });

      // Still set the scan result to display the QR content
      setScanResult(result);
      setContentType(detectContentType(result));
    }
  };

  const handleScanAgain = () => {
    setScanResult(null);
    setContentType(null);
    setError(null);
  };

  return (
    <div className="mx-auto max-w-4xl">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {!scanResult ? (
          <>
            <QRScanner
              onScan={handleScan}
              isScanning={isScanning}
              setIsScanning={setIsScanning}
            />
            {error && (
              <div className="mt-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-600 dark:border-red-900 dark:bg-red-950/50 dark:text-red-400">
                {error}
              </div>
            )}
          </>
        ) : (
          <QRScanResult
            result={scanResult}
            contentType={contentType || "text"}
            onScanAgain={handleScanAgain}
          />
        )}
      </motion.div>
    </div>
  );
}
