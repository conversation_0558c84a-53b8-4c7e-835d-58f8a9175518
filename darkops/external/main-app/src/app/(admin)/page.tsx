import type { Metadata } from "next";
import React from "react";
import { SecurityMetrics } from "@/components/security/SecurityMetrics";
import ThreatScore from "@/components/security/ThreatScore";
import DetectionDetails from "@/components/security/DetectionDetails";
import SystemTime from "@/components/security/SystemTime";
import RecentScans from "@/components/security/RecentScans";
import EmailSecurityPanel from "@/components/dashboard/EmailSecurityPanel";
import DashboardWrapper from "@/components/dashboard/DashboardWrapper";

export const metadata: Metadata = {
  title: "DarkOps Security Dashboard",
  description: "Security analysis dashboard for scanning and detecting threats",
};

export default function Dashboard() {
  return (
    <DashboardWrapper>
      <div className="grid grid-cols-12 gap-4 md:gap-6">
        {/* Security Metrics - 2 rows of 3 cards */}
        <div className="col-span-12">
          <SecurityMetrics />
        </div>

        {/* Threat Analysis Cards */}
        <div className="col-span-12 xl:col-span-4">
          <SystemTime />
        </div>

        <div className="col-span-12 xl:col-span-4">
          <DetectionDetails />
        </div>

        <div className="col-span-12 xl:col-span-4">
          <ThreatScore />
        </div>

        {/* Email Security Panel */}
        <div className="col-span-12">
          <EmailSecurityPanel />
        </div>

        {/* Recent Scans Section */}
        <div className="col-span-12">
          <RecentScans />
        </div>
      </div>
    </DashboardWrapper>
  );
}
