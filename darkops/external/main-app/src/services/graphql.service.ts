"use client";

import graphQL<PERSON>lient from "@/lib/api/graphql";
import {
  DASHBOARD_STATS_QUERY,
  SCAN_HISTORY_QUERY,
  USER_SCAN_STATS_QUERY,
  SCAN_DETAILS_QUERY,
  EXPORT_SCAN_HISTORY_MUTATION,
} from "@/graphql/queries";
import {
  DashboardStatsData,
  ScanHistoryData,
  ScanHistoryVariables,
  UserScanStatsData,
  ScanDetailsData,
  ScanDetailsVariables,
} from "@/types/graphql";

/**
 * GraphQL service for interacting with the GraphQL API
 */
export const GraphQLService = {
  /**
   * Fetch dashboard stats
   * @returns Dashboard stats data
   */
  getDashboardStats: async (): Promise<DashboardStatsData> => {
    return await graphQLClient.query<DashboardStatsData>(DASHBOARD_STATS_QUERY);
  },

  /**
   * Fetch scan history
   * @param variables Scan history variables
   * @returns Scan history data
   */
  getScanHistory: async (
    variables: ScanHistoryVariables,
  ): Promise<ScanHistoryData> => {
    return await graphQLClient.query<ScanHistoryData, ScanHistoryVariables>(
      SCAN_HISTORY_QUERY,
      variables,
    );
  },

  /**
   * Fetch user scan stats
   * @returns User scan stats data
   */
  getUserScanStats: async (): Promise<UserScanStatsData> => {
    return await graphQLClient.query<UserScanStatsData>(USER_SCAN_STATS_QUERY);
  },

  /**
   * Fetch scan details by ID
   * @param id Scan ID
   * @param scanType Scan type (APK, URL, EMAIL, SMS)
   * @returns Scan details data
   */
  getScanDetails: async (
    id: string,
    scanType: string,
  ): Promise<ScanDetailsData> => {
    const variables: ScanDetailsVariables = {
      id,
      isApkScan: scanType === "APK",
      isUrlScan: scanType === "URL",
      isEmailScan: scanType === "EMAIL",
      isSmsScan: scanType === "SMS",
    };

    return await graphQLClient.query<ScanDetailsData, ScanDetailsVariables>(
      SCAN_DETAILS_QUERY,
      variables,
    );
  },

  /**
   * Export scan history to Excel
   * @param variables Export variables
   * @returns Export response with file data and filename
   */
  exportScanHistory: async (variables: {
    scanType?: string;
    startDate?: string;
    endDate?: string;
    threatLevel?: string;
    sr?: string;
  }) => {
    return await graphQLClient.mutate<{
      exportScanHistory: {
        file: string;
        filename: string;
      };
    }>(EXPORT_SCAN_HISTORY_MUTATION, variables);
  },
};

export default GraphQLService;
