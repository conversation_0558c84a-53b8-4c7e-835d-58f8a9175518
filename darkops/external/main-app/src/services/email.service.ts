"use client";

import apiClient, { createApiClient } from "@/lib/api/axios";

/**
 * Email Analysis Response interface
 */
export interface EmailAnalysisResponse {
  headers: {
    [key: string]: string;
    From?: string;
    To?: string;
    Subject?: string;
    Date?: string;
    "Message-ID"?: string;
    "Authentication-Results"?: string;
    "Content-Type"?: string;
  };
  sender_ip?: string;
  ip_info?: {
    ipinfo?: {
      ip?: string;
      hostname?: string;
      city?: string;
      region?: string;
      country?: string;
      loc?: string;
      org?: string;
      postal?: string;
      timezone?: string;
    };
    abuseipdb?: {
      ipAddress?: string;
      isPublic?: boolean;
      ipVersion?: number;
      isWhitelisted?: boolean;
      abuseConfidenceScore?: number;
      countryCode?: string;
      usageType?: string;
      isp?: string;
      domain?: string;
      hostnames?: string[];
      isTor?: boolean;
      totalReports?: number;
      numDistinctUsers?: number;
      lastReportedAt?: string;
    };
  };
  attachments?: Array<{
    filename: string;
    content_type: string;
    size: number;
    is_malicious?: boolean;
    scan_result?: string;
  }>;
  phishing_detection?: {
    prediction?: number;
    prediction_label?: string;
    confidence?: number;
  };
  analysis_timestamp?: string;
  screenshot_url?: string;
  scanEngines?: Array<{
    name: string;
    result: string;
    confidence: number;
    risk_level: string;
    details?: string;
    updateDate?: string;
    version?: string;
  }>;
}

/**
 * Screenshot Response interface
 */
export interface ScreenshotResponse {
  screenshot_url: string;
  status: string;
  message?: string;
}

/**
 * Email Analysis Service for interacting with the email analysis API
 */
export const EmailAnalysisService = {
  /**
   * Analyze email file for phishing and other threats
   * @param file Email file (.eml) to analyze
   * @returns Analysis results
   */
  analyzeEmail: async (file: File): Promise<EmailAnalysisResponse> => {
    try {
      // Create a form data instance with multipart/form-data content type
      const formData = new FormData();
      formData.append("email_file", file);

      // Create a client with multipart/form-data content type
      // This client will automatically add the auth token from localStorage
      const multipartClient = createApiClient("multipart/form-data");

      console.log("Sending email analysis request with auth token");

      const response = await multipartClient.post<EmailAnalysisResponse>(
        "/ai/email/analyze",
        formData,
      );
      return response.data;
    } catch (error) {
      console.error("Error analyzing email:", error);
      throw error;
    }
  },

  /**
   * Capture screenshot of email content
   * @param emailId Email ID to capture screenshot for
   * @param file Email file to capture screenshot from
   * @returns Screenshot URL
   */
  captureScreenshot: async (emailId: string, file: File): Promise<string> => {
    try {
      console.log("Sending screenshot capture request for email:", emailId);

      const formData = new FormData();
      formData.append("email_file", file);

      const response = await apiClient.post(
        "/ai/email/capture-screenshot",
        formData,
        {
          responseType: "blob",
          headers: {
            Accept: "image/jpeg",
            "Content-Type": "multipart/form-data",
          },
        },
      );

      if (!response.data) {
        throw new Error("No screenshot data received");
      }

      // Create a blob URL from the response
      const blob = new Blob([response.data], { type: "image/jpeg" });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error("Error capturing screenshot:", error);
      throw error;
    }
  },
};

export default EmailAnalysisService;
