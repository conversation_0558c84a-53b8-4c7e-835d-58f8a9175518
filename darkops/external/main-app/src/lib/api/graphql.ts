"use client";

import axios from "axios";
import { graphqlClient as axiosGraphqlClient, API_URL } from "./axios";

/**
 * GraphQL client configuration
 */
export interface GraphQLClientConfig {
  url?: string;
  headers?: Record<string, string>;
}

/**
 * GraphQL request variables
 */
export interface GraphQLVariables {
  [key: string]: any;
}

/**
 * GraphQL request
 */
export interface GraphQLRequest<V = GraphQLVariables> {
  query: string;
  variables?: V;
  operationName?: string;
}

/**
 * GraphQL error
 */
export interface GraphQLError {
  message: string;
  locations?: { line: number; column: number }[];
  path?: string[];
  extensions?: Record<string, any>;
}

/**
 * GraphQL response
 */
export interface GraphQLResponse<T = any> {
  data?: T;
  errors?: GraphQLError[];
}

/**
 * Default GraphQL client configuration
 */
const defaultConfig: GraphQLClientConfig = {
  url: `${API_URL}/graphql`,
};

/**
 * GraphQL client for making GraphQL requests
 */
export const graphQLClient = {
  /**
   * Execute a GraphQL query
   * @param request GraphQL request
   * @param config GraphQL client configuration
   * @returns GraphQL response
   */
  async request<T = any, V = GraphQLVariables>(
    request: GraphQLRequest<V>,
    config: GraphQLClientConfig = {},
  ): Promise<GraphQLResponse<T>> {
    const { url = defaultConfig.url, headers = {} } = config;

    console.log("GraphQL request URL:", url);
    console.log("API_URL:", API_URL);

    if (!url) {
      throw new Error("GraphQL URL is required");
    }

    try {
      const response = await axiosGraphqlClient.post<GraphQLResponse<T>>(
        url,
        request,
        {
          headers,
          timeout: 10000, // 10 second timeout
        },
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response) {
          // Server responded with an error status code
          return error.response.data as GraphQLResponse<T>;
        } else if (error.request) {
          // Request was made but no response received (network error)
          console.error("Network error in GraphQL request:", error.message);
          return {
            errors: [
              {
                message: `Network error: ${error.message}. Please check your connection.`,
              },
            ],
          };
        }
      }

      // Handle other errors
      return {
        errors: [
          {
            message: error instanceof Error ? error.message : "Unknown error",
          },
        ],
      };
    }
  },

  /**
   * Execute a GraphQL query
   * @param query GraphQL query string
   * @param variables GraphQL variables
   * @param config GraphQL client configuration
   * @returns Query result data
   */
  async query<T = any, V = GraphQLVariables>(
    query: string,
    variables?: V,
    config?: GraphQLClientConfig,
  ): Promise<T> {
    try {
      const response = await this.request<T, V>({ query, variables }, config);

      if (response.errors && response.errors.length > 0) {
        console.error("GraphQL query errors:", response.errors);

        // Check if this is an authentication error
        const authError = response.errors.find(
          (e) =>
            e.message.includes("Authentication required") ||
            e.message.includes("Invalid authentication token"),
        );

        if (authError) {
          // Clear auth token to force re-login
          if (typeof window !== "undefined") {
            localStorage.removeItem("auth-token");
          }

          throw new Error("Authentication error. Please log in again.");
        }

        throw new Error(
          `GraphQL query error: ${response.errors.map((e) => e.message).join(", ")}`,
        );
      }

      if (!response.data) {
        console.error("GraphQL query returned no data");
        // Return empty object instead of throwing to prevent UI crashes
        return {} as T;
      }

      return response.data;
    } catch (error) {
      console.error("GraphQL query failed:", error);

      // For network errors, return empty object to prevent UI crashes
      if (axios.isAxiosError(error) && !error.response) {
        console.error("Network error in GraphQL query:", error.message);
        return {} as T;
      }

      // Re-throw other errors to be handled by the caller
      throw error;
    }
  },

  /**
   * Execute a GraphQL mutation
   * @param mutation GraphQL mutation string
   * @param variables GraphQL variables
   * @param config GraphQL client configuration
   * @returns Mutation result data
   */
  async mutate<T = any, V = GraphQLVariables>(
    mutation: string,
    variables?: V,
    config?: GraphQLClientConfig,
  ): Promise<T> {
    try {
      const response = await this.request<T, V>(
        { query: mutation, variables },
        config,
      );

      if (response.errors && response.errors.length > 0) {
        console.error("GraphQL mutation errors:", response.errors);

        // Check if this is an authentication error
        const authError = response.errors.find(
          (e) =>
            e.message.includes("Authentication required") ||
            e.message.includes("Invalid authentication token"),
        );

        if (authError) {
          // Clear auth token to force re-login
          if (typeof window !== "undefined") {
            localStorage.removeItem("auth-token");
          }

          throw new Error("Authentication error. Please log in again.");
        }

        throw new Error(
          `GraphQL mutation error: ${response.errors.map((e) => e.message).join(", ")}`,
        );
      }

      if (!response.data) {
        console.error("GraphQL mutation returned no data");
        throw new Error("GraphQL mutation returned no data");
      }

      return response.data;
    } catch (error) {
      console.error("GraphQL mutation failed:", error);

      // For network errors, provide a more specific error
      if (axios.isAxiosError(error) && !error.response) {
        throw new Error(
          `Network error: ${error.message}. Please check your connection.`,
        );
      }

      // Re-throw other errors
      throw error;
    }
  },
};

export default graphQLClient;
