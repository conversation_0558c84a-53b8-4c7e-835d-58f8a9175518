"use client";

import axios, { AxiosRequestConfig } from "axios";

// Use the correct backend URL
export const API_URL = "http://localhost:9999";

/**
 * Create an Axios client with default configuration
 * @param contentType Content type header (default: application/json)
 * @returns Axios instance
 */
// Request interceptor for adding the auth token
const addAuthToken = (config: AxiosRequestConfig) => {
  // Only run in browser environment
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("auth-token");
    if (token && config.headers) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
  }
  return config;
};

// Response interceptor for handling auth errors
const handleAuthErrors = (error: any) => {
  const originalRequest = error.config;

  // Only run in browser environment
  if (typeof window !== "undefined") {
    // If the error is 401 and hasn't been retried yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Clear auth token to prevent further failed requests
      localStorage.removeItem("auth-token");

      // Don't redirect automatically - let the AuthRedirectHandler handle it
      // This prevents multiple redirects and flashing
    }
  }

  return Promise.reject(error);
};

export const createApiClient = (contentType: string = "application/json") => {
  console.log("Creating API client with baseURL:", API_URL);

  const client = axios.create({
    baseURL: API_URL,
    headers: {
      "Content-Type": contentType,
    },
  });

  // Add request logging
  client.interceptors.request.use(
    (config) => {
      console.log(
        `Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`,
      );
      return config;
    },
    (error) => {
      console.error("Request error:", error);
      return Promise.reject(error);
    },
  );

  // Add auth token to all requests for this client
  client.interceptors.request.use(addAuthToken, (error) =>
    Promise.reject(error),
  );

  // Add auth error handling to all responses for this client
  client.interceptors.response.use((response) => response, handleAuthErrors);

  return client;
};

// Create default API client with JSON content type
const apiClient = createApiClient();

// Create GraphQL API client
export const graphqlClient = createApiClient("application/json");

export default apiClient;
