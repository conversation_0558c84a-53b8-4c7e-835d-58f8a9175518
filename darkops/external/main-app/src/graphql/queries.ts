/**
 * GraphQL query to fetch dashboard stats
 */
export const DASHBOARD_STATS_QUERY = `
  query DashboardData {
    dashboardStats {
      # Total Scans and Scan Counts by Type
      totalScans
      totalScansPercentageChange
      scansByType {
        type
        count
        percentageChange
        previousWeekCount
      }

      # Threat Score Card Data
      threatScore {
        score
        level
        percentageChange
        previousScore
      }

      # Last APK Scan for Detection Details Card
      lastApkScan {
        id
        scanType
        target
        SR
        createdAt
        result {
          threatScore
          threatLevel
          confidence
          findings {
            type
            severity
            description
          }
          scanEngines {
            name
            result
            confidence
            risk_level
          }
          apkInfo {
            packageName
            fileName
            fileSize
            sha256
          }
          staticAnalysis {
            malwareScore
            riskScore
            behavioralTags
          }
        }
      }

      # Recent Scans (Last 10)
      recentScans {
        id
        scanType
        target
        SR
        createdAt
        result {
          threatScore
          threatLevel
          confidence
        }
      }
    }
  }
`;

/**
 * GraphQL query to fetch scan history with enhanced fields and filtering
 */
export const SCAN_HISTORY_QUERY = `
  query ScanHistory(
    $page: Int,
    $limit: Int,
    $scanType: String,
    $startDate: String,
    $endDate: String,
    $threatLevel: String,
    $sr: String,
    $sortField: String,
    $sortOrder: String,
    $isApkScan: Boolean!,
    $isUrlScan: Boolean!,
    $isEmailScan: Boolean!,
    $isSmsScan: Boolean!
  ) {
    scanHistory(
      page: $page,
      limit: $limit,
      scanType: $scanType,
      startDate: $startDate,
      endDate: $endDate,
      threatLevel: $threatLevel,
      sr: $sr,
      sortField: $sortField,
      sortOrder: $sortOrder
    ) {
      scans {
        id
        userId
        scanType
        target
        SR
        createdAt
        result {
          threatScore
          threatLevel
          confidence
          findings {
            type
            severity
            description
            details
          }
          scanEngines {
            name
            result
            confidence
            version
            updateDate
            risk_level
            details
          }

          # APK-specific fields
          apkInfo @include(if: $isApkScan) {
            packageName
            fileName
            fileSize
            sha256
            sha1
            md5
            firstSubmission
            lastAnalysis
          }
          staticAnalysis @include(if: $isApkScan) {
            activities
            services
            permissions {
              name
              severity
              description
            }
            receivers
            providers
            malwareScore
            riskScore
            behavioralTags
            dnsLookups
            httpRequests
            filesWritten
            filesDeleted
            mitreTechniques {
              id
              name
              description
            }
          }

          # URL-specific fields
          urlInfo @include(if: $isUrlScan) {
            url
            domain
            ip
            country
            server
            finalUrl
            scanId
            scanResultUrl
            screenshotUrl
            scanTime
            phishingAnalysis {
              prediction
              confidence
            }
          }
          securityInfo @include(if: $isUrlScan) {
            malicious
            score
            categories
            brands
            riskLevel
            domSecurity {
              vulnerableJsLibs
              externalScripts
              forms
              passwordFields
              suspiciousElements
            }
            certificates {
              subjectName
              issuer
              validFrom
              validTo
            }
            mixedContent
            vulnerableLibraries
            suspiciousRedirects
            insecureCookies
            reputation {
              domainAge
              sslValidity
              blacklistStatus
            }
          }

          # EMAIL-specific fields
          emailInfo @include(if: $isEmailScan) {
            subject
            fromEmail
            fromName
            date
            isBcc
            contentType
            replyTo
            cc
            bcc
            receivedDate
            authenticationResults {
              spf {
                result
                domain
              }
              dkim {
                result
                domain
              }
              dmarc {
                result
                domain
              }
            }
          }
          senderInfo @include(if: $isEmailScan) {
            ip
            hostname
            domain
            riskLevel
            org
            isp
            abuseConfidenceScore
            isTor
            isWhitelisted
          }
          attachments @include(if: $isEmailScan) {
            filename
            contentType
            size
            sha256
            isMalicious
            threatType
            riskLevel
            detectionCount
            detectionNames
          }

          # SMS-specific fields
          smsInfo @include(if: $isSmsScan) {
            message
            sender
            timestamp
            analysisTimestamp
            isPhishing
            confidence
            riskLevel
            messageType
            recipient
          }
          contentAnalysis @include(if: $isSmsScan) {
            riskLevel
            suspiciousKeywords
            isPhishing
            confidence
            scanEnginesCount
            riskIndicators
            contentCategories
            language
            sentiment
            threatTypes
            spamScore
            messageIntent
          }
          smsUrlInfo @include(if: $isSmsScan) {
            urls
            shortenedUrls
            maliciousUrlsCount
            suspiciousUrlsCount
            cleanUrlsCount
            urlAnalysis
          }
        }
      }
      pagination {
        total
        page
        limit
        totalPages
      }
    }
  }
`;

/**
 * GraphQL query to fetch user scan stats
 */
export const USER_SCAN_STATS_QUERY = `
  query UserScanStats {
    userScanStats {
      totalScans
      scansByType {
        APK
        URL
        EMAIL
        SMS
        QR
      }
      dailyScans {
        date
        count
        scanType
      }
      monthlyScans {
        year
        month
        count
        scanType
      }
      scanEngineUsage {
        name
        count
        scanType
        detectionCount
        lastUsed
      }
      lastScanAt
    }
  }
`;

/**
 * GraphQL query to fetch a specific scan by ID
 */
export const SCAN_DETAILS_QUERY = `
  query ScanDetails($id: ID!, $isApkScan: Boolean!, $isUrlScan: Boolean!, $isEmailScan: Boolean!, $isSmsScan: Boolean!) {
    scanDetails(id: $id) {
      id
      userId
      scanType
      target
      SR
      createdAt
      result {
        threatScore
        threatLevel
        confidence
        findings {
          type
          severity
          description
          details
        }
        scanEngines {
          name
          result
          confidence
          version
          updateDate
          risk_level
          details
        }

        # APK-specific fields
        apkInfo @include(if: $isApkScan) {
          packageName
          fileName
          fileSize
          sha256
          sha1
          md5
          firstSubmission
          lastAnalysis
        }
        staticAnalysis @include(if: $isApkScan) {
          activities
          services
          permissions {
            name
            severity
            description
          }
          receivers
          providers
          malwareScore
          riskScore
          behavioralTags
          dnsLookups
          httpRequests
          filesWritten
          filesDeleted
          mitreTechniques {
            id
            name
            description
          }
        }

        # URL-specific fields
        urlInfo @include(if: $isUrlScan) {
          url
          domain
          ip
          country
          server
          finalUrl
          scanId
          scanResultUrl
          screenshotUrl
          scanTime
          phishingAnalysis {
            prediction
            confidence
          }
        }
        securityInfo @include(if: $isUrlScan) {
          malicious
          score
          categories
          brands
          riskLevel
          domSecurity {
            vulnerableJsLibs
            externalScripts
            forms
            passwordFields
            suspiciousElements
          }
          certificates {
            subjectName
            issuer
            validFrom
            validTo
          }
          mixedContent
          vulnerableLibraries
          suspiciousRedirects
          insecureCookies
          reputation {
            domainAge
            sslValidity
            blacklistStatus
          }
        }

        # EMAIL-specific fields
        emailInfo @include(if: $isEmailScan) {
          subject
          fromEmail
          fromName
          date
          isBcc
          contentType
          replyTo
          cc
          bcc
          receivedDate
          authenticationResults {
            spf {
              result
              domain
            }
            dkim {
              result
              domain
            }
            dmarc {
              result
              domain
            }
          }
        }
        senderInfo @include(if: $isEmailScan) {
          ip
          hostname
          domain
          riskLevel
          org
          isp
          abuseConfidenceScore
          isTor
          isWhitelisted
        }
        attachments @include(if: $isEmailScan) {
          filename
          contentType
          size
          sha256
          isMalicious
          threatType
          riskLevel
          detectionCount
          detectionNames
        }

        # SMS-specific fields
        smsInfo @include(if: $isSmsScan) {
          message
          sender
          timestamp
          analysisTimestamp
          isPhishing
          confidence
          riskLevel
          messageType
          recipient
        }
        contentAnalysis @include(if: $isSmsScan) {
          riskLevel
          suspiciousKeywords
          isPhishing
          confidence
          scanEnginesCount
          riskIndicators
          contentCategories
          language
          sentiment
          threatTypes
          spamScore
          messageIntent
        }
        smsUrlInfo @include(if: $isSmsScan) {
          urls
          shortenedUrls
          maliciousUrlsCount
          suspiciousUrlsCount
          cleanUrlsCount
          urlAnalysis
        }
      }
    }
  }
`;

/**
 * GraphQL mutation to export scan history to Excel
 */
export const EXPORT_SCAN_HISTORY_MUTATION = `
  mutation ExportScanHistory(
    $scanType: String,
    $startDate: String,
    $endDate: String,
    $threatLevel: String,
    $sr: String
  ) {
    exportScanHistory(
      scanType: $scanType,
      startDate: $startDate,
      endDate: $endDate,
      threatLevel: $threatLevel,
      sr: $sr
    ) {
      file
      filename
    }
  }
`;
