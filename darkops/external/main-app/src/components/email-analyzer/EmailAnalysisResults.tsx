"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { EmailAnalysisResponse } from "@/services/email.service";

interface EmailAnalysisResultsProps {
  results: EmailAnalysisResponse;
  fileName: string;
  onAnalyzeAgain: () => void;
}

export default function EmailAnalysisResults({
  results,
  fileName,
  onAnalyzeAgain,
}: EmailAnalysisResultsProps) {
  const [activeTab, setActiveTab] = useState<
    "overview" | "headers" | "technical" | "screenshot"
  >("overview");

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const getPhishingStatusColor = (isPhishing: boolean) => {
    return isPhishing
      ? "bg-status-danger text-white"
      : "bg-status-safe text-white";
  };

  const getPhishingStatusText = (isPhishing: boolean) => {
    return isPhishing ? "PHISHING" : "LEGIT";
  };

  const getPhishingResultText = (isPhishing: boolean) => {
    return isPhishing
      ? "This email appears to be a phishing attempt"
      : "This email appears to be legitimate and safe";
  };

  const getAuthStatusColor = (status: string) => {
    if (status.toLowerCase().includes("pass")) return "text-status-safe";
    if (status.toLowerCase().includes("fail")) return "text-status-danger";
    return "text-gray-500";
  };

  const getAbuseScoreColor = (score: number) => {
    if (score < 20) return "text-status-safe";
    if (score < 50) return "text-status-warning";
    return "text-status-danger";
  };

  // Determine if the email is phishing based on prediction_label or prediction
  const isPhishing =
    results.phishing_detection?.prediction_label?.toLowerCase() ===
      "phishing" || results.phishing_detection?.prediction === 1;

  // Calculate confidence percentage
  const confidence = results.phishing_detection?.confidence
    ? results.phishing_detection.confidence > 1
      ? results.phishing_detection.confidence.toFixed(2)
      : (results.phishing_detection.confidence * 100).toFixed(2)
    : "0.00";

  // Extract authentication results
  const spfStatus =
    results.headers &&
    results.headers["Authentication-Results"]?.includes("spf=pass")
      ? "OK"
      : "FAIL";

  const dkimStatus =
    results.headers &&
    results.headers["Authentication-Results"]?.includes("dkim=pass")
      ? "PASSED"
      : "FAILED";

  // Check if scan engines are available
  const hasScanEngines = results.scanEngines && results.scanEngines.length > 0;

  return (
    <div className="rounded-2xl border border-gray-200 bg-gradient-to-b from-blue-50/70 to-white p-6 dark:border-gray-700 dark:from-blue-950/10 dark:to-gray-900">
      {/* Back Navigation and Timestamp */}
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={onAnalyzeAgain}
          className="flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-1"
          >
            <path d="m12 19-7-7 7-7" />
            <path d="M19 12H5" />
          </svg>
          Analyze Another Email
        </button>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Analysis Time: {formatTimestamp(results.analysis_timestamp)}
        </div>
      </div>

      {/* Phishing Detection */}
      <div className="mb-6">
        <div className="mb-2 flex items-center gap-3">
          <span
            className={`rounded-md px-2 py-1 text-xs font-bold ${getPhishingStatusColor(
              isPhishing,
            )}`}
          >
            {getPhishingStatusText(isPhishing)}
          </span>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            {getPhishingResultText(isPhishing)}
          </h2>
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Confidence: {confidence}%
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6 flex border-b border-gray-200 dark:border-gray-700">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === "overview"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          }`}
          onClick={() => setActiveTab("overview")}
        >
          Overview
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === "screenshot"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          }`}
          onClick={() => setActiveTab("screenshot")}
        >
          Screenshot
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === "headers"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          }`}
          onClick={() => setActiveTab("headers")}
        >
          Headers
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === "technical"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          }`}
          onClick={() => setActiveTab("technical")}
        >
          Technical
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {/* Email Information */}
          <div className="mb-6">
            <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Email Information
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800 ">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  From
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.headers.From || "Unknown"}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  To
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.headers.To || "Unknown"}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Subject
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.headers.Subject || "No Subject"}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Date
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.headers.Date || "Unknown"}
                </p>
              </div>
            </div>
          </div>

          {/* Sender Information */}
          <div className="mb-6">
            <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Sender Information
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  IP Address
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.sender_ip || "Unknown"}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Location
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.ip_info?.ipinfo
                    ? `${results.ip_info.ipinfo.city}, ${results.ip_info.ipinfo.region}, ${results.ip_info.ipinfo.country}`
                    : "Unknown"}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  ISP
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.ip_info?.ipinfo?.org || "Unknown"}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Abuse Confidence Score
                </p>
                <p
                  className={`text-base font-medium ${getAbuseScoreColor(
                    results.ip_info?.abuseipdb?.abuseConfidenceScore || 0,
                  )}`}
                >
                  {results.ip_info?.abuseipdb?.abuseConfidenceScore || 0}%
                </p>
              </div>
            </div>
          </div>

          {/* Security Information */}
          <div className="mb-6">
            <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Security Information
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  SPF
                </p>
                <p
                  className={`text-base font-medium ${getAuthStatusColor(spfStatus)}`}
                >
                  {spfStatus}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  DKIM
                </p>
                <p
                  className={`text-base font-medium ${getAuthStatusColor(dkimStatus)}`}
                >
                  {dkimStatus}
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Attachments
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  {results.attachments?.length || 0}
                </p>
              </div>
            </div>
          </div>

          {/* Scan Engines */}
          {hasScanEngines && (
            <div className="mb-6">
              <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
                Scan Engines
              </h3>
              <div className="space-y-4">
                {results.scanEngines?.map((engine, index) => (
                  <div
                    key={index}
                    className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {engine.name}
                      </h5>
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                          engine.result.toLowerCase() === "phishing" ||
                          engine.result.toLowerCase() === "malicious"
                            ? "bg-status-danger/10 text-status-danger"
                            : "bg-status-safe/10 text-status-safe"
                        }`}
                      >
                        {engine.result}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Confidence:
                        </span>{" "}
                        <span className="font-medium text-gray-900 dark:text-white">
                          {typeof engine.confidence === "number"
                            ? engine.confidence > 1
                              ? engine.confidence.toFixed(2) + "%"
                              : (engine.confidence * 100).toFixed(2) + "%"
                            : engine.confidence}
                        </span>
                      </div>
                      {engine.risk_level && (
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">
                            Risk Level:
                          </span>{" "}
                          <span
                            className={`font-medium ${getAuthStatusColor(engine.risk_level)}`}
                          >
                            {engine.risk_level}
                          </span>
                        </div>
                      )}
                      {engine.details && (
                        <div className="col-span-2">
                          <span className="text-gray-500 dark:text-gray-400">
                            Details:
                          </span>{" "}
                          <span className="text-gray-900 dark:text-white">
                            {engine.details}
                          </span>
                        </div>
                      )}
                      {engine.version && (
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">
                            Version:
                          </span>{" "}
                          <span className="text-gray-900 dark:text-white">
                            {engine.version}
                          </span>
                        </div>
                      )}
                      {engine.updateDate && (
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">
                            Updated:
                          </span>{" "}
                          <span className="text-gray-900 dark:text-white">
                            {new Date(engine.updateDate).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}

      {activeTab === "headers" && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            Email Headers
          </h3>
          <div className="overflow-x-auto rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900">
            <pre className="whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-300">
              {Object.entries(results.headers)
                .map(([key, value]) => `${key}: ${value}`)
                .join("\n")}
            </pre>
          </div>
        </motion.div>
      )}

      {activeTab === "technical" && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            Technical Details
          </h3>
          <div className="overflow-x-auto rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900">
            <pre className="whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-300">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        </motion.div>
      )}

      {activeTab === "screenshot" && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            Email Screenshot
          </h3>
          <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <div className="flex flex-col items-center">
              <div className="relative mx-auto w-full max-w-2xl overflow-hidden rounded-lg border border-gray-300 shadow-md dark:border-gray-600">
                <img
                  src={
                    results.screenshot_url ||
                    "https://placehold.co/800x600/e2e8f0/64748b?text=Email+Content+Preview"
                  }
                  alt="Email Preview"
                  className="h-auto w-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    if (target.src.startsWith("blob:")) {
                      URL.revokeObjectURL(target.src);
                    }
                    target.src =
                      "https://placehold.co/800x600/e2e8f0/64748b?text=Email+Preview+Not+Available";
                  }}
                />
              </div>
              <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                This is a screenshot of how the email appears to recipients.
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
