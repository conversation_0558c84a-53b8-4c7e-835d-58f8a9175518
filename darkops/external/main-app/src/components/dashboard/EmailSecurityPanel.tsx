"use client";

import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  InfoCircledIcon,
  ExclamationTriangleIcon,
  CheckCircledIcon,
  EnvelopeClosedIcon,
} from "@radix-ui/react-icons";
import { useToast } from "@/components/ui/use-toast";
import apiClient, { API_URL } from "@/lib/api/axios";
import axios from "axios";
import { formatDistanceToNow } from "date-fns";
import { AuthService } from "@/services/auth.service";

// Types
interface EmailAnalysisResult {
  emailId: string;
  gmailThreadId: string;
  snippet: string;
  headers: {
    from: string | null;
    subject: string | null;
    date: string | null;
  };
  result: {
    threatScore: number;
    threatLevel: string;
    confidence: number;
  };
  SR: string;
  status: string;
  createdAt: string;
  processedAt: string | null;
}

interface EmailAnalysisResultsResponse {
  results: EmailAnalysisResult[];
  total: number;
}

interface EmailDetails {
  id: string;
  threadId: string;
  snippet: string;
  subject: string | null;
  from: string | null;
  date: string | null;
}

// GraphQL queries
const EMAIL_ANALYSIS_RESULTS_QUERY = `
  query GetEmailAnalysisResults($limit: Int, $offset: Int, $status: String, $threatLevel: String) {
    emailAnalysisResults(limit: $limit, offset: $offset, status: $status, threatLevel: $threatLevel) {
      results {
        emailId
        gmailThreadId
        snippet
        headers {
          from
          subject
          date
        }
        result {
          threatScore
          threatLevel
          confidence
        }
        SR
        status
        createdAt
        processedAt
      }
      total
    }
  }
`;

// API functions
const fetchRecentEmails = async (): Promise<EmailDetails[]> => {
  try {
    // First check if user has Google account connected
    const googleStatus = await AuthService.checkGoogleConnection();
    if (!googleStatus.isConnected) {
      console.log("Google account not connected, skipping email fetch");
      return [];
    }

    console.log("Fetching emails from:", `${API_URL}/email/recent`);
    // Add timeout to prevent long-running requests
    const response = await apiClient.get<{
      message: string;
      emails: EmailDetails[];
    }>("/email/recent", {
      timeout: 10000, // 10 second timeout
      baseURL: API_URL, // Explicitly set the base URL
    });

    if (response.data && Array.isArray(response.data.emails)) {
      return response.data.emails;
    } else {
      console.error("Invalid response format:", response.data);
      return [];
    }
  } catch (error) {
    console.error("Error fetching recent emails:", error);

    // Show more specific error message in console
    if (axios.isAxiosError(error)) {
      if (error.code === "ECONNABORTED") {
        console.error("Request timeout fetching emails");
      } else if (!error.response) {
        console.error("Network error fetching emails");
      } else if (
        error.response.status === 401 &&
        error.response.data?.message === "Google account not connected"
      ) {
        console.log("User doesn't have Gmail connected");
        // This is an expected error when user doesn't have Gmail, so we just return empty array
        return [];
      } else {
        console.error(
          `Server error (${error.response.status}) fetching emails`,
        );
      }
    }

    // Return empty array to prevent UI errors
    return [];
  }
};

const analyzeEmails = async (
  emailIds: string[],
): Promise<{ message: string; queuedEmails: string[] }> => {
  try {
    // First check if user has Google account connected
    const googleStatus = await AuthService.checkGoogleConnection();
    if (!googleStatus.isConnected) {
      throw new Error(
        "Gmail account not connected. Please connect your Gmail account to analyze emails.",
      );
    }

    console.log("Analyzing emails at:", `${API_URL}/email/analyze`);
    const response = await apiClient.post<{
      message: string;
      queuedEmails: string[];
    }>(
      "/email/analyze",
      { emailIds },
      {
        timeout: 15000, // 15 second timeout
        baseURL: API_URL, // Explicitly set the base URL
      },
    );

    if (response.data && response.data.queuedEmails) {
      return response.data;
    } else {
      console.error("Invalid response format:", response.data);
      throw new Error("Invalid response format from server");
    }
  } catch (error) {
    console.error("Error analyzing emails:", error);

    // Provide more specific error messages
    if (axios.isAxiosError(error)) {
      if (error.code === "ECONNABORTED") {
        throw new Error(
          "Request timed out. The server is taking too long to respond.",
        );
      } else if (!error.response) {
        throw new Error(
          "Network error. Please check your connection and try again.",
        );
      } else if (error.response.status === 401) {
        if (error.response.data?.message === "Google account not connected") {
          throw new Error(
            "Gmail account not connected. Please connect your Gmail account to analyze emails.",
          );
        } else {
          throw new Error("Authentication error. Please log in again.");
        }
      } else {
        throw new Error(
          `Server error (${error.response.status}): ${error.response.data?.message || "Unknown error"}`,
        );
      }
    }

    throw error;
  }
};

const fetchEmailAnalysisResults = async (variables: {
  limit: number;
  offset: number;
  status?: string;
  threatLevel?: string;
}): Promise<EmailAnalysisResultsResponse> => {
  try {
    // First check if user has Google account connected
    const googleStatus = await AuthService.checkGoogleConnection();
    if (!googleStatus.isConnected) {
      console.log(
        "Google account not connected, skipping analysis results fetch",
      );
      return { results: [], total: 0 };
    }

    // Use the GraphQL client instead of direct API calls
    const graphQLClient = await import("@/lib/api/graphql").then(
      (module) => module.default,
    );

    console.log("Fetching email analysis results from:", `${API_URL}/graphql`);

    const data = await graphQLClient.query(
      EMAIL_ANALYSIS_RESULTS_QUERY,
      variables,
      { url: `${API_URL}/graphql` }, // Explicitly set the GraphQL endpoint
    );

    if (data && data.emailAnalysisResults) {
      return data.emailAnalysisResults;
    } else {
      console.error("Invalid response format:", data);
      // Return default structure to prevent UI errors
      return { results: [], total: 0 };
    }
  } catch (error) {
    console.error("Error fetching email analysis results:", error);
    // Return default structure to prevent UI errors
    return { results: [], total: 0 };
  }
};

// Component
export default function EmailSecurityPanel() {
  const { toast } = useToast();
  const [selectedTab, setSelectedTab] = useState("inbox");
  const [page, setPage] = useState(1);
  const limit = 5;

  // Check if user has Google account connected
  const {
    data: googleConnectionStatus,
    isLoading: isCheckingGoogleConnection,
    error: googleConnectionError,
  } = useQuery({
    queryKey: ["googleConnection"],
    queryFn: AuthService.checkGoogleConnection,
    retry: 1,
    retryDelay: 1000,
    onError: (error) => {
      console.error("Error checking Google connection:", error);
    },
    refetchOnWindowFocus: false,
  });

  // Fetch recent emails only if Google account is connected
  const {
    data: recentEmails,
    isLoading: isLoadingEmails,
    error: emailsError,
    refetch: refetchEmails,
  } = useQuery({
    queryKey: ["recentEmails"],
    queryFn: fetchRecentEmails,
    retry: 1,
    retryDelay: 1000,
    onError: (error) => {
      console.error("Error fetching recent emails:", error);
    },
    refetchOnWindowFocus: false,
    enabled: googleConnectionStatus?.isConnected === true,
  });

  // Fetch email analysis results only if Google account is connected
  const {
    data: analysisResults,
    isLoading: isLoadingAnalysis,
    error: analysisError,
    refetch: refetchAnalysis,
  } = useQuery({
    queryKey: ["emailAnalysisResults", page, limit],
    queryFn: () =>
      fetchEmailAnalysisResults({ limit, offset: (page - 1) * limit }),
    retry: 1,
    retryDelay: 1000,
    onError: (error) => {
      console.error("Error fetching email analysis results:", error);
    },
    refetchOnWindowFocus: false,
    enabled: googleConnectionStatus?.isConnected === true,
  });

  // Analyze emails mutation
  const { mutate: analyzeEmailsMutation, isPending: isAnalyzing } = useMutation(
    {
      mutationFn: analyzeEmails,
      retry: 1,
      onSuccess: (data) => {
        toast({
          title: "Emails queued for analysis",
          description: `${data.queuedEmails.length} emails have been queued for analysis.`,
          variant: "default",
        });
        // Refetch analysis results after a delay to allow processing
        setTimeout(() => {
          refetchAnalysis();
        }, 5000);
      },
      onError: (error) => {
        console.error("Error analyzing emails:", error);

        // Handle connection errors gracefully
        const errorMessage =
          error instanceof Error
            ? error.message.includes("Network Error")
              ? "Cannot connect to the server. Please check your connection and try again."
              : error.message
            : "An unknown error occurred";

        toast({
          title: "Error analyzing emails",
          description: errorMessage,
          variant: "destructive",
        });
      },
    },
  );

  // Handle analyze all emails
  const handleAnalyzeAll = () => {
    if (recentEmails && recentEmails.length > 0) {
      analyzeEmailsMutation(recentEmails.map((email) => email.id));
    }
  };

  // Get threat level badge
  const getThreatLevelBadge = (threatLevel: string) => {
    switch (threatLevel) {
      case "CRITICAL":
        return <Badge variant="destructive">Critical</Badge>;
      case "HIGH":
        return <Badge variant="destructive">High</Badge>;
      case "MEDIUM":
        return <Badge variant="warning">Medium</Badge>;
      case "LOW":
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return <Badge variant="success">Completed</Badge>;
      case "PENDING":
        return <Badge variant="outline">Pending</Badge>;
      case "PROCESSING":
        return <Badge variant="secondary">Processing</Badge>;
      case "FAILED":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <Card className="h-full overflow-hidden rounded-xl border border-gray-100 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
      <CardHeader className="border-b border-gray-100 bg-white px-6 py-5 dark:border-gray-800 dark:bg-gray-900">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
              Email Security
            </CardTitle>
            <CardDescription className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Analyze your Gmail inbox for phishing attempts and security
              threats
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs
          defaultValue="inbox"
          value={selectedTab}
          onValueChange={setSelectedTab}
        >
          <TabsList className="mb-0 flex border-b border-gray-100 px-6 dark:border-gray-800">
            <TabsTrigger
              value="inbox"
              className="data-[state=active]:border-brand-500 data-[state=active]:text-brand-600 dark:data-[state=active]:border-brand-400 dark:data-[state=active]:text-brand-400 relative rounded-none border-b-2 border-transparent px-4 py-3 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              Inbox
            </TabsTrigger>
            <TabsTrigger
              value="analysis"
              className="data-[state=active]:border-brand-500 data-[state=active]:text-brand-600 dark:data-[state=active]:border-brand-400 dark:data-[state=active]:text-brand-400 relative rounded-none border-b-2 border-transparent px-4 py-3 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              Analysis Results
            </TabsTrigger>
          </TabsList>

          <TabsContent value="inbox" className="px-6 py-4">
            {isCheckingGoogleConnection ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex flex-col space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                ))}
              </div>
            ) : googleConnectionStatus?.isConnected === false ? (
              <Alert className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950">
                <EnvelopeClosedIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                <AlertTitle className="text-amber-800 dark:text-amber-300">
                  Gmail Account Not Connected
                </AlertTitle>
                <AlertDescription className="text-amber-700 dark:text-amber-400">
                  <p className="mb-2">
                    You are not signed in with a Gmail account, so we don't have
                    permission to read your emails.
                  </p>
                  <p className="mb-4">
                    To use the Email Security features, please connect your
                    Gmail account.
                  </p>
                  <Button
                    onClick={AuthService.googleAuth}
                    className="bg-amber-600 text-white hover:bg-amber-700"
                  >
                    Connect Gmail Account
                  </Button>
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <div className="mb-4 flex justify-between">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Recent Emails
                  </h3>
                  <Button
                    onClick={handleAnalyzeAll}
                    disabled={
                      isAnalyzing || !recentEmails || recentEmails.length === 0
                    }
                    className="bg-brand-600 hover:bg-brand-700 focus-visible:ring-brand-500 dark:bg-brand-600 dark:hover:bg-brand-700 dark:focus-visible:ring-brand-500 inline-flex h-9 items-center justify-center rounded-md px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                  >
                    {isAnalyzing ? "Analyzing..." : "Analyze All"}
                  </Button>
                </div>

                {isLoadingEmails ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex flex-col space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-3 w-1/4" />
                      </div>
                    ))}
                  </div>
                ) : emailsError ? (
                  <Alert variant="destructive">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>
                      Failed to load emails. Please try again later.
                    </AlertDescription>
                  </Alert>
                ) : recentEmails && recentEmails.length > 0 ? (
                  <div className="space-y-4">
                    {recentEmails.map((email) => (
                      <div
                        key={email.id}
                        className="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-colors hover:bg-gray-50 dark:border-gray-800 dark:bg-gray-900 dark:hover:bg-gray-800/50"
                      >
                        <div className="flex justify-between">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {email.from}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {email.date
                              ? formatDistanceToNow(new Date(email.date), {
                                  addSuffix: true,
                                })
                              : "Unknown date"}
                          </div>
                        </div>
                        <div className="mt-1 font-medium text-gray-800 dark:text-gray-200">
                          {email.subject}
                        </div>
                        <div className="mt-1 line-clamp-2 text-sm text-gray-600 dark:text-gray-400">
                          {email.snippet}
                        </div>
                        <div className="mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => analyzeEmailsMutation([email.id])}
                            disabled={isAnalyzing}
                            className="h-8 rounded-md border-gray-200 bg-white px-3 text-xs font-medium text-gray-800 hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                          >
                            Analyze
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Alert>
                    <InfoCircledIcon className="h-4 w-4" />
                    <AlertTitle>No emails found</AlertTitle>
                    <AlertDescription>
                      No recent emails were found in your inbox.
                    </AlertDescription>
                  </Alert>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="analysis" className="px-6 py-4">
            {isCheckingGoogleConnection ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex flex-col space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                ))}
              </div>
            ) : googleConnectionStatus?.isConnected === false ? (
              <Alert className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950">
                <EnvelopeClosedIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                <AlertTitle className="text-amber-800 dark:text-amber-300">
                  Gmail Account Not Connected
                </AlertTitle>
                <AlertDescription className="text-amber-700 dark:text-amber-400">
                  <p className="mb-2">
                    You are not signed in with a Gmail account, so we don't have
                    permission to analyze your emails.
                  </p>
                  <p className="mb-4">
                    To use the Email Security features, please connect your
                    Gmail account.
                  </p>
                  <Button
                    onClick={AuthService.googleAuth}
                    className="bg-amber-600 text-white hover:bg-amber-700"
                  >
                    Connect Gmail Account
                  </Button>
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <div className="mb-4 flex justify-between">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Analysis Results
                  </h3>
                  <Button
                    variant="outline"
                    onClick={() => refetchAnalysis()}
                    className="h-8 rounded-md border-gray-200 bg-white px-3 text-xs font-medium text-gray-800 hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                  >
                    Refresh
                  </Button>
                </div>

                {isLoadingAnalysis ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex flex-col space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-3 w-1/4" />
                      </div>
                    ))}
                  </div>
                ) : analysisError ? (
                  <Alert variant="destructive">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>
                      Failed to load analysis results. Please try again later.
                    </AlertDescription>
                  </Alert>
                ) : analysisResults && analysisResults.results.length > 0 ? (
                  <div className="space-y-4">
                    {analysisResults.results.map((result) => (
                      <div
                        key={result.emailId}
                        className="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-colors hover:bg-gray-50 dark:border-gray-800 dark:bg-gray-900 dark:hover:bg-gray-800/50"
                      >
                        <div className="flex justify-between">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {result.headers.from}
                          </div>
                          <div className="flex space-x-2">
                            {getStatusBadge(result.status)}
                            {result.status === "SUCCESS" &&
                              getThreatLevelBadge(result.result.threatLevel)}
                          </div>
                        </div>
                        <div className="mt-1 font-medium text-gray-800 dark:text-gray-200">
                          {result.headers.subject}
                        </div>
                        <div className="mt-1 line-clamp-2 text-sm text-gray-600 dark:text-gray-400">
                          {result.snippet}
                        </div>
                        {result.status === "SUCCESS" && (
                          <div className="mt-3 flex items-center space-x-4">
                            <div className="flex items-center">
                              <div className="text-sm text-gray-700 dark:text-gray-300">
                                Threat Score:{" "}
                                <span className="font-semibold text-gray-900 dark:text-white">
                                  {result.result.threatScore}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <div className="text-sm text-gray-700 dark:text-gray-300">
                                Confidence:{" "}
                                <span className="font-semibold text-gray-900 dark:text-white">
                                  {Math.round(result.result.confidence * 100)}%
                                </span>
                              </div>
                            </div>
                          </div>
                        )}
                        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                          {result.processedAt
                            ? `Analyzed ${formatDistanceToNow(new Date(result.processedAt), { addSuffix: true })}`
                            : `Created ${formatDistanceToNow(new Date(result.createdAt), { addSuffix: true })}`}
                        </div>
                      </div>
                    ))}

                    {/* Pagination */}
                    {analysisResults.total > limit && (
                      <div className="mt-6 flex justify-center">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPage((p) => Math.max(1, p - 1))}
                            disabled={page === 1}
                            className="h-8 rounded-md border-gray-200 bg-white px-3 text-xs font-medium text-gray-800 hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                          >
                            Previous
                          </Button>
                          <div className="flex items-center px-2 text-sm text-gray-600 dark:text-gray-400">
                            Page {page} of{" "}
                            {Math.ceil(analysisResults.total / limit)}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPage((p) => p + 1)}
                            disabled={
                              page >= Math.ceil(analysisResults.total / limit)
                            }
                            className="h-8 rounded-md border-gray-200 bg-white px-3 text-xs font-medium text-gray-800 hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Alert>
                    <InfoCircledIcon className="h-4 w-4" />
                    <AlertTitle>No analysis results</AlertTitle>
                    <AlertDescription>
                      No email analysis results were found. Analyze some emails
                      to see results here.
                    </AlertDescription>
                  </Alert>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
