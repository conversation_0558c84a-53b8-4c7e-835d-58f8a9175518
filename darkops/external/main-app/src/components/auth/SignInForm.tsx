"use client";
import Label from "@/components/form/Label";
import { ChevronLeftIcon, EyeCloseIcon, EyeIcon } from "@/icons";
import Link from "next/link";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLogin, useGoogleAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";

// Animated Input Component
const AnimatedInput = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & { className?: string }
>(({ className = "", ...props }, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <input
        ref={ref}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`w-full rounded-lg border px-4 py-3 font-poppins text-sm text-gray-700 outline-none transition-all duration-300 dark:bg-gray-800/50 dark:text-white/90 ${
          isFocused
            ? "border-brand-500 shadow-[0_0_0_4px_rgba(99,102,241,0.1)] dark:shadow-[0_0_0_4px_rgba(99,102,241,0.2)]"
            : "border-gray-200 dark:border-gray-700"
        } ${className}`}
        {...props}
      />
    </motion.div>
  );
});

AnimatedInput.displayName = "AnimatedInput";

// Zod validation schema
const signinSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(1, { message: "Password is required" }),
  rememberMe: z.boolean().optional(),
});

// Type for the form data
type SigninFormData = z.infer<typeof signinSchema>;

export default function SignInForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const loginMutation = useLogin();
  const { initiateGoogleAuth, isLoading: isGoogleLoading } = useGoogleAuth();

  // Initialize React Hook Form with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid },
  } = useForm<SigninFormData>({
    resolver: zodResolver(signinSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  // Form submission handler
  const onSubmit = async (data: SigninFormData) => {
    setError(null);
    try {
      await loginMutation.mutateAsync({
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe || false,
      });

      // Redirect to dashboard on success
      router.push("/");
    } catch (error: unknown) {
      console.error("Login error:", error);
      const errorMessage =
        error &&
        typeof error === "object" &&
        "response" in error &&
        error.response &&
        typeof error.response === "object" &&
        "data" in error.response &&
        error.response.data &&
        typeof error.response.data === "object" &&
        "message" in error.response.data &&
        typeof error.response.data.message === "string"
          ? error.response.data.message
          : "Login failed. Please check your credentials.";

      setError(errorMessage);
    }
  };

  return (
    <div
      className="no-scrollbar flex w-full flex-1 flex-col overflow-y-auto lg:w-1/2"
      style={{ fontFamily: "'Poppins', sans-serif" }}
    >
      <div className="mx-auto mb-5 w-full max-w-md sm:pt-10">
        <Link
          href="/"
          className="inline-flex items-center text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <ChevronLeftIcon />
          Back to dashboard
        </Link>
      </div>
      <div className="mx-auto flex w-full max-w-md flex-1 flex-col justify-center">
        <div>
          <div className="mb-5 sm:mb-8">
            <h1
              className="text-title-sm sm:text-title-md mb-2 font-semibold text-gray-800 dark:text-white/90"
              style={{ fontFamily: "'Poppins', sans-serif" }}
            >
              Sign In
            </h1>
            <p
              className="text-sm text-gray-500 dark:text-gray-400"
              style={{ fontFamily: "'Poppins', sans-serif" }}
            >
              Enter your email and password to sign in!
            </p>
          </div>
          <div>
            <div className="mb-4 w-full">
              <motion.button
                className="group relative inline-flex w-full items-center justify-center gap-3 overflow-hidden rounded-xl border border-gray-200 bg-white px-8 py-3.5 text-sm font-medium text-gray-700 transition-all duration-300 hover:border-transparent hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-70 dark:border-gray-700 dark:bg-gray-800/30 dark:text-white/90"
                whileHover={{ scale: isGoogleLoading ? 1 : 1.02 }}
                whileTap={{ scale: isGoogleLoading ? 1 : 0.98 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                style={{ fontFamily: "'Poppins', sans-serif" }}
                onClick={() => {
                  if (!isGoogleLoading) {
                    setError(null);
                    try {
                      initiateGoogleAuth();
                      // Note: We don't need to reset isGoogleLoading here as we're redirecting away
                    } catch (err) {
                      setError(
                        "Failed to initiate Google sign-in. Please try again.",
                      );
                      console.error("Google auth error:", err);
                    }
                  }
                }}
                type="button"
                disabled={isGoogleLoading}
              >
                {isGoogleLoading ? (
                  <>
                    <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                    <span>Connecting to Google...</span>
                  </>
                ) : (
                  <>
                    {/* Circular container for Google icon */}
                    <motion.div
                      className="flex h-6 w-6 items-center justify-center overflow-hidden rounded-full bg-white shadow-sm"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <svg
                        width="18"
                        height="18"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.7511 10.1944C18.7511 9.47495 18.6915 8.94995 18.5626 8.40552H10.1797V11.6527H15.1003C15.0011 12.4597 14.4654 13.675 13.2749 14.4916L13.2582 14.6003L15.9087 16.6126L16.0924 16.6305C17.7788 15.1041 18.7511 12.8583 18.7511 10.1944Z"
                          fill="#4285F4"
                        />
                        <path
                          d="M10.1788 18.75C12.5895 18.75 14.6133 17.9722 16.0915 16.6305L13.274 14.4916C12.5201 15.0068 11.5081 15.3666 10.1788 15.3666C7.81773 15.3666 5.81379 13.8402 5.09944 11.7305L4.99473 11.7392L2.23868 13.8295L2.20264 13.9277C3.67087 16.786 6.68674 18.75 10.1788 18.75Z"
                          fill="#34A853"
                        />
                        <path
                          d="M5.10014 11.7305C4.91165 11.186 4.80257 10.6027 4.80257 9.99992C4.80257 9.3971 4.91165 8.81379 5.09022 8.26935L5.08523 8.1534L2.29464 6.02954L2.20333 6.0721C1.5982 7.25823 1.25098 8.5902 1.25098 9.99992C1.25098 11.4096 1.5982 12.7415 2.20333 13.9277L5.10014 11.7305Z"
                          fill="#FBBC05"
                        />
                        <path
                          d="M10.1789 4.63331C11.8554 4.63331 12.9864 5.34303 13.6312 5.93612L16.1511 3.525C14.6035 2.11528 12.5895 1.25 10.1789 1.25C6.68676 1.25 3.67088 3.21387 2.20264 6.07218L5.08953 8.26943C5.81381 6.15972 7.81776 4.63331 10.1789 4.63331Z"
                          fill="#EB4335"
                        />
                      </svg>
                    </motion.div>
                    <span>Sign in with Google</span>
                  </>
                )}

                {/* Animated background effect */}
                <motion.span
                  className="via-brand-500 absolute bottom-0 left-0 h-0.5 w-full bg-gradient-to-r from-transparent to-transparent"
                  animate={{
                    x: ["-100%", "100%"],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 1,
                  }}
                />
              </motion.button>
            </div>

            <div className="relative py-3 sm:py-5">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200 dark:border-gray-800"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span
                  className="bg-white p-2 text-gray-400 sm:px-5 sm:py-2 dark:bg-gray-900"
                  style={{ fontFamily: "'Poppins', sans-serif" }}
                >
                  Or
                </span>
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} noValidate>
              <div className="space-y-6">
                {/* Email */}
                <div>
                  <Label htmlFor="email">
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                      className="font-poppins"
                    >
                      Email<span className="text-error-500">*</span>
                    </motion.span>
                  </Label>
                  <div className="relative">
                    <AnimatedInput
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      {...register("email")}
                      className={
                        errors.email
                          ? "border-error-500 focus:border-error-500"
                          : ""
                      }
                    />
                    <AnimatePresence>
                      {errors.email && (
                        <motion.p
                          className="text-error-500 mt-1 flex items-center text-xs"
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3.5 w-3.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                            />
                          </svg>
                          {errors.email.message}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </div>
                </div>

                {/* Password */}
                <div>
                  <Label htmlFor="password">
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                      className="font-poppins"
                    >
                      Password<span className="text-error-500">*</span>
                    </motion.span>
                  </Label>
                  <div className="relative">
                    <AnimatedInput
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      {...register("password")}
                      className={
                        errors.password
                          ? "border-error-500 focus:border-error-500"
                          : ""
                      }
                    />
                    <motion.span
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 z-30 -translate-y-1/2 cursor-pointer"
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {showPassword ? (
                        <EyeIcon className="fill-gray-500 dark:fill-gray-400" />
                      ) : (
                        <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400" />
                      )}
                    </motion.span>
                  </div>
                  <AnimatePresence>
                    {errors.password && (
                      <motion.p
                        className="text-error-500 mt-1 flex items-center text-xs"
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="mr-1 h-3.5 w-3.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                          />
                        </svg>
                        {errors.password.message}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>

                {/* Error Message */}
                {error && (
                  <motion.div
                    className="text-error-700 bg-error-100 dark:bg-error-900/30 dark:text-error-400 mb-4 rounded-lg p-3 text-sm"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    <div className="flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="mr-2 h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                      {error}
                    </div>
                  </motion.div>
                )}

                {/* Remember me and Forgot password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="rememberMe"
                      className="text-brand-500 focus:ring-brand-500 h-5 w-5 rounded border-gray-300"
                      {...register("rememberMe")}
                    />
                    <Label htmlFor="rememberMe" className="!mb-0">
                      <span
                        className="text-theme-sm block font-normal text-gray-700 dark:text-gray-400"
                        style={{ fontFamily: "'Poppins', sans-serif" }}
                      >
                        Keep me logged in
                      </span>
                    </Label>
                  </div>
                  <Link
                    href="/forget-password"
                    className="text-brand-500 hover:text-brand-600 dark:text-brand-400 text-sm"
                    style={{ fontFamily: "'Poppins', sans-serif" }}
                  >
                    Forgot password?
                  </Link>
                </div>

                {/* Submit Button */}
                <div>
                  <motion.button
                    type="submit"
                    className="from-brand-400 to-brand-600 hover:from-brand-500 hover:to-brand-700 hover:shadow-brand-500/25 relative flex w-full items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r px-6 py-3.5 font-poppins text-sm font-medium text-white shadow-lg transition disabled:cursor-not-allowed disabled:from-gray-400 disabled:to-gray-500 disabled:shadow-none"
                    disabled={
                      isSubmitting || !isValid || loginMutation.isPending
                    }
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    {/* Animated background effect */}
                    <motion.span
                      className="absolute inset-0 h-full w-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      animate={{
                        x: ["-100%", "100%"],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatDelay: 3,
                      }}
                      style={{
                        opacity: isSubmitting || !isValid ? 0 : 0.5,
                      }}
                    />

                    {isSubmitting || loginMutation.isPending ? (
                      <svg
                        className="mr-2 h-5 w-5 animate-spin"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    ) : (
                      <svg
                        className="mr-2 h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                        />
                      </svg>
                    )}
                    {isSubmitting || loginMutation.isPending
                      ? "Signing in..."
                      : "Sign in"}
                  </motion.button>
                </div>
              </div>
            </form>

            <div className="mt-5">
              <p
                className="text-center text-sm font-normal text-gray-700 sm:text-start dark:text-gray-400"
                style={{ fontFamily: "'Poppins', sans-serif" }}
              >
                Don&apos;t have an account? {""}
                <Link
                  href="/signup"
                  className="text-brand-500 hover:text-brand-600 dark:text-brand-400"
                >
                  Sign Up
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
