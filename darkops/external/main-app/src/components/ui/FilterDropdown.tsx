"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";

interface FilterOption {
  /**
   * Value of the option
   */
  value: string;

  /**
   * Label to display for the option
   */
  label: string;

  /**
   * Optional color for the option
   */
  color?: string;

  /**
   * Optional icon for the option
   */
  icon?: React.ReactNode;
}

interface FilterDropdownProps {
  /**
   * Label for the dropdown
   */
  label: string;

  /**
   * Options for the dropdown
   */
  options: FilterOption[];

  /**
   * Current selected value
   */
  value?: string;

  /**
   * Callback function when value changes
   */
  onChange: (value?: string) => void;

  /**
   * Optional placeholder text
   */
  placeholder?: string;

  /**
   * Optional custom button styling
   */
  buttonClassName?: string;

  /**
   * Optional custom dropdown styling
   */
  dropdownClassName?: string;

  /**
   * Whether to show icons in options
   */
  showIcons?: boolean;
}

/**
 * A dropdown component for filtering
 */
const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  value,
  onChange,
  placeholder = "Select...",
  buttonClassName,
  dropdownClassName,
  showIcons = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Get selected option
  const selectedOption = options.find((option) => option.value === value);

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Handle option selection
  const handleSelect = (optionValue: string) => {
    // If the same value is selected, clear the selection
    if (optionValue === value) {
      onChange(undefined);
    } else {
      onChange(optionValue);
    }
    setIsOpen(false);
  };

  // Clear selection
  const clearSelection = () => {
    onChange(undefined);
    setIsOpen(false);
  };

  const defaultButtonClassName =
    "flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700";
  const defaultDropdownClassName =
    "absolute right-0 z-10 mt-2 w-56 rounded-lg border border-gray-200 bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800";

  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        className={buttonClassName || defaultButtonClassName}
      >
        {showIcons && selectedOption?.icon && (
          <span className="flex items-center">{selectedOption.icon}</span>
        )}
        <span>{label}:</span>
        <span className="font-medium">
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <ChevronDown className="h-4 w-4" />
      </button>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          className={dropdownClassName || defaultDropdownClassName}
        >
          <div className="mb-2 border-b border-gray-200 pb-2 dark:border-gray-700">
            <button
              onClick={clearSelection}
              className="w-full rounded-lg px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              Clear selection
            </button>
          </div>

          <div className="max-h-60 overflow-y-auto">
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={`flex w-full items-center gap-2 rounded-lg px-3 py-2 text-left text-sm ${
                  option.value === value
                    ? "bg-indigo-50 text-indigo-700 dark:bg-indigo-900/20 dark:text-indigo-300"
                    : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                }`}
              >
                {showIcons && option.icon && (
                  <span className="flex items-center">{option.icon}</span>
                )}
                <span>{option.label}</span>
              </button>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default FilterDropdown;
