"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
  Smartphone,
  Globe,
  Mail,
  MessageSquare,
  QrCode,
  Plus,
  ChevronDown,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ScanDropdownProps {
  /**
   * Optional custom button styling
   */
  buttonClassName?: string;

  /**
   * Optional custom dropdown styling
   */
  dropdownClassName?: string;

  /**
   * Whether to show as a compact version
   */
  compact?: boolean;

  /**
   * Custom label for the button
   */
  label?: string;

  /**
   * Whether to show enhanced animations
   */
  enhancedAnimations?: boolean;

  /**
   * Whether to respect reduced motion preferences
   */
  respectReducedMotion?: boolean;

  /**
   * Custom animation duration in seconds
   */
  animationDuration?: number;
}

/**
 * A sophisticated dropdown component for selecting scan types with advanced animations
 * and visual effects that match the DarkOps design system
 */
const ScanDropdown: React.FC<ScanDropdownProps> = ({
  buttonClassName,
  dropdownClassName,
  compact = false,
  label = "New Scan",
  enhancedAnimations = true,
  respectReducedMotion = true,
  animationDuration = 0.3,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [navigatingTo, setNavigatingTo] = useState<string | null>(null);

  // Check for reduced motion preference
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  // Clear loading state when pathname changes (navigation complete)
  useEffect(() => {
    if (isNavigating) {
      // Small delay to ensure navigation is complete
      const timer = setTimeout(() => {
        setIsNavigating(false);
        setNavigatingTo(null);
        setIsOpen(false);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [pathname, isNavigating]);

  // Clear loading state on component unmount
  useEffect(() => {
    return () => {
      setIsNavigating(false);
      setNavigatingTo(null);
    };
  }, []);

  // Clear loading state when dropdown is closed externally
  useEffect(() => {
    if (!isOpen && isNavigating) {
      // If dropdown is closed but we're still in loading state, clear it
      const timer = setTimeout(() => {
        setIsNavigating(false);
        setNavigatingTo(null);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isOpen, isNavigating]);

  useEffect(() => {
    if (respectReducedMotion) {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);

      const handleChange = (e: MediaQueryListEvent) => {
        setPrefersReducedMotion(e.matches);
      };

      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }
  }, [respectReducedMotion]);

  // Scan options with enhanced metadata
  const scanOptions = [
    {
      id: "apk",
      label: "APK Scanner",
      description: "Analyze Android applications",
      icon: Smartphone,
      path: "/apk-scanner",
      color: "text-indigo-600 dark:text-indigo-400",
      hoverColor:
        "hover:bg-indigo-50 hover:text-indigo-700 dark:hover:bg-indigo-900/50",
    },
    {
      id: "url",
      label: "URL Scanner",
      description: "Scan web links for threats",
      icon: Globe,
      path: "/url-scanner",
      color: "text-blue-600 dark:text-blue-400",
      hoverColor:
        "hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/50",
    },
    {
      id: "email",
      label: "Email Analyzer",
      description: "Detect phishing emails",
      icon: Mail,
      path: "/email-analyzer",
      color: "text-green-600 dark:text-green-400",
      hoverColor:
        "hover:bg-green-50 hover:text-green-700 dark:hover:bg-green-900/50",
    },
    {
      id: "sms",
      label: "SMS Analyzer",
      description: "Analyze text messages",
      icon: MessageSquare,
      path: "/sms-analyzer",
      color: "text-purple-600 dark:text-purple-400",
      hoverColor:
        "hover:bg-purple-50 hover:text-purple-700 dark:hover:bg-purple-900/50",
    },
    {
      id: "qr",
      label: "QR Scanner",
      description: "Scan QR codes safely",
      icon: QrCode,
      path: "/qr-checker",
      color: "text-orange-600 dark:text-orange-400",
      hoverColor:
        "hover:bg-orange-50 hover:text-orange-700 dark:hover:bg-orange-900/50",
    },
  ];

  // Animation variants for sophisticated effects
  const containerVariants = {
    hidden: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 0.95,
      y: prefersReducedMotion ? 0 : 10,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0.1 : animationDuration,
        type: "spring",
        stiffness: 300,
        damping: 30,
      },
    },
  };

  const dropdownVariants = {
    hidden: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 0.95,
      y: prefersReducedMotion ? 0 : -10,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0.1 : animationDuration,
        type: "spring",
        stiffness: 400,
        damping: 25,
        staggerChildren: prefersReducedMotion ? 0 : 0.05,
      },
    },
    exit: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 0.95,
      y: prefersReducedMotion ? 0 : -10,
      transition: {
        duration: prefersReducedMotion ? 0.1 : animationDuration * 0.7,
        ease: "easeInOut",
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      x: prefersReducedMotion ? 0 : -10,
      scale: prefersReducedMotion ? 1 : 0.95,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: prefersReducedMotion ? 0.1 : animationDuration * 0.8,
        type: "spring",
        stiffness: 300,
        damping: 25,
      },
    },
  };

  // Handle navigation with loading state
  const handleNavigation = async (path: string, scanType: string) => {
    try {
      setIsNavigating(true);
      setNavigatingTo(scanType);

      // Add a small delay for visual feedback
      await new Promise((resolve) => setTimeout(resolve, 150));

      // Navigate to the new route
      router.push(path);

      // Set a fallback timeout to clear loading state if navigation doesn't trigger pathname change
      setTimeout(() => {
        setIsNavigating(false);
        setNavigatingTo(null);
        setIsOpen(false);
      }, 2000); // 2 second fallback
    } catch (error) {
      // Handle navigation errors
      console.error("Navigation error:", error);
      setIsNavigating(false);
      setNavigatingTo(null);
    }
  };

  // Default styling with enhanced effects
  const defaultButtonClassName = compact
    ? "relative inline-flex items-center justify-center gap-2 rounded-lg bg-indigo-600 px-3 py-2 text-sm text-white transition-all duration-200 hover:bg-indigo-700 hover:shadow-lg hover:shadow-indigo-500/25 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 active:scale-95 dark:bg-indigo-600 dark:hover:bg-indigo-700 dark:hover:shadow-indigo-400/20"
    : "relative inline-flex w-full items-center justify-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-sm text-white transition-all duration-200 hover:bg-indigo-700 hover:shadow-lg hover:shadow-indigo-500/25 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 active:scale-95 sm:w-auto dark:bg-indigo-600 dark:hover:bg-indigo-700 dark:hover:shadow-indigo-400/20";

  const defaultDropdownClassName =
    "relative z-[9999] min-w-[240px] overflow-hidden rounded-xl border border-gray-200/80 bg-white p-1 shadow-xl shadow-gray-900/10 dark:border-gray-700/80 dark:bg-gray-800 dark:shadow-black/20";

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="relative z-[9998]"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <motion.button
            className={buttonClassName || defaultButtonClassName}
            whileHover={
              enhancedAnimations && !prefersReducedMotion
                ? {
                    scale: 1.02,
                    boxShadow: "0 8px 25px rgba(99, 102, 241, 0.3)",
                  }
                : {}
            }
            whileTap={
              enhancedAnimations && !prefersReducedMotion
                ? {
                    scale: 0.98,
                  }
                : {}
            }
            transition={{ duration: 0.15 }}
            disabled={isNavigating}
          >
            {/* Button content with loading state */}
            <AnimatePresence mode="wait">
              {isNavigating ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center gap-2"
                >
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading...</span>
                </motion.div>
              ) : (
                <motion.div
                  key="content"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>{label}</span>
                  <motion.div
                    animate={{ rotate: isOpen ? 180 : 0 }}
                    transition={{
                      duration: prefersReducedMotion ? 0.1 : 0.2,
                    }}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Hover glow effect */}
            <AnimatePresence>
              {isHovered && enhancedAnimations && !prefersReducedMotion && (
                <motion.div
                  className="absolute inset-0 rounded-lg bg-gradient-to-r from-indigo-400/20 via-purple-400/20 to-indigo-400/20"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </AnimatePresence>
          </motion.button>
        </DropdownMenuTrigger>

        <AnimatePresence>
          {isOpen && (
            <DropdownMenuContent
              className={dropdownClassName || defaultDropdownClassName}
              asChild
              sideOffset={8}
            >
              <motion.div
                variants={dropdownVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                {/* Dropdown header with subtle animation */}
                <motion.div
                  variants={itemVariants}
                  className="mb-2 border-b border-gray-200/50 pb-2 dark:border-gray-700/50"
                >
                  <div className="px-3 py-2">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                      Choose scan type
                    </p>
                  </div>
                </motion.div>

                {/* Scan options with staggered animations */}
                <div className="space-y-1">
                  {scanOptions.map((option, index) => {
                    const IconComponent = option.icon;
                    const isCurrentlyNavigating = navigatingTo === option.id;

                    return (
                      <motion.div
                        key={option.id}
                        variants={itemVariants}
                        custom={index}
                      >
                        <DropdownMenuItem asChild>
                          <motion.button
                            onClick={() =>
                              handleNavigation(option.path, option.id)
                            }
                            className={`group relative flex w-full items-center gap-3 rounded-lg px-3 py-3 text-sm transition-all duration-200 ${option.hoverColor} dark:text-gray-200`}
                            disabled={isNavigating}
                            whileHover={
                              enhancedAnimations && !prefersReducedMotion
                                ? {
                                    scale: 1.02,
                                    x: 4,
                                  }
                                : {}
                            }
                            whileTap={
                              enhancedAnimations && !prefersReducedMotion
                                ? {
                                    scale: 0.98,
                                  }
                                : {}
                            }
                            transition={{ duration: 0.15 }}
                          >
                            {/* Icon with loading state */}
                            <div className="relative flex h-5 w-5 items-center justify-center">
                              <AnimatePresence mode="wait">
                                {isCurrentlyNavigating ? (
                                  <motion.div
                                    key="loading"
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.5 }}
                                  >
                                    <Loader2 className="h-4 w-4 animate-spin text-indigo-600 dark:text-indigo-400" />
                                  </motion.div>
                                ) : (
                                  <motion.div
                                    key="icon"
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.5 }}
                                  >
                                    <IconComponent
                                      className={`h-4 w-4 transition-colors duration-200 ${option.color}`}
                                    />
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </div>

                            {/* Content */}
                            <div className="flex flex-1 flex-col items-start">
                              <span className="font-medium">
                                {option.label}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {option.description}
                              </span>
                            </div>

                            {/* Hover indicator */}
                            <motion.div
                              className="h-1 w-1 rounded-full bg-indigo-500 opacity-0 group-hover:opacity-100"
                              initial={{ scale: 0 }}
                              whileHover={{ scale: 1 }}
                              transition={{ duration: 0.2 }}
                            />

                            {/* Subtle glow effect on hover */}
                            <AnimatePresence>
                              {enhancedAnimations && !prefersReducedMotion && (
                                <motion.div
                                  className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100"
                                  initial={{ x: "-100%" }}
                                  whileHover={{ x: "100%" }}
                                  transition={{
                                    duration: 0.6,
                                    ease: "easeInOut",
                                  }}
                                />
                              )}
                            </AnimatePresence>
                          </motion.button>
                        </DropdownMenuItem>
                      </motion.div>
                    );
                  })}
                </div>

                {/* Footer with subtle branding */}
                <motion.div
                  variants={itemVariants}
                  className="mt-2 border-t border-gray-200/50 pt-2 dark:border-gray-700/50"
                >
                  <div className="px-3 py-2">
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      DarkOps Security Platform
                    </p>
                  </div>
                </motion.div>
              </motion.div>
            </DropdownMenuContent>
          )}
        </AnimatePresence>
      </DropdownMenu>

      {/* Ambient glow effect when hovered */}
      <AnimatePresence>
        {isHovered && enhancedAnimations && !prefersReducedMotion && (
          <motion.div
            className="pointer-events-none absolute inset-0 rounded-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.1 }}
            exit={{ opacity: 0 }}
            style={{
              background:
                "radial-gradient(circle at center, #6366f1 0%, transparent 70%)",
              filter: "blur(20px)",
            }}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ScanDropdown;
