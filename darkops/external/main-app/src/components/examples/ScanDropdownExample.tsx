"use client";

import React from "react";
import ScanDropdown from "@/components/ui/ScanDropdown";

/**
 * Example component showing how to use the ScanDropdown component
 * This demonstrates the different ways to use the dropdown with various styling options
 */
const ScanDropdownExample: React.FC = () => {
  return (
    <div className="space-y-8 p-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Scan Dropdown Examples
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          These examples show how to use the ScanDropdown component with
          sophisticated animations, proper loading states, and high z-index for
          top-bar compatibility.
        </p>
      </div>

      {/* Enhanced Scan Dropdown */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Enhanced Scan Dropdown (Default)
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Full-featured dropdown with sophisticated animations and
          micro-interactions
        </p>
        <div className="flex justify-start">
          <ScanDropdown />
        </div>
      </div>

      {/* Compact Enhanced Dropdown */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Compact Enhanced Dropdown
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Compact version with all enhanced features, perfect for headers and
          toolbars
        </p>
        <div className="flex justify-start">
          <ScanDropdown compact={true} label="Scan" />
        </div>
      </div>

      {/* Reduced Motion Version */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Accessibility-Friendly Version
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Respects user's reduced motion preferences for better accessibility
        </p>
        <div className="flex justify-start">
          <ScanDropdown
            label="Accessible Scan"
            respectReducedMotion={true}
            enhancedAnimations={false}
          />
        </div>
      </div>

      {/* Custom Label Scan Dropdown */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Custom Label Scan Dropdown
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Dropdown with custom label text
        </p>
        <div className="flex justify-start">
          <ScanDropdown label="Start Scan" />
        </div>
      </div>

      {/* Custom Styled Scan Dropdown */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Custom Styled Enhanced Dropdown
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Dropdown with custom styling and enhanced animations (green theme)
        </p>
        <div className="flex justify-start">
          <ScanDropdown
            label="Security Scan"
            buttonClassName="relative inline-flex w-full items-center justify-center gap-2 rounded-lg bg-green-600 px-4 py-2 text-sm text-white transition-all duration-200 hover:bg-green-700 hover:shadow-lg hover:shadow-green-500/25 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 active:scale-95 sm:w-auto dark:bg-green-600 dark:hover:bg-green-700 dark:hover:shadow-green-400/20"
            enhancedAnimations={true}
          />
        </div>
      </div>

      {/* Fast Animation Version */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Fast Animation Dropdown
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Dropdown with faster animation timing for snappy interactions
        </p>
        <div className="flex justify-start">
          <ScanDropdown
            label="Quick Scan"
            animationDuration={0.15}
            enhancedAnimations={true}
          />
        </div>
      </div>

      {/* Loading State Demo */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Loading State & Navigation
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Click any scan option to see the loading state in action. The loading
          state automatically clears after navigation.
        </p>
        <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                Test Navigation & Loading States
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                High z-index ensures dropdown appears above navigation bars
              </p>
            </div>
            <ScanDropdown compact={true} label="Test Navigation" />
          </div>
        </div>
      </div>

      {/* Multiple Dropdowns in a Row */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Multiple Dropdowns
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Multiple scan dropdowns in a row, useful for different contexts
        </p>
        <div className="flex flex-wrap gap-4">
          <ScanDropdown compact={true} label="Quick Scan" />
          <ScanDropdown compact={true} label="Deep Scan" />
          <ScanDropdown compact={true} label="Custom Scan" />
        </div>
      </div>

      {/* Usage in a Card */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          In a Card Layout
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Example of using the dropdown within a card component
        </p>
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Security Dashboard
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Start a new security scan to analyze potential threats
              </p>
            </div>
            <ScanDropdown compact={true} />
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Enhanced Usage Instructions
        </h3>
        <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
          <pre className="text-sm text-gray-700 dark:text-gray-300">
            {`// Basic enhanced usage (default)
<ScanDropdown />

// Compact version with all enhancements
<ScanDropdown compact={true} label="Scan" />

// Accessibility-friendly version
<ScanDropdown
  respectReducedMotion={true}
  enhancedAnimations={false}
/>

// Custom animation timing
<ScanDropdown
  animationDuration={0.15}
  enhancedAnimations={true}
/>

// Fully customized
<ScanDropdown
  label="Custom Scan"
  compact={true}
  enhancedAnimations={true}
  respectReducedMotion={true}
  animationDuration={0.3}
  buttonClassName="your-custom-classes"
  dropdownClassName="your-dropdown-classes"
/>`}
          </pre>
        </div>
      </div>

      {/* Features List */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Enhanced Features
        </h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <h4 className="font-medium text-gray-900 dark:text-white">
              🎨 Visual Effects
            </h4>
            <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>• Ambient glow effects</li>
              <li>• Smooth color transitions</li>
              <li>• Subtle shadow animations</li>
              <li>• Hover micro-interactions</li>
              <li>• Enhanced button effects</li>
            </ul>
          </div>
          <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <h4 className="font-medium text-gray-900 dark:text-white">
              ⚡ Animations
            </h4>
            <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>• Staggered menu item animations</li>
              <li>• Spring physics transitions</li>
              <li>• Smooth chevron rotation</li>
              <li>• Smart loading state management</li>
              <li>• Scale and fade effects</li>
            </ul>
          </div>
          <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <h4 className="font-medium text-gray-900 dark:text-white">
              ♿ Accessibility
            </h4>
            <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>• Respects reduced motion</li>
              <li>• Keyboard navigation</li>
              <li>• Screen reader support</li>
              <li>• Focus management</li>
              <li>• ARIA compliance</li>
            </ul>
          </div>
          <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <h4 className="font-medium text-gray-900 dark:text-white">
              🔧 Technical
            </h4>
            <ul className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>• Framer Motion powered</li>
              <li>• TypeScript support</li>
              <li>• Dark mode compatible</li>
              <li>• High z-index (9999) for top-bar compatibility</li>
              <li>• Auto-clearing loading states</li>
              <li>• Navigation error handling</li>
              <li>• Performance optimized</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScanDropdownExample;
