"use client";

import { StagewiseToolbar as StagewiseToolbarComponent } from "@stagewise/toolbar-next";
import { useEffect, useState } from "react";

const stagewiseConfig = {
  plugins: [],
};

export function StagewiseToolbar() {
  const [isDev, setIsDev] = useState(false);

  useEffect(() => {
    setIsDev(process.env.NODE_ENV === "development");
  }, []);

  if (!isDev) return null;

  return <StagewiseToolbarComponent config={stagewiseConfig} />;
}
