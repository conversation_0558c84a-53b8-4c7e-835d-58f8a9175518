"use client";
import React, { useState, useEffect } from "react";
import { FaWifi } from "react-icons/fa";
import { toast } from "react-hot-toast";
import { SMSAnalysisService } from "@/services/sms.service";

interface QRScanResultProps {
  result: string;
  contentType: string;
  onScanAgain: () => void;
}

export default function QRScanResult({
  result,
  contentType,
  onScanAgain,
}: QRScanResultProps) {
  const [parsedContent, setParsedContent] = useState<any>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<any>(null);
  const [copyStatus, setCopyStatus] = useState<{ [key: string]: boolean }>({});

  // Helper function to format confidence values
  const formatConfidence = (confidence: number) => {
    // Check if confidence is already in percentage format (greater than 1)
    if (confidence > 1) {
      return confidence.toFixed(1) + "%";
    }
    // Otherwise, convert from decimal to percentage
    return (confidence * 100).toFixed(1) + "%";
  };

  // Function to copy content to clipboard
  const copyToClipboard = async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopyStatus({ ...copyStatus, [key]: true });

      // Reset copy status after 2 seconds
      setTimeout(() => {
        setCopyStatus({ ...copyStatus, [key]: false });
      }, 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // Function to scan SMS content
  const handleScanSMS = async () => {
    if (!result || contentType !== "sms") return;

    setIsScanning(true);

    try {
      // Use the SMS message content for analysis
      const messageToAnalyze = parsedContent?.body || result;

      // Send SMS content to backend for analysis using the proper service
      const data = await SMSAnalysisService.analyzeSMS(messageToAnalyze);
      setScanResults(data);

      toast.success("SMS analysis completed successfully");
    } catch (error) {
      console.error("Error scanning SMS:", error);
      toast.error("Failed to scan SMS content");
    } finally {
      setIsScanning(false);
    }
  };

  useEffect(() => {
    // Parse different content types
    if (contentType === "wifi") {
      const wifiData: any = {};
      const parts = result.substring(5).split(";");

      parts.forEach((part) => {
        if (part.includes("S:")) {
          wifiData.ssid = part.replace("S:", "");
        } else if (part.includes("T:")) {
          wifiData.type = part.replace("T:", "");
        } else if (part.includes("P:")) {
          wifiData.password = part.replace("P:", "");
        } else if (part.includes("H:")) {
          wifiData.hidden = part.replace("H:", "") === "true";
        }
      });

      setParsedContent(wifiData);
    } else if (contentType === "email") {
      if (result.startsWith("MATMSG:")) {
        const emailData: any = {};
        const parts = result.substring(7).split(";");

        parts.forEach((part) => {
          if (part.startsWith("TO:")) {
            emailData.email = part.replace("TO:", "");
          } else if (part.startsWith("SUB:")) {
            emailData.subject = part.replace("SUB:", "");
          } else if (part.startsWith("BODY:")) {
            emailData.body = part.replace("BODY:", "");
          }
        });

        setParsedContent(emailData);
      } else {
        const email = result.replace("mailto:", "");
        setParsedContent({ email });
      }
    } else if (contentType === "phone") {
      const phone = result.replace("tel:", "");
      setParsedContent({ phone });
    } else if (contentType === "sms") {
      if (result.startsWith("SMSTO:")) {
        const smsData: any = {};
        const parts = result.substring(6).split(":");

        smsData.number = parts[0];
        if (parts.length > 1) {
          smsData.body = parts[1];
        }

        setParsedContent(smsData);
      } else {
        const smsData: any = {};
        const parts = result.replace("sms:", "").split("?");

        smsData.number = parts[0];
        if (parts.length > 1 && parts[1].includes("body=")) {
          smsData.body = decodeURIComponent(parts[1].replace("body=", ""));
        }

        setParsedContent(smsData);
      }
    } else {
      setParsedContent({ content: result });
    }
  }, [result, contentType]);

  const getContentIcon = () => {
    switch (contentType) {
      case "url":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-scan-url dark:text-scan-url"
          >
            <circle cx="12" cy="12" r="10" />
            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20" />
            <path d="M2 12h20" />
          </svg>
        );
      case "wifi":
        return <FaWifi className="size-6 text-green-500 dark:text-green-400" />;
      case "email":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-scan-email dark:text-scan-email"
          >
            <rect width="20" height="16" x="2" y="4" rx="2" />
            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
          </svg>
        );
      case "phone":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-blue-500 dark:text-blue-400"
          >
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
          </svg>
        );
      case "sms":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-scan-sms dark:text-scan-sms"
          >
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
          </svg>
        );
      default:
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-gray-500 dark:text-gray-400"
          >
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
            <polyline points="14 2 14 8 20 8" />
            <line x1="16" y1="13" x2="8" y2="13" />
            <line x1="16" y1="17" x2="8" y2="17" />
            <line x1="10" y1="9" x2="8" y2="9" />
          </svg>
        );
    }
  };

  const getContentTitle = () => {
    switch (contentType) {
      case "url":
        return scanResults ? "URL Analysis Results" : "URL Detected";
      case "wifi":
        return "Wi-Fi Network";
      case "email":
        return "Email Address";
      case "phone":
        return "Phone Number";
      case "sms":
        return "SMS Message";
      default:
        return "Text Content";
    }
  };

  // Function to scan URL directly
  const handleScanUrl = async () => {
    if (!result || contentType !== "url") return;

    setIsScanning(true);

    // Simulate API call with a timeout
    setTimeout(() => {
      // Mock response - in a real app, this would come from an API
      const mockResults = {
        phishing_analysis: {
          url: result,
          is_safe: Math.random() > 0.3, // Randomly determine if safe (70% chance of being safe)
          confidence: 0.9996953600922485,
          prediction: "Safe",
        },
        url_analysis: {
          basic_info: {
            domain: new URL(result).hostname,
            ip: "**************",
            country: "US",
            server: "Server",
            security_state: null,
            final_url: result,
          },
          security: {
            malicious: Math.random() > 0.7, // 30% chance of being malicious
            score: Math.floor(Math.random() * 100),
            categories: [],
            brands: [],
            threats: [],
          },
          scan_info: {
            scan_id: "0196a5f6-4027-73e0-a3b7-c4af8f42de88",
            scan_result_url: "#",
            screenshot_url:
              "https://placehold.co/800x600/e2e8f0/64748b?text=Screenshot+Not+Available",
            scan_time: new Date().toISOString(),
          },
        },
      };

      setScanResults(mockResults);
      setIsScanning(false);
    }, 2000);
  };

  const renderContent = () => {
    if (!parsedContent) return null;

    switch (contentType) {
      case "url":
        if (scanResults) {
          // Show scan results
          const phishingAnalysis = scanResults.phishing_analysis;
          const urlAnalysis = scanResults.url_analysis;
          const isSafe = phishingAnalysis.is_safe;
          const confidence = phishingAnalysis.confidence;

          return (
            <div>
              {/* URL and Safety Status */}
              <div className="mb-6">
                <div className="mb-2 flex items-center gap-3">
                  <span
                    className={`rounded-md px-2 py-1 text-xs font-bold ${
                      isSafe
                        ? "bg-status-safe text-white"
                        : "bg-status-danger text-white"
                    }`}
                  >
                    {isSafe ? "SAFE" : "MALICIOUS"}
                  </span>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {isSafe
                      ? "This URL appears to be safe based on our analysis"
                      : "This URL appears to be malicious based on our analysis"}
                  </h3>
                </div>
                <div className="break-all text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">URL:</span> {result}
                </div>
                <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Confidence: {(confidence * 100).toFixed(2)}%
                </div>
              </div>

              {/* Basic Information */}
              <div className="mb-6">
                <h3 className="text-md mb-3 font-medium text-gray-800 dark:text-gray-200">
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      Domain
                    </p>
                    <p className="text-base font-medium text-gray-900 dark:text-white">
                      {urlAnalysis.basic_info.domain || "N/A"}
                    </p>
                  </div>
                  <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      IP Address
                    </p>
                    <p className="text-base font-medium text-gray-900 dark:text-white">
                      {urlAnalysis.basic_info.ip || "N/A"}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                <a
                  href={result}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-scan-url hover:bg-scan-url/90 inline-flex items-center gap-2 rounded-lg px-4 py-2 text-white"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                    <polyline points="15 3 21 3 21 9" />
                    <line x1="10" y1="14" x2="21" y2="3" />
                  </svg>
                  Open URL
                </a>
                <button
                  onClick={() => copyToClipboard(result, "url-result")}
                  className="inline-flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
                  </svg>
                  {copyStatus["url-result"] ? "Copied!" : "Copy URL"}
                </button>
                <button
                  onClick={onScanAgain}
                  className="bg-scan-url/20 text-scan-url dark:bg-scan-url/10 dark:text-scan-url hover:bg-scan-url/30 dark:hover:bg-scan-url/20 inline-flex items-center gap-2 rounded-lg px-4 py-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M3 2v6h6" />
                    <path d="M21 12A9 9 0 0 0 6 5.3L3 8" />
                    <path d="M21 22v-6h-6" />
                    <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7" />
                  </svg>
                  Scan Another QR
                </button>
              </div>
            </div>
          );
        } else {
          // Show initial URL view with scan option
          return (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    URL:
                  </span>
                  <a
                    href={result}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    {result}
                  </a>
                </div>
                <button
                  onClick={() => copyToClipboard(result, "url")}
                  className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  {copyStatus["url"] ? "Copied!" : "Copy"}
                </button>
              </div>
              <button
                onClick={handleScanUrl}
                disabled={isScanning}
                className="w-full rounded-lg bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50"
              >
                {isScanning ? "Scanning..." : "Scan URL"}
              </button>
            </div>
          );
        }
      case "wifi":
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  SSID:
                </span>
                <span>{parsedContent.ssid}</span>
              </div>
              <button
                onClick={() => copyToClipboard(parsedContent.ssid, "ssid")}
                className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              >
                {copyStatus["ssid"] ? "Copied!" : "Copy"}
              </button>
            </div>
            {parsedContent.password && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Password:
                  </span>
                  <span>{parsedContent.password}</span>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(parsedContent.password, "password")
                  }
                  className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  {copyStatus["password"] ? "Copied!" : "Copy"}
                </button>
              </div>
            )}
            {parsedContent.type && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Type:
                </span>
                <span>{parsedContent.type}</span>
              </div>
            )}
            {parsedContent.hidden !== undefined && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Hidden:
                </span>
                <span>{parsedContent.hidden ? "Yes" : "No"}</span>
              </div>
            )}
          </div>
        );
      case "email":
        return (
          <div className="space-y-4">
            {parsedContent.email && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Email:
                  </span>
                  <a
                    href={`mailto:${parsedContent.email}`}
                    className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    {parsedContent.email}
                  </a>
                </div>
                <button
                  onClick={() => copyToClipboard(parsedContent.email, "email")}
                  className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  {copyStatus["email"] ? "Copied!" : "Copy"}
                </button>
              </div>
            )}
            {parsedContent.subject && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Subject:
                  </span>
                  <span>{parsedContent.subject}</span>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(parsedContent.subject, "subject")
                  }
                  className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  {copyStatus["subject"] ? "Copied!" : "Copy"}
                </button>
              </div>
            )}
            {parsedContent.body && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Body:
                  </span>
                  <span>{parsedContent.body}</span>
                </div>
                <button
                  onClick={() => copyToClipboard(parsedContent.body, "body")}
                  className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  {copyStatus["body"] ? "Copied!" : "Copy"}
                </button>
              </div>
            )}
          </div>
        );
      case "phone":
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Phone:
                </span>
                <a
                  href={`tel:${parsedContent.phone}`}
                  className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {parsedContent.phone}
                </a>
              </div>
              <button
                onClick={() => copyToClipboard(parsedContent.phone, "phone")}
                className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              >
                {copyStatus["phone"] ? "Copied!" : "Copy"}
              </button>
            </div>
          </div>
        );
      case "sms":
        if (scanResults) {
          return (
            <div className="space-y-4">
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                  SMS Analysis Results
                </h3>
                <div className="space-y-4">
                  {/* Phishing Detection Result */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Detection Result:
                    </span>
                    <span
                      className={`rounded-md px-2 py-1 text-xs font-bold ${
                        scanResults.is_phishing
                          ? "bg-status-danger text-white"
                          : "bg-status-safe text-white"
                      }`}
                    >
                      {scanResults.is_phishing ? "PHISHING" : "SAFE"}
                    </span>
                  </div>

                  {/* Confidence Score */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Confidence:
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formatConfidence(scanResults.confidence)}
                    </span>
                  </div>

                  {/* Risk Level */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Risk Level:
                    </span>
                    <span
                      className={`font-medium ${
                        scanResults.risk_level?.toLowerCase() === "high"
                          ? "text-red-500"
                          : scanResults.risk_level?.toLowerCase() === "medium"
                            ? "text-yellow-500"
                            : "text-green-500"
                      }`}
                    >
                      {scanResults.risk_level?.toUpperCase() || "UNKNOWN"}
                    </span>
                  </div>

                  {/* Scan Engines */}
                  {scanResults.scanEngines &&
                    scanResults.scanEngines.length > 0 && (
                      <div className="mt-4">
                        <h4 className="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                          Scan Engines:
                        </h4>
                        <ul className="space-y-2">
                          {scanResults.scanEngines.map(
                            (engine: any, index: number) => (
                              <li
                                key={index}
                                className="rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800"
                              >
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-gray-900 dark:text-white">
                                    {engine.name}
                                  </span>
                                  <span
                                    className={`text-sm font-medium ${
                                      engine.result?.toLowerCase() ===
                                        "phishing" ||
                                      engine.result?.toLowerCase() ===
                                        "malicious"
                                        ? "text-red-500"
                                        : "text-green-500"
                                    }`}
                                  >
                                    {engine.result?.toUpperCase() || "UNKNOWN"}
                                  </span>
                                </div>
                                <div className="mt-1 flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
                                  <span>
                                    Confidence:{" "}
                                    {formatConfidence(engine.confidence)}
                                  </span>
                                  <span>Version: {engine.version}</span>
                                </div>
                                {engine.details && (
                                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                    {engine.details}
                                  </p>
                                )}
                              </li>
                            ),
                          )}
                        </ul>
                      </div>
                    )}
                </div>
              </div>
              <button
                onClick={() => setScanResults(null)}
                className="w-full rounded-lg bg-gray-100 px-4 py-2 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                Back to SMS Details
              </button>
            </div>
          );
        }
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Number:
                </span>
                <a
                  href={`sms:${parsedContent.number}`}
                  className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {parsedContent.number}
                </a>
              </div>
              <button
                onClick={() => copyToClipboard(parsedContent.number, "number")}
                className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              >
                {copyStatus["number"] ? "Copied!" : "Copy"}
              </button>
            </div>
            {parsedContent.body && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Message:
                  </span>
                  <span>{parsedContent.body}</span>
                </div>
                <button
                  onClick={() => copyToClipboard(parsedContent.body, "message")}
                  className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  {copyStatus["message"] ? "Copied!" : "Copy"}
                </button>
              </div>
            )}
            <button
              onClick={handleScanSMS}
              disabled={isScanning}
              className="bg-scan-sms hover:bg-scan-sms/90 w-full rounded-lg px-4 py-2 text-white disabled:opacity-50"
            >
              {isScanning ? "Scanning..." : "Scan SMS Content"}
            </button>
          </div>
        );
      default:
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Content:
                </span>
                <span>{parsedContent.content}</span>
              </div>
              <button
                onClick={() =>
                  copyToClipboard(parsedContent.content, "content")
                }
                className="text-sm text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              >
                {copyStatus["content"] ? "Copied!" : "Copy"}
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-gradient-to-b from-indigo-50/70 to-white p-6 dark:border-gray-700 dark:from-indigo-950/10 dark:to-gray-900">
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={onScanAgain}
          className="text-scan-qr hover:text-scan-qr/80 dark:text-scan-qr dark:hover:text-scan-qr/80 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-1"
          >
            <path d="m12 19-7-7 7-7" />
            <path d="M19 12H5" />
          </svg>
          Scan Another QR Code
        </button>
      </div>

      <div className="mb-6">
        <div className="mb-4 flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-100/80 dark:bg-indigo-900/20">
            {getContentIcon()}
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {getContentTitle()}
          </h2>
        </div>

        <div className="mt-4">{renderContent()}</div>
      </div>

      <div className="mt-8 border-t border-gray-200 pt-4 dark:border-gray-700">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          QR code scanned successfully. The content has been processed and
          displayed above.
        </p>
      </div>
    </div>
  );
}
