"use client";
import React, { useState, useEffect } from "react";
import { motion, useAnimation, AnimatePresence } from "framer-motion";
import { Dropdown } from "../ui/dropdown/Dropdown";
import { DropdownItem } from "../ui/dropdown/DropdownItem";
import { MoreDotIcon } from "@/icons";
import useNavigationAwareDashboardStats from "@/hooks/useNavigationAwareDashboardStats";

// SVG filter for glow effect
const SvgFilter = () => (
  <svg width="0" height="0" style={{ position: "absolute" }}>
    <defs>
      <filter id="glow-green" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="3" result="blur" />
        <feFlood floodColor="#10b981" floodOpacity="0.5" result="color" />
        <feComposite in="color" in2="blur" operator="in" result="glow" />
        <feMerge>
          <feMergeNode in="glow" />
          <feMergeNode in="SourceGraphic" />
        </feMerge>
      </filter>
      <filter id="glow-yellow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="3" result="blur" />
        <feFlood floodColor="#f59e0b" floodOpacity="0.5" result="color" />
        <feComposite in="color" in2="blur" operator="in" result="glow" />
        <feMerge>
          <feMergeNode in="glow" />
          <feMergeNode in="SourceGraphic" />
        </feMerge>
      </filter>
      <filter id="glow-red" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="3" result="blur" />
        <feFlood floodColor="#ef4444" floodOpacity="0.5" result="color" />
        <feComposite in="color" in2="blur" operator="in" result="glow" />
        <feMerge>
          <feMergeNode in="glow" />
          <feMergeNode in="SourceGraphic" />
        </feMerge>
      </filter>
    </defs>
  </svg>
);

// Arrow icons for trend indicators
const ArrowUpIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="m5 12 7-7 7 7" />
    <path d="M12 19V5" />
  </svg>
);

const ArrowDownIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M12 5v14" />
    <path d="m19 12-7 7-7-7" />
  </svg>
);

export default function ThreatScore() {
  const [isOpen, setIsOpen] = useState(false);
  const [displayScore, setDisplayScore] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [previousScore, setPreviousScore] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const { data, isLoading, error, refetch } =
    useNavigationAwareDashboardStats();

  // Animation controls
  const controls = useAnimation();

  // Get target score directly from GraphQL data
  const actualScore = data?.dashboardStats?.threatScore?.score || 0;

  // Calculate score properties based on the actual score from data
  const getScoreProperties = (score: number) => {
    let status, color, filter, bgGradient;

    if (score <= 30) {
      status = "Safe";
      color = "#10b981"; // emerald green
      filter = "url(#glow-green)";
      bgGradient =
        "from-green-50 to-white dark:from-green-950/10 dark:to-gray-900";
    } else if (score <= 70) {
      status = "At Risk";
      color = "#f59e0b"; // amber
      filter = "url(#glow-yellow)";
      bgGradient =
        "from-amber-50 to-white dark:from-amber-950/10 dark:to-gray-900";
    } else {
      status = "Critical";
      color = "#ef4444"; // rose red
      filter = "url(#glow-red)";
      bgGradient = "from-red-50 to-white dark:from-red-950/10 dark:to-gray-900";
    }

    return { status, color, filter, bgGradient };
  };

  // Get current score properties - use actual score from data for consistent colors
  const { status, color, filter, bgGradient } = getScoreProperties(actualScore);

  // Calculate the change percentage
  const isImproved = actualScore < previousScore; // Lower score is better for threat score

  // Calculate the circumference of the circle
  const radius = 45;
  const circumference = 2 * Math.PI * radius;

  // Update previous score when data changes
  useEffect(() => {
    if (data?.dashboardStats?.threatScore) {
      setPreviousScore(data.dashboardStats.threatScore.previousScore || 0);
    }
  }, [data]);

  // Set initial score and handle animation when data changes
  useEffect(() => {
    if (!data?.dashboardStats?.threatScore || isLoading || error) return;

    const newScore = data.dashboardStats.threatScore.score || 0;

    // If we're not already animating, immediately set the score
    if (!isAnimating) {
      setDisplayScore(newScore);

      // Animate the gauge
      controls.start({
        strokeDashoffset: circumference - (circumference * newScore) / 100,
        transition: {
          duration: 1.5,
          ease: "easeOut",
        },
      });

      return;
    }

    // Otherwise, animate to the new score
    const startScore = displayScore;
    const targetScore = newScore;

    // Don't animate if the scores are the same
    if (startScore === targetScore) return;

    setIsAnimating(true);

    // Animate the gauge
    controls.start({
      strokeDashoffset: circumference - (circumference * targetScore) / 100,
      transition: {
        duration: 1.5,
        ease: "easeOut",
      },
    });

    // Animate the score counter
    let startTime = Date.now();
    const duration = 1500; // 1.5 seconds

    const animationFrame = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function
      const easeOutQuad = (t: number) => t * (2 - t);
      const easedProgress = easeOutQuad(progress);

      // Update score
      const currentScore = Math.round(
        startScore + (targetScore - startScore) * easedProgress,
      );
      setDisplayScore(currentScore);

      // Continue animation if not complete
      if (progress < 1) {
        requestAnimationFrame(animationFrame);
      } else {
        setIsAnimating(false);
      }
    };

    // Start animation
    requestAnimationFrame(animationFrame);
  }, [
    data,
    isLoading,
    error,
    controls,
    circumference,
    displayScore,
    isAnimating,
  ]);

  function toggleDropdown() {
    setIsOpen(!isOpen);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  // Loading state
  if (isLoading) {
    return (
      <motion.div
        className="rounded-2xl border border-gray-200 bg-gradient-to-b from-gray-50 to-white p-5 transition-all duration-500 sm:p-6 dark:border-gray-800 dark:from-gray-900/10 dark:to-gray-900"
        style={{ height: "450px" }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 animate-pulse rounded-lg bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-6 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div className="h-8 w-8 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
        </div>

        <div className="flex h-[calc(100%-50px)] flex-col items-center justify-center">
          <div className="relative mb-6 h-56 w-56">
            <div className="h-full w-full animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div className="w-40 animate-pulse rounded bg-gray-200 p-2 text-center dark:bg-gray-700"></div>
          <div className="mt-2 w-32 animate-pulse rounded bg-gray-200 p-1 text-center dark:bg-gray-700"></div>
        </div>
      </motion.div>
    );
  }

  // Error state
  if (error) {
    return (
      <motion.div
        className="rounded-2xl border border-red-200 bg-gradient-to-b from-red-50 to-white p-5 transition-all duration-500 sm:p-6 dark:border-red-800 dark:from-red-900/10 dark:to-gray-900"
        style={{ height: "450px" }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#ef4444"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z" />
                <path d="M12 12v4" />
                <path d="M12 8h.01" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
              Threat Score
            </h3>
          </div>
        </div>

        <div className="flex h-[calc(100%-50px)] flex-col items-center justify-center">
          <svg
            className="mb-4 h-16 w-16 text-red-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="mb-2 text-lg font-medium text-red-700 dark:text-red-400">
            Error Loading Threat Score
          </h3>
          <p className="mb-4 text-center text-sm text-red-600 dark:text-red-300">
            {error.message}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-lg bg-red-100 px-4 py-2 text-sm font-medium text-red-700 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50"
          >
            Retry
          </button>
        </div>
      </motion.div>
    );
  }

  // Get threat level from GraphQL data
  const threatLevel = data?.dashboardStats?.threatScore?.level || "LOW";

  return (
    <motion.div
      className={`rounded-2xl border border-gray-200 bg-gradient-to-b ${bgGradient} p-5 transition-all duration-500 sm:p-6 dark:border-gray-800`}
      style={{ height: "450px" }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <SvgFilter />
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div
            className="flex h-8 w-8 items-center justify-center rounded-lg"
            style={{ backgroundColor: `${color}20` }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z" />
              <path d="M12 12v4" />
              <path d="M12 8h.01" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Threat Score
          </h3>
        </div>
        <div className="relative">
          <button
            onClick={toggleDropdown}
            className="flex h-8 w-8 items-center justify-center rounded-full text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-white"
          >
            <MoreDotIcon />
          </button>
          <Dropdown
            isOpen={isOpen}
            onClose={closeDropdown}
            className="shadow-theme-md absolute right-0 mt-2 w-40 rounded-lg border border-gray-200 bg-white p-2 dark:border-gray-700 dark:bg-gray-800"
          >
            <DropdownItem
              onItemClick={() => {
                refetch();
                closeDropdown();
              }}
              className="flex items-center gap-2 rounded-md px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
            >
              <span>Refresh</span>
            </DropdownItem>
            <DropdownItem
              onItemClick={closeDropdown}
              className="flex items-center gap-2 rounded-md px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
            >
              <span>Export</span>
            </DropdownItem>
          </Dropdown>
        </div>
      </div>

      <div className="flex h-[calc(100%-50px)] flex-col items-center justify-center">
        {/* Circular gauge */}
        <div
          className="relative mb-6 h-56 w-56"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <svg className="h-full w-full" viewBox="0 0 100 100">
            {/* Background circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="#e4e7ec"
              strokeWidth="10"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset="0"
              transform="rotate(-90 50 50)"
            />
            {/* Foreground circle - animated */}
            <motion.circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke={color}
              strokeWidth="10"
              strokeLinecap="round"
              strokeDasharray={circumference}
              initial={{ strokeDashoffset: circumference }}
              animate={controls}
              style={{
                filter: isHovered ? filter : "none",
                transition: "filter 0.3s ease",
              }}
              transform="rotate(-90 50 50)"
            />

            {/* Central score display */}
            <g>
              <text
                x="50"
                y="45"
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="28"
                fontWeight="700"
                fill="#1D2939"
                className="dark:fill-white"
                style={{
                  textShadow: `0 0 10px ${color}40`,
                }}
              >
                {Math.round(displayScore)}
              </text>
              <text
                x="50"
                y="60"
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="12"
                fontWeight="500"
                fill="#667085"
                className="dark:fill-gray-400"
              >
                out of 100
              </text>
            </g>
          </svg>

          {/* Pulse effect overlay */}
          <AnimatePresence>
            {isHovered && (
              <motion.div
                className="absolute inset-0 rounded-full"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{
                  opacity: 0.15,
                  scale: 1.1,
                  backgroundColor: color,
                }}
                exit={{ opacity: 0, scale: 1.2 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
            )}
          </AnimatePresence>
        </div>

        <div className="text-center">
          <div className="mb-3 flex items-center justify-center gap-2">
            {/* Status indicator circle with fixed dimensions and important styles */}
            <div
              style={{
                backgroundColor: color,
                boxShadow: `0 0 5px ${color}`,
                width: "12px",
                height: "12px",
                borderRadius: "50%",
                display: "block",
                flexShrink: 0,
                marginRight: "4px",
              }}
            />
            <span className="text-lg font-semibold" style={{ color: color }}>
              Security Status: {threatLevel}
            </span>
          </div>
          <div className="flex items-center justify-center gap-1 text-sm text-gray-500 dark:text-gray-400">
            <span
              className={`flex items-center font-medium ${
                isImproved
                  ? "text-success-600 dark:text-success-500"
                  : "text-error-600 dark:text-error-500"
              }`}
            >
              {isImproved ? (
                <ArrowDownIcon className="mr-1" />
              ) : (
                <ArrowUpIcon className="mr-1" />
              )}
              {data?.dashboardStats?.threatScore?.percentageChange
                ? Math.abs(
                    data.dashboardStats.threatScore.percentageChange,
                  ).toFixed(1)
                : "0.0"}
              %
            </span>
            <span>from last scan</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
