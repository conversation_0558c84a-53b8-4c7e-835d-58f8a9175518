{"name": "darkops", "version": "1.0.0", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"prettier": "^3.4.2", "turbo": "^2.4.0", "typescript": "5.7.3"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.1.21", "workspaces": ["apps/*", "packages/*", "external/*"], "dependencies": {"@neondatabase/serverless": "^0.10.4", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.5", "axios": "^1.9.0", "drizzle-orm": "^0.39.1", "exceljs": "^4.4.0", "sonner": "^2.0.3", "zustand": "^5.0.4"}}