{"static_analysis": {"File Info": {"ID": "****************************************************************", "Type": "file", "Extension": "apk", "Package": "org.xmlpush.v3"}, "Dates": {"First Submission": 1574826605}, "Analysis Stats": {"malicious": 33, "suspicious": 0, "undetected": 34, "harmless": 0, "timeout": 0, "confirmed-timeout": 0, "failure": 1, "type-unsupported": 8}, "Activities": ["org.xmlpush.v3.StartVersion"], "Services": ["org.xmlpush.v3.AlarmManager", "org.xmlpush.v3.EventBasedService", "org.xmlpush.v3.Services", "org.xmlpush.v3.eventbased.ReceiverService", "org.xmlpush.v3.schedule.SchedulerServices"], "Permissions": null, "Analysis Results": {"Lionic": {"method": "blacklist", "engine_name": "<PERSON><PERSON>", "engine_version": "8.16", "engine_update": "20250508", "category": "malicious", "result": "Trojan.AndroidOS.FinSpy.C!c"}, "Elastic": {"method": "blacklist", "engine_name": "Elastic", "engine_version": "4.0.203", "engine_update": "20250505", "category": "undetected", "result": null}, "MicroWorld-eScan": {"method": "blacklist", "engine_name": "MicroWorld-eScan", "engine_version": "14.0.409.0", "engine_update": "20250508", "category": "undetected", "result": null}, "CTX": {"method": "blacklist", "engine_name": "CTX", "engine_version": "2024.8.29.1", "engine_update": "20250508", "category": "malicious", "result": "apk.trojan.finspy"}, "CAT-QuickHeal": {"method": "blacklist", "engine_name": "CAT-QuickHeal", "engine_version": "22.00", "engine_update": "20250507", "category": "malicious", "result": "Android.TechfuCAD.A"}, "Skyhigh": {"method": "blacklist", "engine_name": "Skyhigh", "engine_version": "v2021.2.0+4045", "engine_update": "20250507", "category": "malicious", "result": "Artemis!Trojan"}, "McAfee": {"method": "blacklist", "engine_name": "McAfee", "engine_version": "6.0.6.653", "engine_update": "20250507", "category": "malicious", "result": "Artemis!79BA96848428"}, "Malwarebytes": {"method": "blacklist", "engine_name": "Malwarebytes", "engine_version": "4.5.5.54", "engine_update": "20250508", "category": "undetected", "result": null}, "Zillya": {"method": "blacklist", "engine_name": "Zillya", "engine_version": "2.0.0.5355", "engine_update": "20250507", "category": "malicious", "result": "Trojan.TechFu.Android.11"}, "Sangfor": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "2.22.3.0", "engine_update": "20250506", "category": "undetected", "result": null}, "Trustlook": {"method": "blacklist", "engine_name": "Trustlook", "engine_version": "1.0", "engine_update": "20250508", "category": "malicious", "result": "Android.Malware.Spyware"}, "Alibaba": {"method": "blacklist", "engine_name": "Alibaba", "engine_version": "0.3.0.5", "engine_update": "20190527", "category": "undetected", "result": null}, "K7GW": {"method": "blacklist", "engine_name": "K7GW", "engine_version": "12.234.55688", "engine_update": "20250508", "category": "malicious", "result": "Spyware ( 0055c6271 )"}, "K7AntiVirus": {"method": "blacklist", "engine_name": "K7AntiVirus", "engine_version": "12.234.55686", "engine_update": "20250508", "category": "undetected", "result": null}, "Baidu": {"method": "blacklist", "engine_name": "Baidu", "engine_version": "1.0.0.2", "engine_update": "20190318", "category": "undetected", "result": null}, "VirIT": {"method": "blacklist", "engine_name": "VirIT", "engine_version": "9.5.949", "engine_update": "20250507", "category": "undetected", "result": null}, "SymantecMobileInsight": {"method": "blacklist", "engine_name": "SymantecMobileInsight", "engine_version": "2.0", "engine_update": "20250124", "category": "malicious", "result": "AppRisk:Generisk"}, "Symantec": {"method": "blacklist", "engine_name": "Symantec", "engine_version": "1.22.0.0", "engine_update": "20250508", "category": "malicious", "result": "Trojan.Gen.2"}, "ESET-NOD32": {"method": "blacklist", "engine_name": "ESET-NOD32", "engine_version": "31164", "engine_update": "20250508", "category": "malicious", "result": "Android/Spy.TechFu.C"}, "Cynet": {"method": "blacklist", "engine_name": "Cynet", "engine_version": "4.0.3.4", "engine_update": "20250508", "category": "undetected", "result": null}, "TrendMicro-HouseCall": {"method": "blacklist", "engine_name": "TrendMicro-HouseCall", "engine_version": "24.550.0.1002", "engine_update": "20250508", "category": "malicious", "result": "TROJ_FRS.0NA103IS20"}, "Avast": {"method": "blacklist", "engine_name": "<PERSON><PERSON>", "engine_version": "23.9.8494.0", "engine_update": "20250508", "category": "malicious", "result": "Android:FinSpy-J [Spy]"}, "ClamAV": {"method": "blacklist", "engine_name": "ClamAV", "engine_version": "1.4.2.0", "engine_update": "20250508", "category": "undetected", "result": null}, "Kaspersky": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "22.0.1.28", "engine_update": "20250508", "category": "malicious", "result": "HEUR:Trojan-Spy.AndroidOS.TechFu.a"}, "BitDefender": {"method": "blacklist", "engine_name": "BitDefender", "engine_version": "7.2", "engine_update": "20250508", "category": "undetected", "result": null}, "NANO-Antivirus": {"method": "blacklist", "engine_name": "NANO-Antivirus", "engine_version": "1.0.170.26531", "engine_update": "20250508", "category": "malicious", "result": "Trojan.Android.Finspy.hxtmbf"}, "SUPERAntiSpyware": {"method": "blacklist", "engine_name": "SUPERAntiSpyware", "engine_version": "5.6.0.1032", "engine_update": "20250507", "category": "undetected", "result": null}, "Tencent": {"method": "blacklist", "engine_name": "Tencent", "engine_version": "1.0.0.1", "engine_update": "20250508", "category": "malicious", "result": "a.privacy.FinSpy"}, "Emsisoft": {"method": "blacklist", "engine_name": "Emsisoft", "engine_version": "2024.8.0.61147", "engine_update": "20250508", "category": "undetected", "result": null}, "F-Secure": {"method": "blacklist", "engine_name": "F-Secure", "engine_version": "18.10.1547.307", "engine_update": "20250508", "category": "undetected", "result": null}, "DrWeb": {"method": "blacklist", "engine_name": "DrWeb", "engine_version": "7.0.67.2170", "engine_update": "20250508", "category": "malicious", "result": "Android.Finspy.2.origin"}, "VIPRE": {"method": "blacklist", "engine_name": "VIPRE", "engine_version": "********", "engine_update": "20250508", "category": "undetected", "result": null}, "TrendMicro": {"method": "blacklist", "engine_name": "TrendMicro", "engine_version": "24.550.0.1002", "engine_update": "20250508", "category": "malicious", "result": "TROJ_FRS.0NA103IS20"}, "CMC": {"method": "blacklist", "engine_name": "CMC", "engine_version": "2.4.2022.1", "engine_update": "20250508", "category": "undetected", "result": null}, "Sophos": {"method": "blacklist", "engine_name": "<PERSON>ph<PERSON>", "engine_version": "*******", "engine_update": "20250508", "category": "malicious", "result": "Andr/Xgen-AQT"}, "huorong": {"method": "blacklist", "engine_name": "huo<PERSON>", "engine_version": "fad55c7:fad55c7:b8c842c:b8c842c", "engine_update": "20250507", "category": "malicious", "result": "Trojan/Generic!D68133689F2746DD"}, "Avast-Mobile": {"method": "blacklist", "engine_name": "Avast-Mobile", "engine_version": "250508-00", "engine_update": "20250508", "category": "malicious", "result": "Android:Evo-gen [Trj]"}, "Jiangmin": {"method": "blacklist", "engine_name": "<PERSON><PERSON>", "engine_version": "16.0.100", "engine_update": "20250507", "category": "malicious", "result": "TrojanSpy.AndroidOS.dics"}, "Webroot": {"method": "blacklist", "engine_name": "Webroot", "engine_version": "1.9.0.8", "engine_update": "20250227", "category": "undetected", "result": null}, "Varist": {"method": "blacklist", "engine_name": "V<PERSON><PERSON>", "engine_version": "6.6.1.3", "engine_update": "20250508", "category": "malicious", "result": "ABRisk.ZTTR-7"}, "Avira": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "8.3.3.20", "engine_update": "20250508", "category": "undetected", "result": null}, "Antiy-AVL": {"method": "blacklist", "engine_name": "Antiy-AVL", "engine_version": "3.0", "engine_update": "20250508", "category": "undetected", "result": null}, "Kingsoft": {"method": "blacklist", "engine_name": "Kingsoft", "engine_version": "None", "engine_update": "20250508", "category": "undetected", "result": null}, "Microsoft": {"method": "blacklist", "engine_name": "Microsoft", "engine_version": "1.1.25030.1", "engine_update": "20250508", "category": "malicious", "result": "Trojan:AndroidOS/FinSpy.A!MTB"}, "Gridinsoft": {"method": "blacklist", "engine_name": "Gridinsoft", "engine_version": "1.0.216.174", "engine_update": "20250508", "category": "undetected", "result": null}, "Xcitium": {"method": "blacklist", "engine_name": "Xcitium", "engine_version": "37711", "engine_update": "20250508", "category": "malicious", "result": "Malware@#1kc4izanttita"}, "Arcabit": {"method": "blacklist", "engine_name": "Arcabit", "engine_version": "2022.0.0.18", "engine_update": "20250508", "category": "undetected", "result": null}, "ViRobot": {"method": "blacklist", "engine_name": "ViRobot", "engine_version": "2014.3.20.0", "engine_update": "20250508", "category": "undetected", "result": null}, "ZoneAlarm": {"method": "blacklist", "engine_name": "ZoneAlarm", "engine_version": "6.15-102623285", "engine_update": "20250508", "category": "malicious", "result": "Andr/Xgen-AQT"}, "GData": {"method": "blacklist", "engine_name": "GData", "engine_version": "GD:27.40251AVA:64.29160", "engine_update": "20250508", "category": "undetected", "result": null}, "Google": {"method": "blacklist", "engine_name": "Google", "engine_version": "1746703838", "engine_update": "20250508", "category": "malicious", "result": "Detected"}, "BitDefenderFalx": {"method": "blacklist", "engine_name": "BitDefenderFalx", "engine_version": "2.0.936", "engine_update": "20250416", "category": "malicious", "result": "Android.Riskware.Agent.ALU"}, "AhnLab-V3": {"method": "blacklist", "engine_name": "AhnLab-V3", "engine_version": "3.27.2.10550", "engine_update": "20250508", "category": "malicious", "result": "PUP/Android.Agent.600083"}, "Acronis": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON><PERSON>", "engine_version": "1.2.0.121", "engine_update": "20240328", "category": "undetected", "result": null}, "ALYac": {"method": "blacklist", "engine_name": "ALYac", "engine_version": "2.0.0.10", "engine_update": "20250508", "category": "undetected", "result": null}, "TACHYON": {"method": "blacklist", "engine_name": "TACHYON", "engine_version": "2025-05-08.02", "engine_update": "20250508", "category": "undetected", "result": null}, "VBA32": {"method": "blacklist", "engine_name": "VBA32", "engine_version": "5.3.2", "engine_update": "20250508", "category": "undetected", "result": null}, "Zoner": {"method": "blacklist", "engine_name": "Zoner", "engine_version": "2.2.2.0", "engine_update": "20250508", "category": "undetected", "result": null}, "Rising": {"method": "blacklist", "engine_name": "Rising", "engine_version": "25.0.0.28", "engine_update": "20250508", "category": "undetected", "result": null}, "Yandex": {"method": "blacklist", "engine_name": "Yandex", "engine_version": "5.5.2.24", "engine_update": "20250508", "category": "undetected", "result": null}, "Ikarus": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "********", "engine_update": "20250508", "category": "malicious", "result": "Trojan.AndroidOS.Obfus"}, "MaxSecure": {"method": "blacklist", "engine_name": "MaxSecure", "engine_version": "1.0.0.1", "engine_update": "20250508", "category": "undetected", "result": null}, "Fortinet": {"method": "blacklist", "engine_name": "Fortinet", "engine_version": "7.0.30.0", "engine_update": "20250508", "category": "malicious", "result": "Android/TechFu.A!tr"}, "AVG": {"method": "blacklist", "engine_name": "AVG", "engine_version": "23.9.8494.0", "engine_update": "20250508", "category": "malicious", "result": "Android:FinSpy-J [Spy]"}, "Panda": {"method": "blacklist", "engine_name": "Panda", "engine_version": "4.6.4.2", "engine_update": "20250508", "category": "undetected", "result": null}, "CrowdStrike": {"method": "blacklist", "engine_name": "CrowdStrike", "engine_version": "1.0", "engine_update": "20230417", "category": "undetected", "result": null}, "alibabacloud": {"method": "blacklist", "engine_name": "alibabacloud", "engine_version": "2.2.0", "engine_update": "20250321", "category": "malicious", "result": "Trojan[spy]:Android/TechFu.C"}, "Bkav": {"method": "blacklist", "engine_name": "Bkav", "engine_version": "2.0.0.1", "engine_update": "20250508", "category": "failure", "result": null}, "DeepInstinct": {"method": "blacklist", "engine_name": "DeepInstinct", "engine_version": "5.0.0.8", "engine_update": "20250508", "category": "type-unsupported", "result": null}, "McAfeeD": {"method": "blacklist", "engine_name": "McAfeeD", "engine_version": "1.2.0.7977", "engine_update": "20250508", "category": "type-unsupported", "result": null}, "Trapmine": {"method": "blacklist", "engine_name": "Trap<PERSON>", "engine_version": "4.0.4.0", "engine_update": "20250417", "category": "type-unsupported", "result": null}, "Paloalto": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON><PERSON>", "engine_version": "0.9.0.1003", "engine_update": "20250508", "category": "type-unsupported", "result": null}, "APEX": {"method": "blacklist", "engine_name": "APEX", "engine_version": "6.652", "engine_update": "20250507", "category": "type-unsupported", "result": null}, "Cylance": {"method": "blacklist", "engine_name": "Cylance", "engine_version": "3.0.0.0", "engine_update": "20250424", "category": "type-unsupported", "result": null}, "SentinelOne": {"method": "blacklist", "engine_name": "SentinelOne", "engine_version": "25.1.1.1", "engine_update": "20250114", "category": "type-unsupported", "result": null}, "tehtris": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": null, "engine_update": "20250508", "category": "type-unsupported", "result": null}}, "Static Analysis": {"data": [{"id": "****************************************************************_Dr.Web vxCube", "type": "file_behaviour", "links": {"self": "https://www.virustotal.com/api/v3/file_behaviours/****************************************************************_Dr.Web vxCube"}, "attributes": {"has_pcap": false, "files_dropped": [{"path": "data/dalvik-cache/x86_64/system@<EMAIL>@classes.dex", "sha256": "c9d17d210ef2d5dfd353655533af0a687e437ef5a623041866cf4b76b2e5298a", "type": "ELF"}, {"path": "data/dalvik-cache/x86_64/system@<EMAIL>@classes.dex.flock (deleted)", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"path": "data/data/org.xmlpush.v3/cache/e648569b", "sha256": "d5b50bb7b0bed3964b51c2a3a0361ecfa74af38bc3dd626fee40780e1dd0ac45", "type": "ELF"}, {"path": "data/data/org.xmlpush.v3/cache/proc_auxv", "sha256": "63c2412998ca4ab541c2955bd4fa1dbb2d4e9adc6c9a0bf318f031412e08fa62"}, {"path": "data/data/org.xmlpush.v3/files/641ea378", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"path": "data/data/org.xmlpush.v3/files/RANDSEED.001", "sha256": "1ae4e00e38cd82c02cc53efd2d2fadb3132ee3967b4e882c68b90f0024e9a566"}, {"path": "data/data/org.xmlpush.v3/files/RANDSEED.002", "sha256": "8365469b93a5cea7c86f874121e42c710661142868bd5db92ce28e7cce7efda2"}, {"path": "data/data/org.xmlpush.v3/files/ac4109d9175fa6f8c0df1a", "sha256": "be2964740f7ae97dae8a806b2a3ce801625d2ef31e451f53e523eea51f2ed3f5"}], "has_evtx": false, "permissions_checked": [{"owner": "org.xmlpush.v3", "permission": "android.permission.INTERACT_ACROSS_USERS"}, {"owner": "org.xmlpush.v3", "permission": "android.permission.INTERACT_ACROSS_USERS_FULL"}], "command_executions": ["su", "/system/bin/log -p d -t su su invoked.", "/system/bin/log -p d -t su starting daemon client 10065 10065", "/system/bin/log -p d -t su connecting client 3136", "/system/bin/log -p d -t su connecting client 3264", "/system/bin/log -p d -t su client exited 0", "/system/bin/log -p d -t su connecting client 3361", "/system/bin/log -p d -t su connecting client 3454"], "services_started": ["org.xmlpush.v3/org.xmlpush.v3.Services", "org.xmlpush.v3/org.xmlpush.v3.EventBasedService"], "system_property_lookups": ["dalvik.vm.usejitprofiles", "debug.force_rtl", "debug.second-display.pkg", "drw.device_id", "drw.network_operator", "drw.subscriber_id", "gsm.operator.isroaming", "persist.sys.apps_statistics", "persist.sys.timezone", "persist.sys.ui.hw", "ro.build.fingerprint", "ro.gfx.driver.0", "ro.kernel.android.tracing", "ro.product.brand", "ro.product.device", "ro.product.manufacturer", "ro.product.model", "ro.product.name", "ro.serialno"], "analysis_date": 1574844185, "dns_lookups": [{"resolved_ips": ["**************"], "hostname": "android.googleapis.com"}, {"resolved_ips": ["**************", "***************", "**************", "**************", "**************", "***************", "**************", "*************"], "hostname": "android.clients.google.com"}, {"resolved_ips": ["**************"], "hostname": "instantmessaging-pa.googleapis.com"}, {"resolved_ips": ["***************"], "hostname": "play.googleapis.com"}, {"resolved_ips": ["*************"], "hostname": "fonts.gstatic.com"}, {"resolved_ips": ["*************"], "hostname": "safebrowsing.googleapis.com"}], "last_modification_date": 1574844374, "verdicts": ["CLEAN"], "crypto_algorithms_observed": ["AES-CBC-PKCS5Padding"], "sandbox_name": "Dr.Web vxCube", "tags": ["SUDO", "CHECKS_GPS"], "has_memdump": false, "has_html_report": true, "invokes": ["main.android.app.ActivityThread", "getScript.java.util.Locale", "getExtensionKeys.java.util.Locale", "getPackageManager.android.content.Context", "setComponentEnabledSetting.android.content.pm.PackageManager", "get.android.os.SystemProperties", "stopMethodTracing.android.os.Debug", "stopNativeTracing.android.os.Debug", "getCellLocation.com.android.internal.telephony.ITelephony", "values.android.net.NetworkInfo$State"], "calls_highlighted": ["dalvik.system.BaseDexClassLoader.<init>", "dalvik.system.DexFile.openDexFileNative", "android.content.pm.PackageManager.setComponentEnabledSetting", "android.app.ContextImpl.checkPermission", "javax.crypto.Cipher.doFinal", "android.telephony.TelephonyManager.getDeviceId", "android.telephony.TelephonyManager.getCurrentPhoneType", "android.app.ContextImpl.getPackageCodePath", "android.telephony.TelephonyManager.getSimState", "android.telephony.TelephonyManager.getNetworkOperator", "android.location.LocationManager.getLastKnownLocation", "android.telephony.TelephonyManager.getSubscriberId", "android.telephony.TelephonyManager.getCellLocation", "android.telephony.TelephonyManager.getITelephony", "java.lang.ProcessBuilder.exec"]}}, {"id": "****************************************************************_Tencent HABO", "type": "file_behaviour", "links": {"self": "https://www.virustotal.com/api/v3/file_behaviours/****************************************************************_Tencent HABO"}, "attributes": {"files_opened": ["/data/data/org.xmlpush.v3/files/e1a698eacec5088bc0df1a", "/data/app/org.xmlpush.v3-1.apk", "unknown", "/data/data/org.xmlpush.v3/files/a20545fc259fa99c0df1a", "/data/data/org.xmlpush.v3/files/df905ab44a24ea67c0df1a", "/data/data/org.xmlpush.v3/files/9a25ab68db56c6e6c0df1a", "/data/data/org.xmlpush.v3/files/c4afc626986b22b7c0df1a", "/data/data/org.xmlpush.v3/files/ca7d164587bd65a2c0df1a", "/data/data/org.xmlpush.v3/files/d584f16220304e15c0df1a", "/data/data/org.xmlpush.v3/files/933295272abaa120c0df1a", "/data/data/org.xmlpush.v3/files/66363889c872172c0df1a", "/data/data/org.xmlpush.v3/files/f8fb5b0ffa0f782c0df1a", "/data/data/org.xmlpush.v3/files/be093dc99d5ecdecc0df1a", "/data/data/org.xmlpush.v3/files/dc373799917e0ba1c0df1a"], "has_pcap": false, "modules_loaded": ["/data/app-lib/org.xmlpush.v3-1/libhelper.so"], "has_evtx": false, "processes_tree": [{"process_id": "8145", "name": "zygote", "children": [{"process_id": "15531", "name": "org.xmlpush.v3"}]}, {"process_id": "8145", "name": "zygote", "children": [{"process_id": "15529", "name": "org.xmlpush.v3"}]}], "services_started": ["org.xmlpush.v3/org.xmlpush.v3.Services", "org.xmlpush.v3/org.xmlpush.v3.EventBasedService", "org.xmlpush.v3/org.xmlpush.v3.eventbased.ReceiverService"], "system_property_lookups": ["ro.serialno", "ro.product.model", "ro.product.brand", "ro.product.name", "ro.product.device", "ro.product.manufacturer"], "files_deleted": ["/data/data/org.xmlpush.v3/cache/e648569b"], "analysis_date": 1574906890, "last_modification_date": 1636164464, "behash": "1a0afdd3f0d32c6dd6ddee527d2a76b9", "crypto_algorithms_observed": ["AES/CBC/PKCS5Padding"], "sandbox_name": "Tencent <PERSON>", "has_memdump": false, "has_html_report": true, "files_written": ["/data/data/org.xmlpush.v3/files/e1a698eacec5088bc0df1a", "unknown", "/data/data/org.xmlpush.v3/files/RANDSEED.001", "/data/data/org.xmlpush.v3/cache/e648569b", "/data/data/org.xmlpush.v3/files/a20545fc259fa99c0df1a", "/data/data/org.xmlpush.v3/files/df905ab44a24ea67c0df1a", "/data/data/org.xmlpush.v3/files/9a25ab68db56c6e6c0df1a", "/data/data/org.xmlpush.v3/files/c4afc626986b22b7c0df1a", "/data/data/org.xmlpush.v3/files/ca7d164587bd65a2c0df1a", "/data/data/org.xmlpush.v3/files/d584f16220304e15c0df1a", "/data/data/org.xmlpush.v3/files/933295272abaa120c0df1a", "/data/data/org.xmlpush.v3/files/66363889c872172c0df1a", "/data/data/org.xmlpush.v3/files/f8fb5b0ffa0f782c0df1a", "/data/data/org.xmlpush.v3/files/be093dc99d5ecdecc0df1a", "/data/data/org.xmlpush.v3/files/dc373799917e0ba1c0df1a"], "signals_hooked": ["android.intent.action.SCREEN_ON", "android.intent.action.SCREEN_OFF", "SENT_SMS_ACTION", "DELIVERED_SMS_ACTION"]}}, {"id": "****************************************************************_VirusTotal R2DBox", "type": "file_behaviour", "links": {"self": "https://www.virustotal.com/api/v3/file_behaviours/****************************************************************_VirusTotal R2DBox"}, "attributes": {"files_opened": ["/sys/qemu_trace/state"], "has_pcap": true, "modules_loaded": ["META-INF/services/java.nio.channels.spi.SelectorProvider"], "ip_traffic": [{"transport_layer_protocol": "TCP", "destination_ip": "*************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "************", "destination_port": 443}], "has_evtx": false, "analysis_date": 2, "last_modification_date": **********, "behash": "13612381597ac9d26cc0cd5f6c03e552", "sandbox_name": "VirusTotal R2DBox", "tags": ["RUNTIME_MODULES", "REFLECTION", "TELEPHONY"], "has_memdump": false, "has_html_report": true, "files_written": ["/sys/qemu_trace/state"], "invokes": ["android.os.SystemProperties.get", "android.os.Debug.stopMethodTracing", "android.os.Debug.stopNativeTracing"]}}, {"id": "****************************************************************_Zenbox android", "type": "file_behaviour", "links": {"self": "https://www.virustotal.com/api/v3/file_behaviours/****************************************************************_Zenbox android"}, "attributes": {"has_pcap": true, "ip_traffic": [{"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "*************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 80}, {"transport_layer_protocol": "TCP", "destination_ip": "***************", "destination_port": 443}], "has_evtx": false, "mitre_attack_techniques": [{"refs": [{"ref": "#signature_matches", "value": "1052"}], "signature_description": "Queries a list of installed applications", "id": "T1418", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1041"}], "signature_description": "Obfuscates method names", "id": "T1406", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1013"}], "signature_description": "Checks an internet connection is available", "id": "T1421", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "263"}], "signature_description": "May try to detect the virtual machine to hinder analysis (VM artifact strings found in memory)", "id": "T1518.001", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1176"}], "signature_description": "Has permission to query the current location", "id": "T1430", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1015"}], "signature_description": "Monitors incoming Phone calls", "id": "T1433", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1016"}], "signature_description": "Monitors outgoing Phone calls", "id": "T1433", "severity": "IMPACT_SEVERITY_LOW"}, {"refs": [{"ref": "#signature_matches", "value": "1019"}], "signature_description": "Has permission to record audio in the background", "id": "T1429", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1013"}], "signature_description": "Checks an internet connection is available", "id": "T1507", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "625"}], "signature_description": "Uses HTTPS", "id": "T1573", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "625"}], "signature_description": "Uses HTTPS", "id": "T1071", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1155"}], "signature_description": "Has permissions to monitor, redirect and/or block calls", "id": "T1449", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1017"}], "signature_description": "Has permission to send SMS in the background", "id": "T1449", "severity": "IMPACT_SEVERITY_INFO"}, {"refs": [{"ref": "#signature_matches", "value": "1017"}], "signature_description": "Has permission to send SMS in the background", "id": "T1448", "severity": "IMPACT_SEVERITY_INFO"}], "signature_matches": [{"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.WRITE_SMS"], "id": "1166", "description": "Has permission to write to the SMS storage"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["Landroid/app/KeyguardManager;", "inKeyguardRestrictedInputMode"], "id": "1147", "description": "May access the Android keyguard (lock screen)"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.PACKAGE_USAGE_STATS"], "id": "1002", "description": "Requests permissions only permitted to signed APKs"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.GET_ACCOUNTS"], "id": "1152", "description": "Has permissions to create, read or change account settings (inlcuding account password settings)"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.RECEIVE_SMS"], "id": "1162", "description": "Has permission to receive SMS in the background"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.content.pm.PackageManager.getInstalledApplications"], "id": "1052", "description": "Queries a list of installed applications"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "id": "1176", "description": "Has permission to query the current location"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["HTTP traffic on port 35426 -> 443", "HTTP traffic on port 55092 -> 443", "HTTP traffic on port 35428 -> 443", "HTTP traffic on port 35432 -> 443", "HTTP traffic on port 35396 -> 443", "HTTP traffic on port 443 -> 56016", "HTTP traffic on port 443 -> 34036", "HTTP traffic on port 443 -> 35400", "HTTP traffic on port 34036 -> 443", "HTTP traffic on port 35418 -> 443", "HTTP traffic on port 443 -> 34174", "HTTP traffic on port 60152 -> 443", "HTTP traffic on port 443 -> 60152", "HTTP traffic on port 40236 -> 443", "HTTP traffic on port 34174 -> 443", "HTTP traffic on port 35400 -> 443", "HTTP traffic on port 35234 -> 443", "HTTP traffic on port 443 -> 55092", "HTTP traffic on port 35402 -> 443", "HTTP traffic on port 35404 -> 443", "HTTP traffic on port 34132 -> 443", "HTTP traffic on port 443 -> 40380", "HTTP traffic on port 443 -> 35404", "HTTP traffic on port 443 -> 35402", "HTTP traffic on port 35296 -> 443", "HTTP traffic on port 443 -> 35396", "HTTP traffic on port 35438 -> 443", "HTTP traffic on port 35420 -> 443", "HTTP traffic on port 35422 -> 443", "HTTP traffic on port 56016 -> 443", "HTTP traffic on port 40380 -> 443"], "id": "625", "description": "Uses HTTPS"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.READ_CONTACTS"], "id": "1159", "description": "Has permission to read contacts"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["***************", "***************", "***************", "***************"], "id": "7021", "description": "Connects to IPs without corresponding DNS lookups"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.intent.action.PHONE_STATE"], "id": "1015", "description": "Monitors incoming Phone calls"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["0%"], "id": "1041", "description": "Obfuscates method names"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.READ_SMS"], "id": "1161", "description": "Has permission to read the SMS storage"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.READ_PHONE_STATE"], "id": "1160", "description": "Has permission to read the phones state (phone number, device IDs, active call ect.)"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["http://schemas.android.com/apk/res/android"], "id": "238", "description": "URLs found in memory or binary data"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.READ_CALL_LOG"], "id": "1156", "description": "Has permission to read the call log"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["Lorg/xmlpush/v3/n/k/a;.b(Landroid/content/Context;Landroid/database/Cursor;)Lorg/xmlpush/v3/n/g;"], "id": "1039", "description": "Might use exploit to break dedexer tools"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.net.wifi.WifiManager.getConnectionInfo"], "id": "1013", "description": "Checks an internet connection is available"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.os.Environment.getExternalStorageDirectory"], "id": "1028", "description": "Accesses external storage location"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.os.Build.DEVICE", "android.os.Build.MODEL", "android.os.Build.MANUFACTURER", "android.os.Build$VERSION.RELEASE"], "id": "1036", "description": "Accesses android OS build fields"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["sus31.spyw.andAPK@0/251@0/0"], "id": "715", "description": "Classification label"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.CHANGE_WIFI_STATE"], "id": "1158", "description": "Has permission to change the WIFI configuration including connecting and disconnecting"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.RECORD_AUDIO"], "id": "1019", "description": "Has permission to record audio in the background"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.CAMERA", "android.permission.CHANGE_WIFI_STATE", "android.permission.INTERNET", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.PROCESS_OUTGOING_CALLS", "android.permission.READ_CALENDAR", "android.permission.READ_CONTACTS", "android.permission.READ_PHONE_STATE", "android.permission.READ_SMS", "android.permission.RECEIVE_SMS", "android.permission.RECORD_AUDIO", "android.permission.SEND_SMS", "android.permission.WAKE_LOCK", "android.permission.WRITE_CONTACTS", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.WRITE_SETTINGS", "android.permission.WRITE_SMS"], "id": "1001", "description": "Requests potentially dangerous permissions"}, {"severity": "IMPACT_SEVERITY_LOW", "match_data": ["android.intent.action.NEW_OUTGOING_CALL"], "id": "1016", "description": "Monitors outgoing Phone calls"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["ZQEMUTLi"], "id": "263", "description": "May try to detect the virtual machine to hinder analysis (VM artifact strings found in memory)"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.SEND_SMS"], "id": "1017", "description": "Has permission to send SMS in the background"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.CAMERA"], "id": "1157", "description": "Has permission to take photos"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.PROCESS_OUTGOING_CALLS"], "id": "1155", "description": "Has permissions to monitor, redirect and/or block calls"}, {"severity": "IMPACT_SEVERITY_INFO", "id": "1255", "description": "Unable to instrument or execute APK, no dynamic information has been logged"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.net.wifi.WifiInfo.getMacAddress"], "id": "1077", "description": "Queries the WIFI MAC address"}, {"severity": "IMPACT_SEVERITY_INFO", "match_data": ["android.permission.RECEIVE_BOOT_COMPLETED"], "id": "1010", "description": "Has permission to execute code after phone reboot"}], "analysis_date": 1688663197, "dns_lookups": [{"resolved_ips": ["***************", "***************", "***************", "***************"], "hostname": "android.googleapis.com"}, {"resolved_ips": ["***************"], "hostname": "connectivitycheck.gstatic.com"}, {"resolved_ips": ["***************", "***************", "***************", "***************"], "hostname": "infinitedata-pa.googleapis.com"}, {"resolved_ips": ["***************", "***************", "***************", "***************"], "hostname": "gmscompliance-pa.googleapis.com"}], "last_modification_date": 1688663293, "behash": "5378c97b156764f5e943de3b418cf337", "sandbox_name": "Zenbox android", "tags": ["OBFUSCATED"], "has_memdump": false, "has_html_report": true, "ids_alerts": [{"rule_category": "Misc activity", "alert_severity": "low", "rule_msg": "ET INFO Android Device Connectivity Check", "rule_raw": "alert http $HOME_NET any -> $EXTERNAL_NET any (msg:\"ET INFO Android Device Connectivity Check\"; flow:established,to_server; urilen:13; http.method; content:\"GET\"; http.uri; content:\"/generate_204\"; fast_pattern; endswith; http.host; content:\"connectivitycheck.gstatic.com\"; http.accept_enc; content:\"gzip\"; depth:4; endswith; http.header_names; content:!\"Cache\"; content:!\"Referer\"; classtype:misc-activity; sid:2036220; rev:4; metadata:affected_product Android, attack_target Mobile_Client, created_at 2018_09_14, deployment Perimeter, deployment Internal, former_category INFO, performance_impact Low, signature_severity Informational, tag Connectivity_Check, updated_at 2020_09_16;)", "alert_context": {"url": "http://connectivitycheck.gstatic.com/generate_204", "dest_port": 80, "hostname": "connectivitycheck.gstatic.com", "dest_ip": "***************"}, "rule_url": "https://rules.emergingthreats.net/", "rule_source": "Proofpoint Emerging Threats Open", "rule_id": "1:2036220"}], "ja3_digests": ["9b02ebd3a43b62d825e1ac605b621dc8", "cd08e31494f9531f560d64c695473da9", "33490b1d5377580b19f7f9b5849d7991", "9c815150ea821166faecf80757d8826a"], "http_conversations": [{"url": "http://connectivitycheck.gstatic.com/generate_204", "request_method": "GET", "response_status_code": 204, "request_headers": {"Host": "connectivitycheck.gstatic.com", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.32 Safari/537.36"}}]}}], "meta": {"count": 4}, "links": {"self": "https://www.virustotal.com/api/v3/files/****************************************************************/behaviours?limit=10"}}}, "security_analysis": {"ml_detection": {"result": "Malware"}, "virustotal_analysis": {"data": {"id": "NzliYTk2ODQ4NDI4MzM3ZTY4NWUxMGIwNmNjYzFjODk6MTc0NjczODU2NQ==", "type": "analysis", "links": {"self": "https://www.virustotal.com/api/v3/analyses/NzliYTk2ODQ4NDI4MzM3ZTY4NWUxMGIwNmNjYzFjODk6MTc0NjczODU2NQ==", "item": "https://www.virustotal.com/api/v3/files/****************************************************************"}, "attributes": {"stats": {"malicious": 33, "suspicious": 0, "undetected": 35, "harmless": 0, "timeout": 0, "confirmed-timeout": 0, "failure": 0, "type-unsupported": 8}, "status": "completed", "results": {"Bkav": {"method": "blacklist", "engine_name": "Bkav", "engine_version": "2.0.0.1", "engine_update": "20250508", "category": "undetected", "result": null}, "Lionic": {"method": "blacklist", "engine_name": "<PERSON><PERSON>", "engine_version": "8.16", "engine_update": "20250508", "category": "malicious", "result": "Trojan.AndroidOS.FinSpy.C!c"}, "Elastic": {"method": "blacklist", "engine_name": "Elastic", "engine_version": "4.0.203", "engine_update": "20250505", "category": "undetected", "result": null}, "MicroWorld-eScan": {"method": "blacklist", "engine_name": "MicroWorld-eScan", "engine_version": "14.0.409.0", "engine_update": "20250508", "category": "undetected", "result": null}, "CMC": {"method": "blacklist", "engine_name": "CMC", "engine_version": "2.4.2022.1", "engine_update": "20250508", "category": "undetected", "result": null}, "CAT-QuickHeal": {"method": "blacklist", "engine_name": "CAT-QuickHeal", "engine_version": "22.00", "engine_update": "20250507", "category": "malicious", "result": "Android.TechfuCAD.A"}, "Skyhigh": {"method": "blacklist", "engine_name": "Skyhigh", "engine_version": "v2021.2.0+4045", "engine_update": "20250508", "category": "malicious", "result": "Artemis!Trojan"}, "McAfee": {"method": "blacklist", "engine_name": "McAfee", "engine_version": "6.0.6.653", "engine_update": "20250508", "category": "malicious", "result": "Artemis!79BA96848428"}, "Malwarebytes": {"method": "blacklist", "engine_name": "Malwarebytes", "engine_version": "4.5.5.54", "engine_update": "20250508", "category": "undetected", "result": null}, "Zillya": {"method": "blacklist", "engine_name": "Zillya", "engine_version": "2.0.0.5356", "engine_update": "20250508", "category": "malicious", "result": "Trojan.TechFu.Android.11"}, "Sangfor": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "2.22.3.0", "engine_update": "20250506", "category": "undetected", "result": null}, "Trustlook": {"method": "blacklist", "engine_name": "Trustlook", "engine_version": "1.0", "engine_update": "20250508", "category": "malicious", "result": "Android.Malware.Spyware"}, "Alibaba": {"method": "blacklist", "engine_name": "Alibaba", "engine_version": "0.3.0.5", "engine_update": "20190527", "category": "undetected", "result": null}, "K7GW": {"method": "blacklist", "engine_name": "K7GW", "engine_version": "12.234.55693", "engine_update": "20250508", "category": "malicious", "result": "Spyware ( 0055c6271 )"}, "K7AntiVirus": {"method": "blacklist", "engine_name": "K7AntiVirus", "engine_version": "12.234.55691", "engine_update": "20250508", "category": "undetected", "result": null}, "Arcabit": {"method": "blacklist", "engine_name": "Arcabit", "engine_version": "2022.0.0.18", "engine_update": "20250508", "category": "undetected", "result": null}, "Baidu": {"method": "blacklist", "engine_name": "Baidu", "engine_version": "1.0.0.2", "engine_update": "20190318", "category": "undetected", "result": null}, "VirIT": {"method": "blacklist", "engine_name": "VirIT", "engine_version": "9.5.950", "engine_update": "20250508", "category": "undetected", "result": null}, "SymantecMobileInsight": {"method": "blacklist", "engine_name": "SymantecMobileInsight", "engine_version": "2.0", "engine_update": "20250124", "category": "malicious", "result": "AppRisk:Generisk"}, "Symantec": {"method": "blacklist", "engine_name": "Symantec", "engine_version": "1.22.0.0", "engine_update": "20250508", "category": "malicious", "result": "Trojan.Gen.2"}, "ESET-NOD32": {"method": "blacklist", "engine_name": "ESET-NOD32", "engine_version": "31166", "engine_update": "20250508", "category": "malicious", "result": "Android/Spy.TechFu.C"}, "TrendMicro-HouseCall": {"method": "blacklist", "engine_name": "TrendMicro-HouseCall", "engine_version": "24.550.0.1002", "engine_update": "20250508", "category": "malicious", "result": "TROJ_FRS.0NA103IS20"}, "Avast": {"method": "blacklist", "engine_name": "<PERSON><PERSON>", "engine_version": "23.9.8494.0", "engine_update": "20250508", "category": "malicious", "result": "Android:FinSpy-J [Spy]"}, "ClamAV": {"method": "blacklist", "engine_name": "ClamAV", "engine_version": "1.4.2.0", "engine_update": "20250508", "category": "undetected", "result": null}, "Kaspersky": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "22.0.1.28", "engine_update": "20250508", "category": "malicious", "result": "HEUR:Trojan-Spy.AndroidOS.TechFu.a"}, "BitDefender": {"method": "blacklist", "engine_name": "BitDefender", "engine_version": "7.2", "engine_update": "20250508", "category": "undetected", "result": null}, "NANO-Antivirus": {"method": "blacklist", "engine_name": "NANO-Antivirus", "engine_version": "1.0.170.26531", "engine_update": "20250508", "category": "malicious", "result": "Trojan.Android.Finspy.hxtmbf"}, "SUPERAntiSpyware": {"method": "blacklist", "engine_name": "SUPERAntiSpyware", "engine_version": "5.6.0.1032", "engine_update": "20250508", "category": "undetected", "result": null}, "Rising": {"method": "blacklist", "engine_name": "Rising", "engine_version": "25.0.0.28", "engine_update": "20250508", "category": "undetected", "result": null}, "Emsisoft": {"method": "blacklist", "engine_name": "Emsisoft", "engine_version": "2024.8.0.61147", "engine_update": "20250508", "category": "undetected", "result": null}, "F-Secure": {"method": "blacklist", "engine_name": "F-Secure", "engine_version": "18.10.1547.307", "engine_update": "20250508", "category": "undetected", "result": null}, "DrWeb": {"method": "blacklist", "engine_name": "DrWeb", "engine_version": "7.0.67.2170", "engine_update": "20250508", "category": "malicious", "result": "Android.Finspy.2.origin"}, "VIPRE": {"method": "blacklist", "engine_name": "VIPRE", "engine_version": "********", "engine_update": "20250508", "category": "undetected", "result": null}, "TrendMicro": {"method": "blacklist", "engine_name": "TrendMicro", "engine_version": "24.550.0.1002", "engine_update": "20250508", "category": "malicious", "result": "TROJ_FRS.0NA103IS20"}, "CTX": {"method": "blacklist", "engine_name": "CTX", "engine_version": "2024.8.29.1", "engine_update": "20250508", "category": "malicious", "result": "apk.trojan.finspy"}, "Sophos": {"method": "blacklist", "engine_name": "<PERSON>ph<PERSON>", "engine_version": "*******", "engine_update": "20250508", "category": "malicious", "result": "Andr/Xgen-AQT"}, "Ikarus": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "********", "engine_update": "20250508", "category": "malicious", "result": "Trojan.AndroidOS.Obfus"}, "GData": {"method": "blacklist", "engine_name": "GData", "engine_version": "GD:27.40255AVA:64.29162", "engine_update": "20250508", "category": "undetected", "result": null}, "Jiangmin": {"method": "blacklist", "engine_name": "<PERSON><PERSON>", "engine_version": "16.0.100", "engine_update": "20250507", "category": "malicious", "result": "TrojanSpy.AndroidOS.dics"}, "Webroot": {"method": "blacklist", "engine_name": "Webroot", "engine_version": "1.9.0.8", "engine_update": "20250227", "category": "undetected", "result": null}, "Google": {"method": "blacklist", "engine_name": "Google", "engine_version": "1746729040", "engine_update": "20250508", "category": "malicious", "result": "Detected"}, "Avira": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "8.3.3.20", "engine_update": "20250508", "category": "undetected", "result": null}, "Varist": {"method": "blacklist", "engine_name": "V<PERSON><PERSON>", "engine_version": "6.6.1.3", "engine_update": "20250508", "category": "malicious", "result": "ABRisk.ZTTR-7"}, "Antiy-AVL": {"method": "blacklist", "engine_name": "Antiy-AVL", "engine_version": "3.0", "engine_update": "20250508", "category": "undetected", "result": null}, "Kingsoft": {"method": "blacklist", "engine_name": "Kingsoft", "engine_version": "None", "engine_update": "20250508", "category": "undetected", "result": null}, "Gridinsoft": {"method": "blacklist", "engine_name": "Gridinsoft", "engine_version": "1.0.216.174", "engine_update": "20250508", "category": "undetected", "result": null}, "Xcitium": {"method": "blacklist", "engine_name": "Xcitium", "engine_version": "37713", "engine_update": "20250508", "category": "malicious", "result": "Malware@#1kc4izanttita"}, "Microsoft": {"method": "blacklist", "engine_name": "Microsoft", "engine_version": "1.1.25030.1", "engine_update": "20250508", "category": "malicious", "result": "Trojan:AndroidOS/FinSpy.A!MTB"}, "ViRobot": {"method": "blacklist", "engine_name": "ViRobot", "engine_version": "2014.3.20.0", "engine_update": "20250508", "category": "undetected", "result": null}, "ZoneAlarm": {"method": "blacklist", "engine_name": "ZoneAlarm", "engine_version": "6.15-102623289", "engine_update": "20250508", "category": "malicious", "result": "Andr/Xgen-AQT"}, "Avast-Mobile": {"method": "blacklist", "engine_name": "Avast-Mobile", "engine_version": "250508-00", "engine_update": "20250508", "category": "malicious", "result": "Android:Evo-gen [Trj]"}, "Cynet": {"method": "blacklist", "engine_name": "Cynet", "engine_version": "4.0.3.4", "engine_update": "20250508", "category": "undetected", "result": null}, "BitDefenderFalx": {"method": "blacklist", "engine_name": "BitDefenderFalx", "engine_version": "2.0.936", "engine_update": "20250416", "category": "malicious", "result": "Android.Riskware.Agent.ALU"}, "AhnLab-V3": {"method": "blacklist", "engine_name": "AhnLab-V3", "engine_version": "3.27.2.10550", "engine_update": "20250508", "category": "malicious", "result": "PUP/Android.Agent.600083"}, "Acronis": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON><PERSON>", "engine_version": "1.2.0.121", "engine_update": "20240328", "category": "undetected", "result": null}, "VBA32": {"method": "blacklist", "engine_name": "VBA32", "engine_version": "5.3.2", "engine_update": "20250508", "category": "undetected", "result": null}, "ALYac": {"method": "blacklist", "engine_name": "ALYac", "engine_version": "2.0.0.10", "engine_update": "20250508", "category": "undetected", "result": null}, "TACHYON": {"method": "blacklist", "engine_name": "TACHYON", "engine_version": "2025-05-08.02", "engine_update": "20250508", "category": "undetected", "result": null}, "Zoner": {"method": "blacklist", "engine_name": "Zoner", "engine_version": "2.2.2.0", "engine_update": "20250508", "category": "undetected", "result": null}, "Tencent": {"method": "blacklist", "engine_name": "Tencent", "engine_version": "1.0.0.1", "engine_update": "20250508", "category": "malicious", "result": "a.privacy.FinSpy"}, "Yandex": {"method": "blacklist", "engine_name": "Yandex", "engine_version": "5.5.2.24", "engine_update": "20250508", "category": "undetected", "result": null}, "huorong": {"method": "blacklist", "engine_name": "huo<PERSON>", "engine_version": "fad55c7:fad55c7:b8c842c:b8c842c", "engine_update": "20250507", "category": "malicious", "result": "Trojan/Generic!D68133689F2746DD"}, "MaxSecure": {"method": "blacklist", "engine_name": "MaxSecure", "engine_version": "1.0.0.1", "engine_update": "20250508", "category": "undetected", "result": null}, "Fortinet": {"method": "blacklist", "engine_name": "Fortinet", "engine_version": "7.0.30.0", "engine_update": "20250508", "category": "malicious", "result": "Android/TechFu.A!tr"}, "AVG": {"method": "blacklist", "engine_name": "AVG", "engine_version": "23.9.8494.0", "engine_update": "20250508", "category": "malicious", "result": "Android:FinSpy-J [Spy]"}, "Panda": {"method": "blacklist", "engine_name": "Panda", "engine_version": "4.6.4.2", "engine_update": "20250508", "category": "undetected", "result": null}, "CrowdStrike": {"method": "blacklist", "engine_name": "CrowdStrike", "engine_version": "1.0", "engine_update": "20231026", "category": "undetected", "result": null}, "alibabacloud": {"method": "blacklist", "engine_name": "alibabacloud", "engine_version": "2.2.0", "engine_update": "20250321", "category": "malicious", "result": "Trojan[spy]:Android/TechFu.C"}, "McAfeeD": {"method": "blacklist", "engine_name": "McAfeeD", "engine_version": "1.2.0.7977", "engine_update": "20250508", "category": "type-unsupported", "result": null}, "tehtris": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>", "engine_version": "v0.1.4", "engine_update": "20250508", "category": "type-unsupported", "result": null}, "APEX": {"method": "blacklist", "engine_name": "APEX", "engine_version": "6.652", "engine_update": "20250507", "category": "type-unsupported", "result": null}, "Paloalto": {"method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON><PERSON>", "engine_version": "0.9.0.1003", "engine_update": "20250508", "category": "type-unsupported", "result": null}, "Trapmine": {"method": "blacklist", "engine_name": "Trap<PERSON>", "engine_version": "4.0.4.0", "engine_update": "20250417", "category": "type-unsupported", "result": null}, "Cylance": {"method": "blacklist", "engine_name": "Cylance", "engine_version": "3.0.0.0", "engine_update": "20250424", "category": "type-unsupported", "result": null}, "SentinelOne": {"method": "blacklist", "engine_name": "SentinelOne", "engine_version": "25.1.1.1", "engine_update": "20250114", "category": "type-unsupported", "result": null}, "DeepInstinct": {"method": "blacklist", "engine_name": "DeepInstinct", "engine_version": "5.0.0.8", "engine_update": "20250508", "category": "type-unsupported", "result": null}}, "date": 1746738565}}, "meta": {"file_info": {"sha256": "****************************************************************", "md5": "79ba96848428337e685e10b06ccc1c89", "sha1": "51b31827c1d961ced142a3c5f3efa2b389f9c5ad", "size": 3007920}}}}, "confidence": 54.166666666666664, "scanEngines": [{"name": "VirusTotal Summary", "result": "Malicious", "confidence": 50, "version": "1.0", "details": "Detection rate: 50.0% (5/10)", "risk_level": "HIGH"}, {"name": "Bkav", "result": "Clean", "confidence": 0, "version": "2.0.0.1", "updateDate": "2020-25-05", "risk_level": "LOW"}, {"name": "<PERSON><PERSON>", "result": "Malicious", "confidence": 100, "version": "8.16", "updateDate": "2020-25-05", "risk_level": "HIGH"}, {"name": "Elastic", "result": "Clean", "confidence": 0, "version": "4.0.203", "updateDate": "2020-25-05", "risk_level": "LOW"}, {"name": "MicroWorld-eScan", "result": "Clean", "confidence": 0, "version": "14.0.409.0", "updateDate": "2020-25-05", "risk_level": "LOW"}, {"name": "CMC", "result": "Clean", "confidence": 0, "version": "2.4.2022.1", "updateDate": "2020-25-05", "risk_level": "LOW"}, {"name": "CAT-QuickHeal", "result": "Malicious", "confidence": 100, "version": "22.00", "updateDate": "2020-25-05", "risk_level": "HIGH"}, {"name": "Skyhigh", "result": "Malicious", "confidence": 100, "version": "v2021.2.0+4045", "updateDate": "2020-25-05", "risk_level": "HIGH"}, {"name": "McAfee", "result": "Malicious", "confidence": 100, "version": "6.0.6.653", "updateDate": "2020-25-05", "risk_level": "HIGH"}, {"name": "Malwarebytes", "result": "Clean", "confidence": 0, "version": "4.5.5.54", "updateDate": "2020-25-05", "risk_level": "LOW"}, {"name": "Zillya", "result": "Malicious", "confidence": 100, "version": "2.0.0.5356", "updateDate": "2020-25-05", "risk_level": "HIGH"}, {"name": "ML Detection", "result": "Malicious", "confidence": 100, "version": "1.0", "details": "AI-powered machine learning detection", "risk_level": "HIGH"}]}