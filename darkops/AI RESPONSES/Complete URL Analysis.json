{"phishing_analysis": {"url": "https://amazon.com/", "is_safe": true, "confidence": 0.9996876678630731, "prediction": "Safe"}, "url_analysis": {"basic_info": {"domain": "www.amazon.com", "ip": "**********", "country": "US", "server": "Server", "security_state": null, "final_url": "https://www.amazon.com/"}, "technologies": [], "behavior": {"requests": 0, "domains": 0, "resources": {}, "redirects": 0, "mixed_content": 0}, "security": {"malicious": false, "score": 0, "categories": [], "brands": [], "threats": [], "dom_security": {"vulnerable_js_libs": [], "external_scripts": 0, "forms": 0, "password_fields": 0, "suspicious_elements": []}, "certificates": [{"subjectName": "www.amazon.com", "issuer": "DigiCert Global CA G2", "validFrom": 1726185600, "validTo": 1755993599}, {"subjectName": "m.media-amazon.com", "issuer": "DigiCert TLS RSA SHA256 2020 CA1", "validFrom": 1719964800, "validTo": 1751759999}, {"subjectName": "unagi-na.amazon.com", "issuer": "Amazon RSA 2048 M01", "validFrom": 1731888000, "validTo": 1762646399}, {"subjectName": "fls-na.amazon.com", "issuer": "Amazon RSA 2048 M02", "validFrom": 1727913600, "validTo": 1762041599}], "security_headers": {}}, "console_messages": [], "cookies": [], "scan_info": {"scan_id": "************************************", "scan_result_url": "https://urlscan.io/result/************************************/", "screenshot_url": "https://urlscan.io/screenshots/************************************.png", "screenshot_path": "url_screenshots/************************************.png", "scan_time": "2025-02-15T14:14:50.177Z", "analysis_time": null}, "enhanced_security": {"mixed_content": true, "vulnerable_libraries": [], "suspicious_redirects": true, "insecure_cookies": true}, "reputation": {"domain_age": {"error": "Unable to determine domain age"}, "ssl_validity": {"valid": false, "reason": "'issuer'"}, "blacklist_status": "Not implemented"}, "technology_stack": {"server": "Server", "frameworks": [], "analytics": [], "cms": "Unknown"}}}