import { createSchema } from "graphql-yoga";
import { create<PERSON><PERSON> } from "graphql-yoga";
import { GraphQLScalarType, Kind } from "graphql";
import jwt from "jsonwebtoken";
import env from "@/env.js";
import { DashboardStats as DashboardStatsModel } from "@/db/DashboardStats.ts";
import { ScanHistory } from "@/db/ScanHistory.ts";
import { EmailAnalysisResults } from "@/db/EmailAnalysisResults.js";
import { ApkAnalysisService } from "../services/apkAnalysis.service.ts";
import { EmailAnalysisService } from "../services/emailAnalysis.service.ts";
import { UrlAnalysisService } from "../services/urlAnalysis.service.ts";
import { SmsAnalysisService } from "../services/smsAnalysis.service.ts";
import { UpdateLastScansService } from "../services/updateLastScans.service.ts";
import { UserScanStatsService } from "../services/userScanStats.service.ts";
import ExcelJS from "exceljs";

// Type definitions
const typeDefs = `
  type Query {
    dashboardStats: DashboardStatsData!
    scanHistory(
      page: Int,
      limit: Int,
      scanType: String,
      startDate: String,
      endDate: String,
      threatLevel: String,
      sr: String,
      sortField: String,
      sortOrder: String
    ): ScanHistoryResponse!
    userScanStats: UserScanStatsData!
    scanDetails(id: ID!): ScanResult!
    emailAnalysisResults(
      limit: Int,
      offset: Int,
      status: String,
      threatLevel: String
    ): EmailAnalysisResultsResponse!
    emailAnalysisDetails(emailId: String!): EmailAnalysisResult
  }

  type Mutation {
    exportScanHistory(
      scanType: String,
      startDate: String,
      endDate: String,
      threatLevel: String,
      sr: String
    ): ExportScanHistoryResponse!
  }

  type UserScanStatsData {
    totalScans: Int!
    scansByType: ScansByType!
    dailyScans: [DailyScanCount!]!
    monthlyScans: [MonthlyScanCount!]!
    scanEngineUsage: [ScanEngineUsage!]!
    lastScanAt: String
  }

  type ScansByType {
    APK: Int!
    URL: Int!
    EMAIL: Int!
    SMS: Int!
    QR: Int!
  }

  type DailyScanCount {
    date: String!
    count: Int!
    scanType: String!
  }

  type MonthlyScanCount {
    year: Int!
    month: Int!
    count: Int!
    scanType: String!
  }

  type ScanEngineUsage {
    name: String!
    count: Int!
    scanType: String!
    detectionCount: Int!
    lastUsed: String
  }

  type DashboardStatsData {
    totalScans: Int!
    totalScansPercentageChange: Float!
    threatScore: ThreatScore!
    scansByType: [ScanTypeCount!]!
    featureUsageStats: [FeatureUsageStats!]!
    recentScans: [ScanResult!]!
    threatsByCategory: [ThreatCategoryCount!]!
    lastApkScan: ScanResult
    lastEmailScan: ScanResult
    lastUrlScan: ScanResult
    lastSmsScan: ScanResult
  }

  type FeatureUsageStats {
    feature: String!
    count: Int!
    lastUsed: String
  }

  type ThreatScore {
    score: Float!
    level: String!
    percentageChange: Float!
    previousScore: Float!
  }

  type ScanTypeCount {
    type: String!
    count: Int!
    percentageChange: Float!
    previousWeekCount: Int!
  }

  type ThreatCategoryCount {
    category: String!
    count: Int!
  }

  type ScanResult {
    id: ID!
    userId: String!
    scanType: String!
    target: String!
    result: ScanResultData!
    SR: String!
    createdAt: String!
  }

  type ScanResultData {
    threatScore: Float!
    threatLevel: String!
    findings: [Finding!]!
    confidence: Float
    scanEngines: [ScanEngine!]
    apkInfo: ApkInfo
    staticAnalysis: StaticAnalysis
    emailInfo: EmailInfo
    senderInfo: SenderInfo
    attachments: [Attachment!]
    urlInfo: UrlInfo
    securityInfo: SecurityInfo
    technologyStack: TechnologyStack
    behavior: Behavior
    smsInfo: SmsInfo
    contentAnalysis: ContentAnalysis
    smsUrlInfo: SmsUrlInfo
  }

  type Finding {
    type: String!
    severity: String!
    description: String!
    details: JSON
  }

  type ScanEngine {
    name: String!
    result: String!
    confidence: Float!
    version: String
    updateDate: String
    risk_level: String
    details: String
  }

  type ApkInfo {
    packageName: String
    fileName: String
    fileSize: Int
    sha256: String
    sha1: String
    md5: String
    firstSubmission: String
    lastAnalysis: String
  }

  type StaticAnalysis {
    activities: [String]
    services: [String]
    permissions: [Permission]
    receivers: [String]
    providers: [String]
    malwareScore: Float
    riskScore: Float
    behavioralTags: [String]
    dnsLookups: [String]
    httpRequests: [String]
    filesWritten: [String]
    filesDeleted: [String]
    ja3Digests: [String]
    mitreTechniques: [MitreTechnique]
  }

  type MitreTechnique {
    id: String
    name: String
    description: String
  }

  type Permission {
    name: String!
    severity: String
    description: String
  }

  type EmailInfo {
    from: String
    fromName: String
    fromEmail: String
    to: String
    subject: String
    date: String
    messageId: String
    authResults: String
    spfResult: String
    dkimSignature: String
    returnPath: String
    contentType: String
    replyTo: String
    cc: String
    bcc: String
    isBcc: Boolean
    receivedDate: String
    xHeaders: JSON
    analysisTimestamp: String
    authenticationResults: EmailAuthenticationResults
  }

  type EmailAuthenticationResults {
    spf: EmailAuthResult
    dkim: EmailAuthResult
    dmarc: EmailAuthResult
    compAuth: EmailAuthResult
  }

  type EmailAuthResult {
    result: String
    domain: String
    action: String
    selector: String
    reason: String
  }

  type SenderInfo {
    ip: String
    hostname: String
    location: Location
    org: String
    isp: String
    asn: String
    domain: String
    usageType: String
    abuseConfidenceScore: Float
    totalReports: Int
    lastReportedAt: String
    isTor: Boolean
    isWhitelisted: Boolean
    riskLevel: String
    postalCode: String
    timezone: String
  }

  type Location {
    city: String
    region: String
    country: String
    coordinates: String
  }

  type Attachment {
    filename: String!
    contentType: String
    size: Int
    sha256: String
    md5: String
    sha1: String
    isMalicious: Boolean
    threatType: String
    scanId: String
    scanResults: JSON
    scanTimestamp: String
    detectionCount: Int
    detectionNames: [String]
    riskLevel: String
  }

  type UrlInfo {
    url: String
    domain: String
    ip: String
    country: String
    server: String
    finalUrl: String
    scanId: String
    scanResultUrl: String
    screenshotUrl: String
    scanTime: String
    securityState: String
    phishingAnalysis: PhishingAnalysis
  }

  type PhishingAnalysis {
    isSafe: Boolean
    prediction: String
    confidence: Float
  }

  type SecurityInfo {
    malicious: Boolean
    score: Float
    riskLevel: String
    categories: [String]
    brands: [String]
    threats: [JSON]
    domSecurity: DomSecurity
    certificates: [Certificate]
    securityHeaders: JSON
    mixedContent: Boolean
    vulnerableLibraries: [JSON]
    suspiciousRedirects: Boolean
    insecureCookies: Boolean
    reputation: Reputation
    cspEvaluation: JSON
    corsMisconfiguration: Boolean
  }

  type DomSecurity {
    vulnerableJsLibs: [JSON]
    externalScripts: Int
    forms: Int
    passwordFields: Int
    suspiciousElements: [JSON]
  }

  type Certificate {
    subjectName: String
    issuer: String
    validFrom: String
    validTo: String
    fingerprint: String
  }

  type Reputation {
    domainAge: JSON
    domainAgeInDays: Int
    domainRegistrationDate: String
    sslValidity: JSON
    sslValid: Boolean
    sslReason: String
    blacklistStatus: String
  }

  type TechnologyStack {
    server: String
    frameworks: [String]
    analytics: [String]
    cms: String
    technologies: [Technology]
    javascriptLibraries: [String]
    programmingLanguages: [String]
    webServers: [String]
    hostingProviders: [String]
  }

  type Technology {
    name: String
    version: String
    category: String
  }

  type Behavior {
    requests: Int
    domains: Int
    resources: JSON
    redirects: Int
    mixedContent: Int
    contactedDomains: [String]
    redirectChain: [String]
  }

  type SmsInfo {
    message: String
    sender: String
    timestamp: String
    analysisTimestamp: String
    messageLength: Int
    isPhishing: Boolean
    confidence: Float
    riskLevel: String
    messageType: String
    recipient: String
  }

  type ContentAnalysis {
    riskLevel: String
    suspiciousKeywords: [String]
    riskIndicators: JSON
    contentCategories: [String]
    language: String
    sentiment: JSON
    isPhishing: Boolean
    confidence: Float
    scanEnginesCount: Int
    detectionResults: [DetectionResult]
    threatTypes: [String]
    spamScore: Float
    messageIntent: String
  }

  type DetectionResult {
    name: String
    result: String
    confidence: Float
  }

  type SmsUrlInfo {
    urls: [String]
    shortenedUrls: [String]
    urlAnalysis: [JSON]
    maliciousUrlsCount: Int
    suspiciousUrlsCount: Int
    cleanUrlsCount: Int
  }

  # Define JSON scalar type for mixed data
  scalar JSON

  type ScanHistoryResponse {
    scans: [ScanResult!]!
    pagination: PaginationInfo!
  }

  type PaginationInfo {
    total: Int!
    page: Int!
    limit: Int!
    totalPages: Int!
  }

  type EmailAnalysisResultsResponse {
    results: [EmailAnalysisResult!]!
    total: Int!
  }

  type EmailAnalysisResult {
    emailId: String!
    gmailThreadId: String!
    snippet: String!
    headers: EmailHeaders!
    result: EmailAnalysisResultData!
    SR: String!
    status: String!
    createdAt: String!
    processedAt: String
  }

  type EmailHeaders {
    from: String
    to: String
    subject: String
    date: String
    messageId: String
    contentType: String
    replyTo: String
    cc: String
    receivedDate: String
    xHeaders: JSON
    authenticationResults: JSON
  }

  type EmailAnalysisResultData {
    threatScore: Float!
    threatLevel: String!
    confidence: Float!
    scanEngines: [ScanEngine!]!
  }

  type ExportScanHistoryResponse {
    file: String!
    filename: String!
  }
`;

// Helper function to get userId from context
function getUserIdFromContext(context: {
  request: { headers: { get: (name: string) => string | null } };
  userId?: string;
}): string {
  // If userId is already in context, use it
  if (context.userId) {
    return context.userId;
  }

  // Otherwise extract from Authorization header
  const authHeader = context.request.headers.get("Authorization");
  const token = authHeader?.split(" ")[1];

  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as { userId: string };
    return decoded.userId;
  } catch (e) {
    console.error("Error verifying token:", e);
    throw new Error("Invalid authentication token");
  }
}

// Add this utility function to ensure all required fields are present
function ensureRequiredFields(data: any): {
  totalScans: number;
  totalScansPercentageChange: number;
  threatScore: {
    score: number;
    level: string;
    percentageChange: number;
    previousScore: number;
  };
  scansByType: Array<{
    type: string;
    count: number;
    percentageChange: number;
    previousWeekCount: number;
  }>;
  featureUsageStats: Array<{
    feature: string;
    count: number;
    lastUsed: string | null;
  }>;
  recentScans: Array<any>;
  threatsByCategory: Array<{ category: string; count: number }>;
  lastApkScan: any;
  lastEmailScan: any;
  lastUrlScan: any;
  lastSmsScan: any;
} {
  // First ensure the data object exists
  const safeData = data || {};

  // Ensure scansByType is an array and each item has required fields
  let scansByType = Array.isArray(safeData.scansByType)
    ? safeData.scansByType
    : [];

  // Filter out any items with null type and ensure each item has required fields
  scansByType = scansByType
    .filter((item) => item && item.type) // Filter out null items or items with null type
    .map((item) => ({
      type: item.type || "UNKNOWN", // Provide default for type
      count: typeof item.count === "number" ? item.count : 0, // Ensure count is a number
      percentageChange:
        typeof item.percentageChange === "number" ? item.percentageChange : 0,
      previousWeekCount:
        typeof item.previousWeekCount === "number" ? item.previousWeekCount : 0,
    }));

  // Ensure featureUsageStats is an array and each item has required fields
  let featureUsageStats = Array.isArray(safeData.featureUsageStats)
    ? safeData.featureUsageStats
    : [];

  // Filter out any items with null feature
  featureUsageStats = featureUsageStats
    .filter((item) => item && item.feature)
    .map((item) => ({
      feature: item.feature || "UNKNOWN",
      count: typeof item.count === "number" ? item.count : 0,
      lastUsed: item.lastUsed ? new Date(item.lastUsed).toISOString() : null,
    }));

  // Ensure threatsByCategory is an array and each item has required fields
  let threatsByCategory = Array.isArray(safeData.threatsByCategory)
    ? safeData.threatsByCategory
    : [];

  // Filter out any items with null category
  threatsByCategory = threatsByCategory
    .filter((item) => item && item.category)
    .map((item) => ({
      category: item.category || "UNKNOWN",
      count: typeof item.count === "number" ? item.count : 0,
    }));

  // Ensure recentScans is an array and each item has required fields
  let recentScans = Array.isArray(safeData.recentScans)
    ? safeData.recentScans
    : [];

  // Filter out any invalid scan items
  recentScans = recentScans
    .filter((scan) => scan && scan.id && scan.scanType)
    .map((scan) => ({
      id: scan.id,
      userId: scan.userId || "unknown",
      scanType: scan.scanType,
      target: scan.target || "unknown",
      result: scan.result || {
        threatScore: 0,
        threatLevel: "LOW",
        findings: [],
        confidence: 0,
        scanEngines: [],
      },
      SR: scan.SR || "UNKNOWN",
      createdAt: scan.createdAt
        ? typeof scan.createdAt === "string"
          ? scan.createdAt.includes("T")
            ? scan.createdAt
            : new Date(scan.createdAt).toISOString()
          : new Date(scan.createdAt).toISOString()
        : new Date().toISOString(),
    }));

  // Ensure threatScore has all required fields
  const threatScore = safeData.threatScore || { score: 0, level: "LOW" };
  if (typeof threatScore.percentageChange !== "number") {
    threatScore.percentageChange = 0;
  }
  if (typeof threatScore.previousScore !== "number") {
    threatScore.previousScore = 0;
  }

  return {
    totalScans:
      typeof safeData.totalScans === "number" ? safeData.totalScans : 0,
    totalScansPercentageChange:
      typeof safeData.totalScansPercentageChange === "number"
        ? safeData.totalScansPercentageChange
        : 0,
    threatScore,
    scansByType,
    featureUsageStats,
    recentScans,
    threatsByCategory,
    lastApkScan: safeData.lastApkScan || null,
    lastEmailScan: safeData.lastEmailScan || null,
    lastUrlScan: safeData.lastUrlScan || null,
    lastSmsScan: safeData.lastSmsScan || null,
  };
}

// Function to transform ScanHistory documents to match GraphQL schema
function transformScanHistory(scan: any): {
  id: string;
  userId: string;
  scanType: string;
  target: string;
  result: {
    threatScore: number;
    threatLevel: string;
    findings: Array<{
      type: string;
      severity: string;
      description: string;
      details: any;
    }>;
    confidence: number;
    scanEngines: Array<{
      name: string;
      result: string;
      confidence: number;
    }>;
  };
  SR: string;
  createdAt: string;
} | null {
  if (!scan) return null;

  // Convert Mongoose document to plain object if needed
  const scanObj = scan.toObject ? scan.toObject() : scan;

  // Ensure findings have all required fields
  const findings = Array.isArray(scanObj.result?.findings)
    ? scanObj.result.findings.map((finding: any) => ({
        type: finding.type || "UNKNOWN",
        severity: finding.severity || "MEDIUM",
        description: finding.description || "No description provided",
        details: finding.details || null,
      }))
    : [];

  // Ensure scan engines have all required fields
  const scanEngines = Array.isArray(scanObj.result?.scanEngines)
    ? scanObj.result.scanEngines.map((engine: any) => ({
        name: engine.name || "Unknown Engine",
        result: engine.result || "Unknown",
        confidence:
          typeof engine.confidence === "number" ? engine.confidence : 0,
        version: engine.version || null,
        updateDate: engine.updateDate || null,
        details: engine.details || null,
        risk_level: engine.risk_level || null,
      }))
    : [];

  // Extract additional APK information if this is an APK scan
  let apkInfo = null;
  let staticAnalysis = null;

  if (scanObj.scanType === "APK" && scanObj.result) {
    apkInfo = ApkAnalysisService.extractApkInfo(scanObj.result);
    staticAnalysis = ApkAnalysisService.extractStaticAnalysis(scanObj.result);
  }

  // Extract additional Email information if this is an Email scan
  let emailInfo = null;
  let senderInfo = null;
  let attachments = null;

  if (scanObj.scanType === "EMAIL" && scanObj.result) {
    emailInfo = EmailAnalysisService.extractEmailHeaders(scanObj.result);
    senderInfo = EmailAnalysisService.extractSenderInfo(scanObj.result);
    attachments = EmailAnalysisService.extractAttachments(scanObj.result);
  }

  // Extract additional URL information if this is a URL scan
  let urlInfo = null;
  let securityInfo = null;
  let technologyStack = null;
  let behavior = null;

  if (scanObj.scanType === "URL" && scanObj.result) {
    urlInfo = UrlAnalysisService.extractUrlInfo(scanObj.result);
    securityInfo = UrlAnalysisService.extractSecurityInfo(scanObj.result);
    technologyStack = UrlAnalysisService.extractTechnologyStack(scanObj.result);
    behavior = UrlAnalysisService.extractBehavior(scanObj.result);
  }

  // Extract additional SMS information if this is a SMS scan
  let smsInfo = null;
  let contentAnalysis = null;
  let smsUrlInfo = null;

  if (scanObj.scanType === "SMS" && scanObj.result) {
    smsInfo = SmsAnalysisService.extractSmsInfo(scanObj.result);
    contentAnalysis = SmsAnalysisService.extractContentAnalysis(scanObj.result);
    smsUrlInfo = SmsAnalysisService.extractUrlInfo(scanObj.result);
  }

  return {
    id: scanObj._id?.toString() || scanObj.id || "unknown",
    userId: scanObj.userId || "unknown",
    scanType: scanObj.scanType || "UNKNOWN",
    target: scanObj.targetName || "unknown", // Map targetName to target
    result: {
      threatScore:
        typeof scanObj.result?.threatScore === "number"
          ? scanObj.result.threatScore
          : 0,
      threatLevel: scanObj.result?.threatLevel || "LOW",
      findings: findings,
      confidence:
        typeof scanObj.result?.confidence === "number"
          ? scanObj.result.confidence
          : 0,
      scanEngines: scanEngines,
      apkInfo: apkInfo,
      staticAnalysis: staticAnalysis,
      emailInfo: emailInfo,
      senderInfo: senderInfo,
      attachments: attachments,
      urlInfo: urlInfo,
      securityInfo: securityInfo,
      technologyStack: technologyStack,
      behavior: behavior,
      smsInfo: smsInfo,
      contentAnalysis: contentAnalysis,
      smsUrlInfo: smsUrlInfo,
    },
    SR: scanObj.SR || "UNKNOWN", // Include SR field
    createdAt: scanObj.createdAt
      ? typeof scanObj.createdAt === "string" && scanObj.createdAt.includes("T")
        ? scanObj.createdAt
        : new Date(scanObj.createdAt).toISOString()
      : new Date().toISOString(),
  };
}

// Scan Details resolver
class ScanDetailsResolver {
  static async getScanDetails(
    _: any,
    { id }: { id: string },
    context: { request: { headers: { get: (name: string) => string | null } } }
  ) {
    const userId = getUserIdFromContext(context);

    // Find the scan by ID
    const scan = await ScanHistory.findOne({
      _id: id,
      userId,
    });

    if (!scan) {
      throw new Error(`Scan with ID ${id} not found`);
    }

    // Process the scan data based on scan type
    try {
      // Format the scan data
      const formattedScan = {
        id: scan._id.toString(),
        userId: scan.userId,
        scanType: scan.scanType,
        target: scan.targetName || "unknown",
        SR: scan.SR || "UNKNOWN",
        createdAt: scan.createdAt.toISOString(),
        result: {
          threatScore: scan.result?.threatScore || 0,
          threatLevel: scan.result?.threatLevel || "LOW",
          findings: scan.result?.findings || [],
          confidence: scan.result?.confidence || 0,
        },
      };

      // Add scan type specific data
      switch (scan.scanType) {
        case "APK":
          // Process APK scan data
          if (scan.result) {
            formattedScan.result.scanEngines =
              ApkAnalysisService.extractScanEngines(scan.result);
            formattedScan.result.apkInfo = scan.result.apkInfo || {};
            formattedScan.result.staticAnalysis =
              scan.result.staticAnalysis || {};
          }
          break;
        case "URL":
          // Process URL scan data
          if (scan.result) {
            formattedScan.result.urlInfo = scan.result.urlInfo || {};
            formattedScan.result.securityInfo = scan.result.securityInfo || {};
            formattedScan.result.technologyStack =
              scan.result.technologyStack || {};
            formattedScan.result.behavior = scan.result.behavior || {};
          }
          break;
        case "EMAIL":
          // Process Email scan data
          if (scan.result) {
            formattedScan.result.emailInfo = scan.result.emailInfo || {};
            formattedScan.result.senderInfo = scan.result.senderInfo || {};
            formattedScan.result.attachments = scan.result.attachments || [];
          }
          break;
        case "SMS":
          // Process SMS scan data
          if (scan.result) {
            formattedScan.result.smsInfo = scan.result.smsInfo || {};
            formattedScan.result.contentAnalysis =
              scan.result.contentAnalysis || {};
            formattedScan.result.smsUrlInfo = scan.result.smsUrlInfo || {};
          }
          break;
      }

      return formattedScan;
    } catch (error) {
      console.error(`Error processing ${scan.scanType} scan data:`, error);
      throw new Error(`Error processing scan data: ${error.message}`);
    }
  }
}

// Resolver implementations
class DashboardStatsData {
  static async getDashboardStats(
    _: any,
    __: any,
    context: {
      request: { headers: { get: (name: string) => string | null } };
      userId?: string;
    }
  ) {
    // Extract userId from context, either directly or using the helper function
    const userId = context.userId || getUserIdFromContext(context);

    // Get dashboard stats from MongoDB
    const stats = await DashboardStatsModel.findOne({ userId });
    if (!stats) {
      // Return default values for a new user
      return {
        totalScans: 0,
        totalScansPercentageChange: 0,
        threatScore: {
          score: 0,
          level: "LOW",
          percentageChange: 0,
          previousScore: 0,
        },
        scansByType: [], // Empty array is safer than null
        featureUsageStats: [], // Include empty feature usage stats
        recentScans: [],
        threatsByCategory: [],
        lastApkScan: null,
        lastEmailScan: null,
        lastUrlScan: null,
        lastSmsScan: null,
      };
    }

    // Convert Mongoose document to plain object and ensure all required fields
    return ensureRequiredFields(stats.toObject ? stats.toObject() : stats);
  }
}

class ScanHistoryResponse {
  static async getScanHistory(
    _: any,
    {
      page = 1,
      limit = 10,
      scanType,
      startDate,
      endDate,
      threatLevel,
      sr,
      sortField = "createdAt",
      sortOrder = "DESC",
    }: {
      page?: number;
      limit?: number;
      scanType?: string;
      startDate?: string;
      endDate?: string;
      threatLevel?: string;
      sr?: string;
      sortField?: string;
      sortOrder?: string;
    },
    context: { request: { headers: { get: (name: string) => string | null } } }
  ) {
    const userId = getUserIdFromContext(context);

    // Build query
    const query: any = { userId };

    // Add filters if provided
    if (scanType) {
      query.scanType = scanType;
    }

    if (threatLevel) {
      query["result.threatLevel"] = threatLevel;
    }

    if (sr) {
      query.SR = sr;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      query.createdAt = {};

      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }

      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    // Get total count
    const total = await ScanHistory.countDocuments(query);

    // Determine sort order
    const sort: any = {};
    sort[sortField] = sortOrder === "ASC" ? 1 : -1;

    // Get paginated results
    const history = await ScanHistory.find(query)
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit);

    const totalPages = Math.ceil(total / limit);

    // Transform scan history to match GraphQL schema
    const scans = history.map(transformScanHistory).filter(Boolean);

    return {
      scans,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  static async exportScanHistory(
    _: any,
    {
      scanType,
      startDate,
      endDate,
      threatLevel,
      sr,
    }: {
      scanType?: string;
      startDate?: string;
      endDate?: string;
      threatLevel?: string;
      sr?: string;
    },
    context: { request: { headers: { get: (name: string) => string | null } } }
  ) {
    const userId = getUserIdFromContext(context);

    // Build query
    const query: any = { userId };

    // Add filters if provided
    if (scanType) {
      query.scanType = scanType;
    }

    if (threatLevel) {
      query["result.threatLevel"] = threatLevel;
    }

    if (sr) {
      query.SR = sr;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      query.createdAt = {};

      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }

      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    // Get all matching scans
    const scans = await ScanHistory.find(query).sort({ createdAt: -1 });

    // Transform scan history to match GraphQL schema
    const formattedScans = scans.map(transformScanHistory).filter(Boolean);

    // Generate Excel file
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Scan History");

    // Add headers
    worksheet.columns = [
      { header: "Scan Type", key: "scanType", width: 15 },
      { header: "Target", key: "target", width: 40 },
      { header: "Threat Level", key: "threatLevel", width: 15 },
      { header: "Threat Score", key: "threatScore", width: 15 },
      { header: "Scan Result", key: "SR", width: 15 },
      { header: "Date/Time", key: "createdAt", width: 20 },
    ];

    // Add rows
    formattedScans.forEach((scan) => {
      worksheet.addRow({
        scanType: scan.scanType,
        target: scan.target,
        threatLevel: scan.result.threatLevel,
        threatScore: scan.result.threatScore,
        SR: scan.SR,
        createdAt: new Date(scan.createdAt).toLocaleString(),
      });
    });

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Return base64 encoded Excel file
    return {
      file: buffer.toString("base64"),
      filename: `scan-history-${new Date().toISOString().split("T")[0]}.xlsx`,
    };
  }
}

// JSON scalar type resolver
const JSONScalar = new GraphQLScalarType({
  name: "JSON",
  description:
    "The JSON scalar type represents JSON values as specified by ECMA-404",
  serialize(value) {
    return value; // Return the value directly for serialization
  },
  parseValue(value) {
    return value; // Return the value directly for client variables
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      try {
        return JSON.parse(ast.value);
      } catch (e) {
        return null;
      }
    }
    return null;
  },
});

// Email Analysis Results Resolver
class EmailAnalysisResolver {
  static async getEmailAnalysisResults(
    _: any,
    {
      limit = 10,
      offset = 0,
      status,
      threatLevel,
    }: {
      limit?: number;
      offset?: number;
      status?: string;
      threatLevel?: string;
    },
    context: { request: { headers: { get: (name: string) => string | null } } }
  ) {
    const userId = getUserIdFromContext(context);

    // Build query
    const query: any = { userId };

    // Add filters if provided
    if (status) {
      query.status = status;
    }

    if (threatLevel) {
      query["result.threatLevel"] = threatLevel;
    }

    // Get total count
    const total = await EmailAnalysisResults.countDocuments(query);

    // Get results
    const results = await EmailAnalysisResults.find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);

    // Format results
    const formattedResults = results.map((result) => ({
      emailId: result.emailId,
      gmailThreadId: result.gmailThreadId,
      snippet: result.snippet,
      headers: {
        from: result.headers?.from || null,
        to: result.headers?.to || null,
        subject: result.headers?.subject || null,
        date: result.headers?.date || null,
        messageId: result.headers?.messageId || null,
        contentType: result.headers?.contentType || null,
        replyTo: result.headers?.replyTo || null,
        cc: result.headers?.cc || null,
        receivedDate: result.headers?.receivedDate || null,
        xHeaders: result.headers?.xHeaders || null,
        authenticationResults: result.headers?.authenticationResults || null,
      },
      result: {
        threatScore: result.result?.threatScore || 0,
        threatLevel: result.result?.threatLevel || "LOW",
        confidence: result.result?.confidence || 0,
        scanEngines: result.result?.scanEngines || [],
      },
      SR: result.SR,
      status: result.status,
      createdAt: result.createdAt.toISOString(),
      processedAt: result.processedAt ? result.processedAt.toISOString() : null,
    }));

    return {
      results: formattedResults,
      total,
    };
  }

  static async getEmailAnalysisDetails(
    _: any,
    { emailId }: { emailId: string },
    context: { request: { headers: { get: (name: string) => string | null } } }
  ) {
    const userId = getUserIdFromContext(context);

    // Get analysis details
    const analysis = await EmailAnalysisResults.findOne({
      userId,
      emailId,
    });

    if (!analysis) {
      return null;
    }

    // Format result
    return {
      emailId: analysis.emailId,
      gmailThreadId: analysis.gmailThreadId,
      snippet: analysis.snippet,
      headers: {
        from: analysis.headers?.from || null,
        to: analysis.headers?.to || null,
        subject: analysis.headers?.subject || null,
        date: analysis.headers?.date || null,
        messageId: analysis.headers?.messageId || null,
        contentType: analysis.headers?.contentType || null,
        replyTo: analysis.headers?.replyTo || null,
        cc: analysis.headers?.cc || null,
        receivedDate: analysis.headers?.receivedDate || null,
        xHeaders: analysis.headers?.xHeaders || null,
        authenticationResults: analysis.headers?.authenticationResults || null,
      },
      result: {
        threatScore: analysis.result?.threatScore || 0,
        threatLevel: analysis.result?.threatLevel || "LOW",
        confidence: analysis.result?.confidence || 0,
        scanEngines: analysis.result?.scanEngines || [],
      },
      SR: analysis.SR,
      status: analysis.status,
      createdAt: analysis.createdAt.toISOString(),
      processedAt: analysis.processedAt
        ? analysis.processedAt.toISOString()
        : null,
    };
  }
}

// Resolvers
const resolvers = {
  JSON: JSONScalar,
  Query: {
    scanDetails: ScanDetailsResolver.getScanDetails,
    emailAnalysisResults: EmailAnalysisResolver.getEmailAnalysisResults,
    emailAnalysisDetails: EmailAnalysisResolver.getEmailAnalysisDetails,
    dashboardStats: async (parent, args, context) => {
      try {
        console.log("DEBUG - GraphQL dashboardStats query called");
        // Extract userId from context
        const userId = context.userId || getUserIdFromContext(context);
        console.log("DEBUG - User ID:", userId);

        // Get the dashboard stats first
        const result = await DashboardStatsData.getDashboardStats(
          parent,
          args,
          context
        );

        // Only try to update if we have a result
        if (result) {
          try {
            // Try to update the lastScans, but don't let it break the query if it fails
            await UpdateLastScansService.updateAllLastScans(userId);
            console.log("DEBUG - Successfully updated lastScans");
          } catch (updateError) {
            console.error(
              "Error updating lastScans, continuing with existing data:",
              updateError
            );
          }
        }

        // If we don't have a result, return an empty object to prevent errors
        if (!result) {
          console.log(
            "DEBUG - No dashboard stats found, returning empty object"
          );
          return {
            totalScans: 0,
            scansByType: [],
            featureUsageStats: [],
            recentScans: [],
            threatsByCategory: [],
            lastApkScan: null,
            lastEmailScan: null,
            lastUrlScan: null,
            lastSmsScan: null,
          };
        }

        // Log the lastApkScan data if available
        if (result.lastApkScan) {
          console.log(
            "DEBUG - lastApkScan found in result:",
            result.lastApkScan.id
          );

          // Ensure scanEngines is populated
          if (result.lastApkScan.result) {
            try {
              // Use the imported ApkAnalysisService

              // Always extract scan engines to ensure we have the latest implementation
              result.lastApkScan.result.scanEngines =
                ApkAnalysisService.extractScanEngines(
                  result.lastApkScan.result
                );
              console.log(
                `DEBUG - Added ${result.lastApkScan.result.scanEngines.length} scanEngines to lastApkScan`
              );
            } catch (error) {
              console.error("Error extracting scan engines:", error);
            }
          }
        } else {
          console.log("DEBUG - No lastApkScan found in result");

          // Try to find APK scans in recentScans
          if (
            Array.isArray(result.recentScans) &&
            result.recentScans.length > 0
          ) {
            const apkScans = result.recentScans.filter(
              (scan) => scan.scanType === "APK"
            );

            if (apkScans.length > 0) {
              console.log(
                "DEBUG - Found APK scans in recentScans:",
                apkScans.length
              );

              // Don't modify the database here, just return the data
              // This avoids potential race conditions and errors
              result.lastApkScan = apkScans[0];

              // Ensure scanEngines is populated for the APK scan from recentScans
              if (result.lastApkScan.result) {
                try {
                  // Use the imported ApkAnalysisService

                  // Always extract scan engines to ensure we have the latest implementation
                  result.lastApkScan.result.scanEngines =
                    ApkAnalysisService.extractScanEngines(
                      result.lastApkScan.result
                    );
                  console.log(
                    `DEBUG - Added ${result.lastApkScan.result.scanEngines.length} scanEngines to APK scan from recentScans`
                  );
                } catch (error) {
                  console.error(
                    "Error extracting scan engines for APK scan from recentScans:",
                    error
                  );
                }
              }
            }
          }
        }

        return result;
      } catch (error) {
        console.error("ERROR in dashboardStats resolver:", error);
        // Return a minimal valid response instead of throwing
        return {
          totalScans: 0,
          scansByType: [],
          featureUsageStats: [],
          recentScans: [],
          threatsByCategory: [],
          lastApkScan: null,
          lastEmailScan: null,
          lastUrlScan: null,
          lastSmsScan: null,
        };
      }
    },
    scanHistory: ScanHistoryResponse.getScanHistory,
    userScanStats: async (parent, args, context) => {
      try {
        const userId = getUserIdFromContext(context);
        const stats = await UserScanStatsService.getUserStats(userId);

        // Format the data to match the GraphQL schema
        return {
          totalScans: stats.totalScans || 0,
          scansByType: stats.scansByType || {
            APK: 0,
            URL: 0,
            EMAIL: 0,
            SMS: 0,
            QR: 0,
          },
          dailyScans:
            stats.dailyScans?.map((entry) => ({
              date: entry.date.toISOString(),
              count: entry.count,
              scanType: entry.scanType,
            })) || [],
          monthlyScans:
            stats.monthlyScans?.map((entry) => ({
              year: entry.year,
              month: entry.month,
              count: entry.count,
              scanType: entry.scanType,
            })) || [],
          scanEngineUsage:
            stats.scanEngineUsage?.map((entry) => ({
              name: entry.name,
              count: entry.count,
              scanType: entry.scanType,
              detectionCount: entry.detectionCount || 0,
              lastUsed: entry.lastUsed ? entry.lastUsed.toISOString() : null,
            })) || [],
          lastScanAt: stats.lastScanAt ? stats.lastScanAt.toISOString() : null,
        };
      } catch (error) {
        console.error("ERROR in userScanStats resolver:", error);
        // Return a minimal valid response instead of throwing
        return {
          totalScans: 0,
          scansByType: {
            APK: 0,
            URL: 0,
            EMAIL: 0,
            SMS: 0,
            QR: 0,
          },
          dailyScans: [],
          monthlyScans: [],
          scanEngineUsage: [],
          lastScanAt: null,
        };
      }
    },
  },
  Mutation: {
    exportScanHistory: async (
      _: any,
      {
        scanType,
        startDate,
        endDate,
        threatLevel,
        sr,
      }: {
        scanType?: string;
        startDate?: string;
        endDate?: string;
        threatLevel?: string;
        sr?: string;
      },
      context: {
        request: { headers: { get: (name: string) => string | null } };
      }
    ) => {
      const userId = getUserIdFromContext(context);

      // Build query
      const query: any = { userId };

      // Add filters if provided
      if (scanType) {
        query.scanType = scanType;
      }

      if (threatLevel) {
        query["result.threatLevel"] = threatLevel;
      }

      if (sr) {
        query.SR = sr;
      }

      // Add date range filter if provided
      if (startDate || endDate) {
        query.createdAt = {};

        if (startDate) {
          query.createdAt.$gte = new Date(startDate);
        }

        if (endDate) {
          query.createdAt.$lte = new Date(endDate);
        }
      }

      // Get all matching scans
      const scans = await ScanHistory.find(query).sort({ createdAt: -1 });

      // Create a new Excel workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("Scan History");

      // Add headers
      worksheet.columns = [
        { header: "Scan ID", key: "id", width: 30 },
        { header: "Scan Type", key: "scanType", width: 15 },
        { header: "Target", key: "target", width: 40 },
        { header: "Threat Level", key: "threatLevel", width: 15 },
        { header: "Threat Score", key: "threatScore", width: 15 },
        { header: "Status", key: "status", width: 15 },
        { header: "Scan Result", key: "sr", width: 15 },
        { header: "Created At", key: "createdAt", width: 20 },
      ];

      // Add data rows
      scans.forEach((scan) => {
        worksheet.addRow({
          id: scan._id.toString(),
          scanType: scan.scanType,
          target: scan.targetName,
          threatLevel: scan.result?.threatLevel || "LOW",
          threatScore: scan.result?.threatScore || 0,
          status: scan.status || "SUCCESS",
          sr: scan.SR || "UNKNOWN",
          createdAt: scan.createdAt.toISOString(),
        });
      });

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFE0E0E0" },
      };

      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer();

      // Convert buffer to base64
      const base64 = buffer.toString("base64");

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const filename = `scan-history-${timestamp}.xlsx`;

      return {
        file: base64,
        filename,
      };
    },
  },
};

// Create GraphQL schema
const schema = createSchema({
  typeDefs,
  resolvers,
});

// Create Yoga instance
export const yoga = createYoga({
  schema,
  graphqlEndpoint: "/graphql",
  landingPage: true, // Enable GraphQL UI
  cors: {
    origin: "*",
    credentials: true,
  },
  // Add authentication check
  async context({ request }) {
    // Skip auth for introspection queries
    const body = await request.json().catch(() => ({}));
    const isIntrospection = body?.query?.includes("IntrospectionQuery");

    let userId = null;

    if (!isIntrospection) {
      const authHeader = request.headers.get("Authorization");
      if (!authHeader?.startsWith("Bearer ")) {
        throw new Error("Authentication required");
      }

      const token = authHeader.split(" ")[1];
      if (!token) {
        throw new Error("Authentication token required");
      }

      try {
        const decoded = jwt.verify(token, env.JWT_SECRET) as { userId: string };
        userId = decoded.userId;
      } catch (e) {
        throw new Error("Invalid authentication token");
      }
    }

    return { request, userId };
  },
});

// Export for use in app.ts
export default yoga;
