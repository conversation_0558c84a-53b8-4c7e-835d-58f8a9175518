import { celery } from './celery.js';
import { EmailFetcherService } from '@/services/emailFetcher.service.js';
import { EmailAnalysisResults } from '@/db/EmailAnalysisResults.js';
import { UpdateLastScansService } from '@/services/updateLastScans.service.js';

// Register email analysis task
celery.registerTask('analyze_email', async (userId: string, emailId: string, emlPath: string) => {
  try {
    console.log(`Processing email analysis task for user ${userId}, email ${emailId}`);
    
    // Update status to processing
    await EmailAnalysisResults.findOneAndUpdate(
      { userId, emailId },
      {
        status: "PROCESSING",
        updatedAt: new Date(),
      }
    );

    // Analyze email
    const result = await EmailFetcherService.analyzeEmail(userId, emailId, emlPath);
    
    // Update user stats
    await UpdateLastScansService.updateAllLastScans(userId);
    
    return result;
  } catch (error) {
    console.error(`Error analyzing email ${emailId}:`, error);
    
    // Update status to failed
    await EmailAnalysisResults.findOneAndUpdate(
      { userId, emailId },
      {
        status: "FAILED",
        updatedAt: new Date(),
      }
    );
    
    throw error;
  }
});

// Register batch email analysis task
celery.registerTask('analyze_emails_batch', async (userId: string, emailIds: string[]) => {
  try {
    console.log(`Processing batch email analysis task for user ${userId}, ${emailIds.length} emails`);
    
    // Fetch emails
    const emails = await EmailFetcherService.fetchRecentEmails(userId);
    
    // Filter emails by ID
    const emailsToAnalyze = emails.filter((email) => emailIds.includes(email.id));
    
    if (emailsToAnalyze.length === 0) {
      return { message: "No emails found with the provided IDs" };
    }
    
    // Queue emails for analysis
    const queuedEmails = await EmailFetcherService.queueEmailsForAnalysis(userId, emailsToAnalyze);
    
    return { message: "Emails queued for analysis", queuedEmails };
  } catch (error) {
    console.error(`Error analyzing emails batch for user ${userId}:`, error);
    throw error;
  }
});

// Register fetch recent emails task
celery.registerTask('fetch_recent_emails', async (userId: string, limit: number = 10) => {
  try {
    console.log(`Fetching recent emails for user ${userId}, limit ${limit}`);
    
    // Fetch recent emails
    const emails = await EmailFetcherService.fetchRecentEmails(userId, limit);
    
    // Return simplified email data (without content and raw)
    return emails.map((email) => ({
      id: email.id,
      threadId: email.threadId,
      snippet: email.snippet,
      subject: email.subject,
      from: email.from,
      date: email.date,
    }));
  } catch (error) {
    console.error(`Error fetching recent emails for user ${userId}:`, error);
    throw error;
  }
});

// Helper function to queue an email analysis task
export async function queueEmailAnalysisTask(userId: string, emailId: string, emlPath: string): Promise<string> {
  return celery.createTask('analyze_email', [userId, emailId, emlPath]);
}

// Helper function to queue a batch email analysis task
export async function queueEmailsBatchAnalysisTask(userId: string, emailIds: string[]): Promise<string> {
  return celery.createTask('analyze_emails_batch', [userId, emailIds]);
}

// Helper function to queue a fetch recent emails task
export async function queueFetchRecentEmailsTask(userId: string, limit: number = 10): Promise<string> {
  return celery.createTask('fetch_recent_emails', [userId, limit]);
}
