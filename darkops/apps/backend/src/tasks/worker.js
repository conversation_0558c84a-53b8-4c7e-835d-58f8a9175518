// Worker process for Celery tasks
import { celery } from './celery.js';
import { connectMongoDB } from '../db/mongodb.js';
import env from '../env.js';

// Import task handlers
import './emailTasks.js';

// Connect to MongoDB
await connectMongoDB();

console.log('Starting Celery worker...');
console.log('Environment:', process.env.NODE_ENV);
console.log('Redis URL:', process.env.REDIS_URL || 'redis://localhost:6379');

// Start processing tasks
await celery.processTasks();

console.log('Celery worker started successfully');

// Keep the process running
process.on('SIGINT', () => {
  console.log('Shutting down Celery worker...');
  process.exit(0);
});

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Keep the worker running despite unhandled rejections
});
