import mongoose from 'mongoose';
import env from '@/env.ts';

export async function connectMongoDB() {
  try {
    await mongoose.connect(env.MONGO_ATLAS_URI , {
      serverSelectionTimeoutMS: 60000, // Increase timeout to 30 seconds
      socketTimeoutMS: 60000, // Increase socket timeout
    });
    console.log('Connected to MongoDB ✅');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('MongoDB disconnected');
});