import mongoose from "mongoose";

// Define schema for daily scan counts
const dailyScanCountSchema = new mongoose.Schema(
  {
    date: {
      type: Date,
      required: true,
    },
    count: {
      type: Number,
      default: 0,
      required: true,
    },
    scanType: {
      type: String,
      enum: ["APK", "URL", "EMAIL", "SMS", "QR", "ALL"],
      required: true,
    },
  },
  { _id: false }
);

// Define schema for monthly scan counts
const monthlyScanCountSchema = new mongoose.Schema(
  {
    year: {
      type: Number,
      required: true,
    },
    month: {
      type: Number,
      required: true,
    },
    count: {
      type: Number,
      default: 0,
      required: true,
    },
    scanType: {
      type: String,
      enum: ["APK", "URL", "EMAIL", "SMS", "QR", "ALL"],
      required: true,
    },
  },
  { _id: false }
);

// Define schema for scan engine usage
const scanEngineUsageSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    count: {
      type: Number,
      default: 0,
      required: true,
    },
    scanType: {
      type: String,
      enum: ["APK", "URL", "EMAIL", "SMS", "QR", "ALL"],
      required: true,
    },
    detectionCount: {
      type: Number,
      default: 0,
    },
    lastUsed: {
      type: Date,
      default: null,
    },
  },
  { _id: false }
);

// Define schema for user scan stats
const userScanStatsSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  totalScans: {
    type: Number,
    default: 0,
    required: true,
  },
  scansByType: {
    APK: {
      type: Number,
      default: 0,
    },
    URL: {
      type: Number,
      default: 0,
    },
    EMAIL: {
      type: Number,
      default: 0,
    },
    SMS: {
      type: Number,
      default: 0,
    },
    QR: {
      type: Number,
      default: 0,
    },
  },
  dailyScans: {
    type: [dailyScanCountSchema],
    default: [],
  },
  monthlyScans: {
    type: [monthlyScanCountSchema],
    default: [],
  },
  scanEngineUsage: {
    type: [scanEngineUsageSchema],
    default: [],
  },
  lastScanAt: {
    type: Date,
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add pre-save hook to update timestamps
userScanStatsSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

export const UserScanStats = mongoose.model(
  "UserScanStats",
  userScanStatsSchema
);
