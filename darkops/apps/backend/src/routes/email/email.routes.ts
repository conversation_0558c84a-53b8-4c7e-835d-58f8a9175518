import { z } from "zod";
import { createRoute } from "@hono/zod-openapi";
import { json<PERSON>ontent, jsonContentRequired } from "stoker/openapi/helpers";
import * as HttpStatusCodes from "stoker/http-status-codes";

const tags = ["Email"];

// Define schemas for request and response
const schemas = {
  // Fetch recent emails request schema
  FetchEmailsRequestSchema: z.object({
    limit: z
      .number()
      .optional()
      .default(10)
      .describe("Number of emails to fetch"),
  }),

  // Fetch recent emails response schema
  FetchEmailsResponseSchema: z.object({
    message: z.string(),
    emails: z.array(
      z.object({
        id: z.string(),
        threadId: z.string(),
        snippet: z.string(),
        subject: z.string().nullable(),
        from: z.string().nullable(),
        date: z.string().nullable(),
      })
    ),
  }),

  // Analyze emails request schema
  AnalyzeEmailsRequestSchema: z.object({
    emailIds: z.array(z.string()).describe("Array of email IDs to analyze"),
  }),

  // Analyze emails response schema
  AnalyzeEmailsResponseSchema: z.object({
    message: z.string(),
    queuedEmails: z.array(z.string()),
  }),

  // Get email analysis results request schema
  GetAnalysisResultsRequestSchema: z.object({
    limit: z
      .number()
      .optional()
      .default(10)
      .describe("Number of results to fetch"),
    offset: z.number().optional().default(0).describe("Offset for pagination"),
    status: z.enum(["PENDING", "PROCESSING", "SUCCESS", "FAILED"]).optional(),
    threatLevel: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).optional(),
  }),

  // Get email analysis results response schema
  GetAnalysisResultsResponseSchema: z.object({
    message: z.string(),
    results: z.array(
      z.object({
        emailId: z.string(),
        gmailThreadId: z.string(),
        snippet: z.string(),
        headers: z.object({
          from: z.string().nullable(),
          subject: z.string().nullable(),
          date: z.string().nullable(),
        }),
        result: z.object({
          threatScore: z.number(),
          threatLevel: z.string(),
          confidence: z.number(),
        }),
        SR: z.string(),
        status: z.string(),
        createdAt: z.string(),
        processedAt: z.string().nullable(),
      })
    ),
    total: z.number(),
  }),

  // Get email analysis details request schema
  GetAnalysisDetailsRequestSchema: z.object({
    emailId: z.string().describe("Email ID to get details for"),
  }),

  // Get email analysis details response schema
  GetAnalysisDetailsResponseSchema: z.object({
    message: z.string(),
    result: z.object({
      emailId: z.string(),
      gmailThreadId: z.string(),
      snippet: z.string(),
      headers: z.object({
        from: z.string().nullable(),
        to: z.string().nullable(),
        subject: z.string().nullable(),
        date: z.string().nullable(),
        messageId: z.string().nullable(),
        contentType: z.string().nullable(),
        replyTo: z.string().nullable(),
        cc: z.string().nullable(),
        receivedDate: z.string().nullable(),
        xHeaders: z.record(z.string(), z.string()).nullable(),
        authenticationResults: z.record(z.string(), z.string()).nullable(),
      }),
      result: z.object({
        threatScore: z.number(),
        threatLevel: z.string(),
        confidence: z.number(),
        scanEngines: z.array(
          z.object({
            name: z.string(),
            result: z.string(),
            confidence: z.number(),
            risk_level: z.string().nullable(),
            details: z.string().nullable(),
            updateDate: z.string().nullable(),
          })
        ),
      }),
      SR: z.string(),
      status: z.string(),
      createdAt: z.string(),
      processedAt: z.string().nullable(),
    }),
  }),
};

// Define routes
export const routes = {
  // Fetch recent emails from Gmail
  fetchEmails: createRoute({
    method: "get",
    path: "/email/recent",
    tags,
    request: {
      query: schemas.FetchEmailsRequestSchema,
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.FetchEmailsResponseSchema,
        "Recent emails fetched successfully"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Unauthorized"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Server error"
      ),
    },
  }),

  // Analyze emails
  analyzeEmails: createRoute({
    method: "post",
    path: "/email/analyze",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.AnalyzeEmailsRequestSchema,
        "Analyze emails request"
      ),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.AnalyzeEmailsResponseSchema,
        "Emails queued for analysis"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Unauthorized"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Server error"
      ),
    },
  }),

  // Get email analysis results
  getAnalysisResults: createRoute({
    method: "get",
    path: "/email/analysis",
    tags,
    request: {
      query: schemas.GetAnalysisResultsRequestSchema,
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.GetAnalysisResultsResponseSchema,
        "Email analysis results fetched successfully"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Unauthorized"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Server error"
      ),
    },
  }),

  // Get email analysis details
  getAnalysisDetails: createRoute({
    method: "get",
    path: "/email/analysis/:emailId",
    tags,
    request: {
      params: z.object({
        emailId: z.string(),
      }),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.GetAnalysisDetailsResponseSchema,
        "Email analysis details fetched successfully"
      ),
      [HttpStatusCodes.NOT_FOUND]: jsonContent(
        z.object({ message: z.string() }),
        "Analysis not found"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Unauthorized"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Server error"
      ),
    },
  }),
};
