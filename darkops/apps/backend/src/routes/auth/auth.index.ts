import { createRouter } from "@/lib/create-app.js";
import * as handlers from "./auth.handlers.ts";
import { routes } from "./auth.routes.ts";
import { OpenAPIHono } from "@hono/zod-openapi";
import type { AppBindings } from "@/lib/types.ts";
import { isAuth } from "@/middlewares/auth.js";

// Explicitly type the router as OpenAPIHono with AppBindings
const router = createRouter() as OpenAPIHono<AppBindings>;

router
  .openapi(routes.signup, handlers.signupHandler)
  .openapi(routes.login, handlers.loginHandler)
  .openapi(routes.forgotPassword, handlers.forgotPasswordHandler)
  .openapi(routes.resetPassword, handlers.resetPasswordHandler)
  .openapi(routes.verifyEmail, handlers.verifyEmailHandler)
  .openapi(routes.resendVerification, handlers.resendVerificationHandler)
  .openapi(routes.logout, isAuth(handlers.logoutHandler))
  .openapi(routes.googleAuth, handlers.googleAuthHandler)
  .openapi(routes.googleCallback, handlers.googleCallbackHandler)
  .openapi(routes.me, handlers.meHandler)
  .openapi(routes.emails, handlers.getEmailsHandler)
  .openapi(routes.checkGoogleConnection, handlers.checkGoogleConnectionHandler);

export default router;
