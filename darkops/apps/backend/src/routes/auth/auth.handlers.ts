import db from "@/db/index.ts";
import type { AppBindings } from "@/lib/types.js";
import type { <PERSON><PERSON> } from "hono";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { and, eq } from "drizzle-orm";
import { accounts, sessions, users, verifications } from "@/db/schema.js";
import {
  generateId,
  generateOTP,
  hashPassword,
  verifyPassword,
} from "@/utils/auth.js";
import { sendOTPEmail } from "@/utils/mailer.js";
import jwt from "jsonwebtoken";
import env from "@/env.js";
import { googleOAuth, getGoogleUser, GOOGLE_SCOPES } from "@/lib/google.js";
import { getRecentEmails, saveAsEML } from "@/lib/gmail.js";
import type { Context } from "hono";

export const signupHandler: Handler<AppBindings> = async (c: Context) => {
  const { email, password, name } = await c.req.json();

  // Check existing user
  const existingUser = await db
    .select()
    .from(users)
    .where(eq(users.email, email));

  if (existingUser.length > 0) {
    return c.json(
      { message: "Email already exists" },
      HttpStatusCodes.CONFLICT
    );
  }

  // Hash password
  const hashedPassword = await hashPassword(password);
  const userId = generateId(16);

  try {
    // Create user
    await db.insert(users).values({
      id: userId,
      email,
      name,
      emailVerified: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Create email/password account
    await db.insert(accounts).values({
      id: generateId(16),
      userId,
      accountId: email,
      providerId: "email",
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    //Generate Verification OTP
    const otp = generateOTP();
    await db.insert(verifications).values({
      id: generateId(16),
      identifier: email,
      value: otp,
      expiresAt: new Date(Date.now() + 600000), // OTP Duration 10 minutes
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    try {
      // Send OTP to email with nodemailer
      await sendOTPEmail(email, otp, "verification");
    } catch (emailError: any) {
      console.error("Email sending error:", emailError);

      // Continue with signup but inform user about email issue
      return c.json(
        {
          message:
            "Signup successful but verification email failed to send. Please contact support.",
          error: emailError.message || "Email service error",
          user: {
            id: userId,
            email,
            name,
            emailVerified: false,
            createdAt: new Date().toISOString(),
          },
        },
        HttpStatusCodes.CREATED
      );
    }

    return c.json(
      {
        message: "Signup successful. Check your email for verification.",
        user: {
          id: userId,
          email,
          name,
          emailVerified: false,
          createdAt: new Date().toISOString(),
        },
      },
      HttpStatusCodes.CREATED
    );
  } catch (error: any) {
    console.error("Signup error:", error);

    // Provide more specific error messages based on error type
    if (error.code === "23505") {
      // PostgreSQL unique violation
      return c.json(
        { message: "Email already exists" },
        HttpStatusCodes.CONFLICT
      );
    }

    return c.json(
      {
        message: "Error creating user",
        error: error.message || "Database error",
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const loginHandler: Handler<AppBindings> = async (c: Context) => {
  const { email, password, rememberMe } = await c.req.json();

  //Find user
  const [user] = await db.select().from(users).where(eq(users.email, email));
  // Check if user exists
  if (!user)
    return c.json(
      { message: "Invalid Credentials" },
      HttpStatusCodes.UNAUTHORIZED
    );

  //Find email/password account
  const [account] = await db
    .select()
    .from(accounts)
    .where(and(eq(accounts.userId, user.id), eq(accounts.providerId, "email")));

  if (!account?.password) {
    return c.json(
      { message: "Invalid Credentials" },
      HttpStatusCodes.UNAUTHORIZED
    );
  }

  // Verify password
  const validPassword = await verifyPassword(password, account.password);
  if (!validPassword)
    return c.json(
      { message: "Invalid Credentials" },
      HttpStatusCodes.UNAUTHORIZED
    );

  // Generate JWT token
  const token = jwt.sign({ userId: user.id }, env.JWT_SECRET, {
    expiresIn: rememberMe ? "7d" : "1h",
  });

  //Create Session
  await db.insert(sessions).values({
    id: generateId(16),
    userId: user.id,
    token,
    expiresAt: new Date(Date.now() + (rememberMe ? ********* : 3600000)),
    ipAddress: c.req.header("x-forwarded-for") || "",
    userAgent: c.req.header("User-Agent") || "",
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  return c.json(
    {
      message: "Login Successful",
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified,
      },
    },
    HttpStatusCodes.OK
  );
};

export const forgotPasswordHandler: Handler<AppBindings> = async (
  c: Context
) => {
  const { email } = await c.req.json();

  //verify user exists
  const [user] = await db.select().from(users).where(eq(users.email, email));
  if (!user)
    return c.json({ message: "Email not found" }, HttpStatusCodes.NOT_FOUND);

  try {
    // Generate Reset OTP
    const otp = generateOTP();
    await db.insert(verifications).values({
      id: generateId(16),
      identifier: email,
      value: otp,
      expiresAt: new Date(Date.now() + 600000), // OTP Duration 10 minutes
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    try {
      // Send OTP to email
      await sendOTPEmail(email, otp, "password_reset");
    } catch (emailError: any) {
      console.error("Password reset email error:", emailError);
      return c.json(
        {
          message: "Failed to send password reset email",
          error: emailError.message || "Email service error",
        },
        HttpStatusCodes.INTERNAL_SERVER_ERROR
      );
    }

    return c.json(
      { message: "Check your email for password reset OTP" },
      HttpStatusCodes.OK
    );
  } catch (error: any) {
    console.error("Forgot password error:", error);
    return c.json(
      {
        message: "Error processing password reset request",
        error: error.message || "Database error",
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

// When the user wants to change their password, they will provide the OTP sent to their email (in the Profile page)
export const resetPasswordHandler: Handler<AppBindings> = async (
  c: Context
) => {
  const { email, otp, newPassword } = await c.req.json();

  //Validate OTP
  const [verification] = await db
    .select()
    .from(verifications)
    .where(
      and(eq(verifications.identifier, email), eq(verifications.value, otp))
    );

  if (!verification || verification.expiresAt < new Date()) {
    return c.json({ message: "Invalid OTP" }, HttpStatusCodes.BAD_REQUEST);
  }

  // Update password
  const hashedPassword = await hashPassword(newPassword);
  await db
    .update(accounts)
    .set({ password: hashedPassword })
    .where(eq(accounts.accountId, email));

  // Delete verification entry
  await db.delete(verifications).where(eq(verifications.identifier, email));

  return c.json({ message: "Password reset successful" }, HttpStatusCodes.OK);
};

export const verifyEmailHandler: Handler<AppBindings> = async (c: Context) => {
  const { email, otp } = await c.req.json();

  try {
    // Validate OTP
    const [verification] = await db
      .select()
      .from(verifications)
      .where(
        and(eq(verifications.identifier, email), eq(verifications.value, otp))
      );

    if (!verification) {
      return c.json({ message: "Invalid OTP" }, HttpStatusCodes.BAD_REQUEST);
    }

    if (new Date() > verification.expiresAt) {
      return c.json(
        { message: "OTP has expired" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    // Update user verification status
    await db
      .update(users)
      .set({ emailVerified: true })
      .where(eq(users.email, email));

    // Delete verification entry
    await db.delete(verifications).where(eq(verifications.id, verification.id));

    return c.json(
      { message: "Email verified successfully" },
      HttpStatusCodes.OK
    );
  } catch (error: any) {
    console.error("Email verification error:", error);
    return c.json(
      {
        message: "Error verifying email",
        error: error.message || "Database error",
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const resendVerificationHandler: Handler<AppBindings> = async (
  c: Context
) => {
  const { email } = await c.req.json();

  try {
    // Check if user exists
    const [user] = await db.select().from(users).where(eq(users.email, email));

    if (!user) {
      return c.json({ message: "Email not found" }, HttpStatusCodes.NOT_FOUND);
    }

    // Check if email is already verified
    if (user.emailVerified) {
      return c.json(
        { message: "Email is already verified" },
        HttpStatusCodes.CONFLICT
      );
    }

    // Delete any existing verification entries for this email
    await db.delete(verifications).where(eq(verifications.identifier, email));

    // Generate new OTP
    const otp = generateOTP();

    // Save new verification entry
    await db.insert(verifications).values({
      id: generateId(16),
      identifier: email,
      value: otp,
      expiresAt: new Date(Date.now() + 600000), // OTP Duration 10 minutes
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    try {
      // Send OTP to email
      await sendOTPEmail(email, otp, "verification");
    } catch (emailError: any) {
      console.error("Email sending error:", emailError);

      return c.json(
        {
          message: "Failed to send verification email",
          error: emailError.message || "Email service error",
        },
        HttpStatusCodes.INTERNAL_SERVER_ERROR
      );
    }

    return c.json(
      { message: "Verification email resent successfully" },
      HttpStatusCodes.OK
    );
  } catch (error: any) {
    console.error("Resend verification error:", error);
    return c.json(
      {
        message: "Error resending verification email",
        error: error.message || "Database error",
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const logoutHandler: Handler<AppBindings> = async (c: Context) => {
  // Get user from context (set by authMiddleware)
  const user = c.get("user");
  const token = c.req.header("Authorization")?.split(" ")[1];

  if (!user || !token) {
    return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
  }

  try {
    // Delete session
    await db.delete(sessions).where(eq(sessions.token, token));
    return c.json({ message: "Logged out successfully" }, HttpStatusCodes.OK);
  } catch (error) {
    console.error("Logout error:", error);
    return c.json(
      { message: "Error during logout" },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const googleAuthHandler: Handler<AppBindings> = async (c: Context) => {
  const url = googleOAuth.generateAuthUrl({
    access_type: "offline",
    scope: GOOGLE_SCOPES,
  });

  return c.redirect(url);
};

export const googleCallbackHandler: Handler<AppBindings> = async (
  c: Context
) => {
  try {
    const code = c.req.query("code");
    if (!code) {
      return c.json(
        { message: "Authorization code required" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const { tokens } = await googleOAuth.getToken(code);
    const googleUser = await getGoogleUser(tokens.access_token!);

    if (!googleUser) {
      return c.json(
        { message: "Failed to get user info" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    // Check if user exists
    let [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, googleUser.email));

    if (!user) {
      // Create new user
      const userId = generateId(16);
      await db.insert(users).values({
        id: userId,
        email: googleUser.email,
        name: googleUser.name,
        image: googleUser.picture,
        emailVerified: googleUser.verified_email,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      user = {
        id: userId,
        email: googleUser.email,
        name: googleUser.name,
        image: googleUser.picture,
        emailVerified: googleUser.verified_email,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    // Create or update Google account
    await db
      .insert(accounts)
      .values({
        id: generateId(16),
        userId: user.id,
        accountId: googleUser.id,
        providerId: "google",
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        accessTokenExpiresAt: tokens.expiry_date
          ? new Date(tokens.expiry_date)
          : null,
        idToken: tokens.id_token,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: [accounts.accountId, accounts.providerId],
        set: {
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          accessTokenExpiresAt: tokens.expiry_date
            ? new Date(tokens.expiry_date)
            : null,
          idToken: tokens.id_token,
          updatedAt: new Date(),
        },
      });

    // Generate JWT token
    const token = jwt.sign({ userId: user.id }, env.JWT_SECRET, {
      expiresIn: "7d",
    });

    // Create session for Google login
    await db.insert(sessions).values({
      id: generateId(16),
      userId: user.id,
      token,
      expiresAt: new Date(Date.now() + *********), // 7 days
      ipAddress: c.req.header("x-forwarded-for") || "",
      userAgent: c.req.header("User-Agent") || "",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Redirect to frontend with auth data
    const frontendCallbackUrl = `${process.env.FRONTEND_URL || "http://localhost:3000"}/auth/google/callback`;
    const authData = {
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
      },
    };

    const params = new URLSearchParams({
      token,
      userData: JSON.stringify(authData.user),
    });

    return c.redirect(`${frontendCallbackUrl}?${params.toString()}`);
  } catch (error) {
    console.error("Google OAuth Error:", error);
    const frontendErrorUrl = `${process.env.FRONTEND_URL || "http://localhost:3000"}/login?error=auth_failed`;
    return c.redirect(frontendErrorUrl);
  }
};

export const meHandler: Handler<AppBindings> = async (c: Context) => {
  const token = c.req.header("Authorization")?.split(" ")[1];
  if (!token) {
    return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as { userId: string };
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, decoded.userId));

    if (!user) {
      return c.json(
        { message: "User not found" },
        HttpStatusCodes.UNAUTHORIZED
      );
    }

    return c.json({
      message: "User profile retrieved successfully",
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
        emailVerified: user.emailVerified,
      },
    });
  } catch (error) {
    return c.json({ message: "Invalid token" }, HttpStatusCodes.UNAUTHORIZED);
  }
};

export const checkGoogleConnectionHandler: Handler<AppBindings> = async (
  c: Context
) => {
  const token = c.req.header("Authorization")?.split(" ")[1];
  if (!token) {
    return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as { userId: string };
    const [account] = await db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.userId, decoded.userId),
          eq(accounts.providerId, "google")
        )
      );

    if (!account?.accessToken) {
      return c.json({
        message: "Google account not connected",
        isConnected: false,
      });
    }

    // Check if token is expired
    if (
      account.accessTokenExpiresAt &&
      new Date() > account.accessTokenExpiresAt
    ) {
      return c.json({
        message: "Google token expired. Please re-authenticate.",
        isConnected: false,
        isExpired: true,
      });
    }

    return c.json({
      message: "Google account is connected",
      isConnected: true,
    });
  } catch (error) {
    console.error("Error checking Google connection:", error);
    return c.json(
      {
        message: "Failed to check Google connection",
        isConnected: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const getEmailsHandler: Handler<AppBindings> = async (c: Context) => {
  const token = c.req.header("Authorization")?.split(" ")[1];
  if (!token) {
    return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as { userId: string };
    const [account] = await db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.userId, decoded.userId),
          eq(accounts.providerId, "google")
        )
      );

    if (!account?.accessToken) {
      return c.json(
        { message: "Google account not connected" },
        HttpStatusCodes.UNAUTHORIZED
      );
    }

    // Check if token is expired
    if (
      account.accessTokenExpiresAt &&
      new Date() > account.accessTokenExpiresAt
    ) {
      return c.json(
        { message: "Google token expired. Please re-authenticate." },
        HttpStatusCodes.UNAUTHORIZED
      );
    }

    try {
      const emails = await getRecentEmails(account.accessToken);

      // Save each email as EML file
      const emlPaths = await Promise.all(
        emails.map(async (email) => {
          const filename = `${email.id}_${Date.now()}`;
          const emlPath = await saveAsEML(email.raw, filename);
          return { id: email.id, path: emlPath };
        })
      );

      return c.json({
        message: "Emails retrieved successfully",
        emails,
        emlFiles: emlPaths,
      });
    } catch (error) {
      console.error("Gmail API Error:", error);
      return c.json(
        { message: "Failed to fetch emails from Gmail" },
        HttpStatusCodes.INTERNAL_SERVER_ERROR
      );
    }
  } catch (error) {
    console.error("Error:", error);
    return c.json(
      { message: "Failed to fetch emails" },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};
