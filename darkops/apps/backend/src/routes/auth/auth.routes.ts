import { createRoute, z } from "@hono/zod-openapi";
import { json<PERSON>ontent, jsonContentRequired } from "stoker/openapi/helpers";
import * as HttpStatusCodes from "stoker/http-status-codes";
import * as schemas from "./auth.schemas.js";

const tags = ["Auth"];

export const routes = {
  signup: createRoute({
    method: "post",
    path: "/auth/signup",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.SignupRequestSchema,
        "Signup request payload"
      ),
    },
    responses: {
      [HttpStatusCodes.CREATED]: jsonContent(
        schemas.SignupResponseSchema,
        "Signup successful. Verification email sent"
      ),
      [HttpStatusCodes.CONFLICT]: jsonContent(
        z.object({ message: z.string() }),
        "Email already exists"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Server error during registration"
      ),
    },
  }),

  login: createRoute({
    method: "post",
    path: "/auth/signin",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.LoginRequestSchema,
        "Login request payload"
      ),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.LoginResponseSchema,
        "Login successful"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Invalid credentials"
      ),
    },
  }),

  forgotPassword: createRoute({
    method: "post",
    path: "/auth/forgot-password",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.ForgotPasswordRequestSchema,
        "Forgot password request payload"
      ),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.ForgotPasswordResponseSchema,
        "Password reset email sent"
      ),
      [HttpStatusCodes.NOT_FOUND]: jsonContent(
        z.object({ message: z.string() }),
        "Email not found"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Failed to send reset email"
      ),
    },
  }),

  resetPassword: createRoute({
    method: "post",
    path: "/auth/reset-password",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.ResetPasswordRequestSchema,
        "Reset password request payload"
      ),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.ResetPasswordResponseSchema,
        "Password reset successful"
      ),
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        z.object({ message: z.string() }),
        "Invalid OTP or expired"
      ),
    },
  }),

  verifyEmail: createRoute({
    method: "post",
    path: "/auth/verify-email",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.VerifyEmailRequestSchema,
        "Email verification request payload"
      ),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.VerifyEmailResponseSchema,
        "Email verification successful"
      ),
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        z.object({ message: z.string() }),
        "Invalid OTP or expired"
      ),
    },
  }),

  resendVerification: createRoute({
    method: "post",
    path: "/auth/resend-verification",
    tags,
    request: {
      body: jsonContentRequired(
        schemas.ResendVerificationRequestSchema,
        "Resend verification email request payload"
      ),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.ResendVerificationResponseSchema,
        "Verification email resent successfully"
      ),
      [HttpStatusCodes.CONFLICT]: jsonContent(
        z.object({ message: z.string() }),
        "Email already verified"
      ),
      [HttpStatusCodes.NOT_FOUND]: jsonContent(
        z.object({ message: z.string() }),
        "Email not found"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({ message: z.string() }),
        "Failed to send verification email"
      ),
    },
  }),

  logout: createRoute({
    method: "post",
    path: "/auth/logout",
    tags,
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.LogoutResponseSchema,
        "Logout successful"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Missing or invalid token"
      ),
    },
  }),

  googleAuth: createRoute({
    method: "get",
    path: "/auth/google",
    tags,
    responses: {
      [HttpStatusCodes.TEMPORARY_REDIRECT]: {
        description: "Redirect to Google OAuth consent screen",
      },
    },
  }),
  googleCallback: createRoute({
    method: "get",
    path: "/auth/google/callback",
    tags,
    request: {
      query: z.object({
        code: z.string(),
        state: z.string().optional(),
      }),
    },
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.OAuthResponseSchema,
        "Google OAuth successful"
      ),
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        z.object({ message: z.string() }),
        "Invalid OAuth callback"
      ),
    },
  }),

  me: createRoute({
    method: "get",
    path: "/auth/me",
    tags,
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        schemas.MeResponseSchema,
        "User profile retrieved successfully"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Missing or invalid token"
      ),
    },
  }),

  emails: createRoute({
    method: "get",
    path: "/auth/emails",
    tags,
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        z.object({
          message: z.string(),
          emails: z.array(
            z.object({
              id: z.string(),
              threadId: z.string(),
              snippet: z.string(),
              subject: z.string().optional(),
              from: z.string().optional(),
              date: z.string().optional(),
            })
          ),
        }),
        "Emails retrieved successfully"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Unauthorized"
      ),
    },
  }),

  checkGoogleConnection: createRoute({
    method: "get",
    path: "/auth/check-google-connection",
    tags,
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        z.object({
          message: z.string(),
          isConnected: z.boolean(),
          isExpired: z.boolean().optional(),
        }),
        "Google connection status"
      ),
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        z.object({ message: z.string() }),
        "Unauthorized"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        z.object({
          message: z.string(),
          isConnected: z.boolean(),
          error: z.string().optional(),
        }),
        "Error checking Google connection"
      ),
    },
  }),
} as const;
