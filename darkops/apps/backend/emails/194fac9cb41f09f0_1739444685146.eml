Delivered-To: <EMAIL>
Received: by 2002:a05:612c:884:b0:4b7:725a:7004 with SMTP id gg4csp7244vqb;
        Wed, 12 Feb 2025 07:31:56 -0800 (PST)
X-Google-Smtp-Source: AGHT+IFakSlUKI7iuTqagr7jsGj7vs33Nc4mtsf236+PBvD7JBk1jsopaCi7Q1Szf7nEZBvLnRHU
X-Received: by 2002:a17:903:2385:b0:21f:3e2d:7d40 with SMTP id d9443c01a7336-220bbaf7c9cmr50334855ad.18.1739374316406;
        Wed, 12 Feb 2025 07:31:56 -0800 (PST)
ARC-Seal: i=1; a=rsa-sha256; t=1739374316; cv=none;
        d=google.com; s=arc-20240605;
        b=BaJ7K5RIzMtpl6M5FFJ9DbXKTHQR4OF48k0SUMSWYDunwu9yBjovE+eWfzd7WF5NLs
         aHX/sGbJAKV+anf8FRvC+9pHVirPbWpZrQ2wr9et35NfolNW4AinLiW38+z2pEe4kiPF
         9mpl4SqWGABaW6fVdu8SRX9lQCF0F35ZM5OSayz7aLGU8DO4GGgGRMSaAehNxSa3V2no
         h3O+oisHbkxki/iCbsB21UNdkSuIp96FoAwOt0wEVZsbg/9OJ3w4jzAANpXe3AAL4+H4
         9I3fOf20t4zsZqTHevnIxxkhi0LboFy3RzLZHQUPeIbmdMOrBFobBXekNh8rMg0slMEt
         FE5g==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=require-recipient-valid-since:feedback-id:list-unsubscribe-post
         :list-unsubscribe:date:to:mime-version:subject:message-id:from
         :dkim-signature:dkim-signature;
        bh=oz4z4UV5iD345t3qBDpqDEYNebSwfsByt8+HcWra4YI=;
        fh=wWzJLaixNKcc85d8NSKuGZjgZUuCbmE6EP1F5UkZyc0=;
        b=SCnB06Tj9Jbf02pmboSVHofWI1wD8IgEzaL7wflJZKK/P2QYgifIQR7oiNncIhUXQO
         iDIxhpkX/fAu6RJKpbIuUj/Z/9uCNgGrqFikeqUcCAgt0aQLsVVoXeoutVpAlxz4nSs+
         Gm1Wy8QsQQcn3jdNpCm1TDu13KKRZKu3ayqaOJv4WSNdnfOJQJfJXAYBlbkzweoPpOit
         u3aNT2BHrcsSMzsPdVpAn8Aq1KNBUiuYAwOc5wf46u07gZtMplv/oc7USYYCbZQqwnWb
         VxRUgLItS4IbtR/doN9OLq2mmHzJEVeK0b3WsO4MLOkTa52cu/Hqk8+PxQWOfX3S9HzK
         xB5g==;
        dara=google.com
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@mailb.linkedin.com header.s=d2048-202308-0b header.b=u2ubqOAs;
       dkim=pass header.i=@linkedin.com header.s=d2048-202308-00 header.b=G4cpSTeP;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=linkedin.com
Return-Path: <<EMAIL>>
Received: from mailb-dc.linkedin.com (mailb-dc.linkedin.com. [*************])
        by mx.google.com with ESMTPS id d9443c01a7336-220c16798b8si17216735ad.596.2025.***********.54
        for <<EMAIL>>
        (version=TLS1_2 cipher=ECDHE-ECDSA-AES128-GCM-SHA256 bits=128/128);
        Wed, 12 Feb 2025 07:31:56 -0800 (PST)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@mailb.linkedin.com header.s=d2048-202308-0b header.b=u2ubqOAs;
       dkim=pass header.i=@linkedin.com header.s=d2048-202308-00 header.b=G4cpSTeP;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=linkedin.com
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=mailb.linkedin.com;
	s=d2048-202308-0b; t=1739374303;
	bh=oz4z4UV5iD345t3qBDpqDEYNebSwfsByt8+HcWra4YI=;
	h=From:Subject:MIME-Version:Content-Type:To:Date:X-LinkedIn-Class:
	 X-LinkedIn-Template:X-LinkedIn-fbl;
	b=u2ubqOAsRgPIKuuo9IRYldbql0tq3FZIezVmvLi9mnbsxp1NO6gzhcGFT1EJB3Jk2
	 uYl07dKGx9eTT9ojGACn/lXDK3VTia8HFktdEC1qYrFzMOaMlgPKIg5qX/cVpfUqZL
	 VTBoSij0iW7/lgn8LHlAtO/weJiqZsux4cuhSUZaBgRuhJ+8WKH21bWcFJxJs57mMv
	 +9Xexgj5A9wJ52oTDAEu/bbPt++BPBOPHkEcZt1lHASgMfQACBGy0ShZtb1WWBZ+Ww
	 riFCARg3ZkHOf4Jps+JQnb/OSSRIONn97MRfBo9e+HuT8A3x8Kl8DVL860nCrPE52H
	 GVAKB7HUxZksw==
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=linkedin.com;
	s=d2048-202308-00; t=1739374303;
	bh=oz4z4UV5iD345t3qBDpqDEYNebSwfsByt8+HcWra4YI=;
	h=From:Subject:MIME-Version:Content-Type:To:Date:X-LinkedIn-Class:
	 X-LinkedIn-Template:X-LinkedIn-fbl;
	b=G4cpSTePWsAWKMYhZ2bZZ9/iZmRhJn2CU2WI5E+e2l24XlXocx9p2QFHFvfS4QfGh
	 gGOdA/YFNfdx4D20gHqvUL6CwvIv5/+4+zS3LddILB61SFyfkrC4r/OerIrqSxEQNn
	 acm84yiPegq0XmskTNtZrnXA6bNXbuN4CqA2e9Gg1d8v/ve4nqH22YzNoWT+/pQQa1
	 jl9sy0UEPgNDCP6Vc8yPtrSM22C5Dsqg/5yvO2jPeWCLukCBt4Puu8sVa1QN/fvNMy
	 hfvr7ivtO3IHyuHXL46xAh+lPmSx8JZygzXsbqT9nW2FmFcuUVUK/3usl3SLRPgHLH
	 6wodytaPgm2EA==
From: LinkedIn <<EMAIL>>
Message-ID: <<EMAIL>>
Subject: Data Entry Specialist role at Gini Talent and other opportunities
 open
MIME-Version: 1.0
Content-Type: multipart/alternative; 
	boundary="----=_Part_3296108_404475567.1739374303617"
To: Abanoub Nashaat <<EMAIL>>
Date: Wed, 12 Feb 2025 15:31:43 +0000 (UTC)
X-LinkedIn-Class: JOBS-TO-MBR
X-LinkedIn-Template: jobs_jymbii_digest
X-LinkedIn-fbl: m2-aszp2ylh9698u0donkesx8xyatxtgsofhkzzpmqrclf40o22jvddocly6w5gvuoaxeig5vc61j7t2hnavctl7qjslxlj22sbtlxm42
X-LinkedIn-Id: fojfqf-m722jtfz-d9
List-Unsubscribe: <https://www.linkedin.com/comm/psettings/email-unsubscribe?lipi=urn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&midToken=AQHb-hvPD2eSGA&midSig=1UWL5KEFK3mbE1&trk=eml-jobs_jymbii_digest-unsub-0-unsub&trkEmail=eml-jobs_jymbii_digest-unsub-0-unsub-null-fojfqf~m722jtfz~d9-null-null&eid=fojfqf-m722jtfz-d9&loid=AQEDooge5KsMWgAAAZT6yZZt7mEiamn3vDLNwUO8_sLRqGuTCqq7Ok7Ab9rbKsKpXRiyq6GWkX-tQvMiQI8Dj85auRAss-qMXf9m1AEO_Q>
List-Unsubscribe-Post: List-Unsubscribe=One-Click
Feedback-ID: jobs_jymbii_digest:linkedin
Require-Recipient-Valid-Since: <EMAIL>; Thu, 4 Nov 2021 20:25:25 +0000

------=_Part_3296108_404475567.1739374303617
Content-Type: text/plain;charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-ID: text-body

Top job picks for you: https://www.linkedin.com/comm/jobs/collections/recom=
mended?origin=3DJYMBII_EMAIL&lgCta=3Deml-jymbii-bottom-see-all-jobs&lgTemp=
=3Djobs_jymbii_digest&lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8=
MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3=
mbE1&trk=3Deml-jobs_jymbii_digest-null-0-null&trkEmail=3Deml-jobs_jymbii_di=
gest-null-0-null-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9=
&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjY=
xNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZG=
MyNjMzNTk4MTk2NjlmNiwxLDE%3D
 =20
         =20
Data Entry Specialist
Gini Talent
Egypt
This company is actively hiring
View job: https://www.linkedin.com/comm/jobs/view/*********9/?trackingId=3D=
jllNb3ItTLS9p7lsOE3%2F0w%3D%3D&refId=3DrNe%2Fm0FZQzGnHiBX7lPz0w%3D%3D&lipi=
=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D=
%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii=
_digest-job_card-0-view_job&trkEmail=3Deml-jobs_jymbii_digest-job_card-0-vi=
ew_job-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=
=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4=
Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk=
4MTk2NjlmNiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Remote Content Analyst(Arabic) - 20930
Turing
Egypt
This company is actively hiring
Apply with resume & profile
View job: https://www.linkedin.com/comm/jobs/view/4145750779/?trackingId=3D=
jLl3v0elSMqHW8ntnDYmuQ%3D%3D&refId=3DOwUyj0JPTmuwNGBD2KJhew%3D%3D&lipi=3Dur=
n%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&m=
idToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_dige=
st-job_card-0-view_job&trkEmail=3Deml-jobs_jymbii_digest-job_card-0-view_jo=
b-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWI=
wMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0Zj=
VkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2N=
jlmNiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Software Frontend Development Engineer
Siemens Digital Industries Software
Qesm 2nd New Cairo
This company is actively hiring
View job: https://www.linkedin.com/comm/jobs/view/4138308626/?trackingId=3D=
tidhVgfPT4CbB3go1EWA7w%3D%3D&refId=3DnRLJ8MrxRqOWdEzamuiUnA%3D%3D&lipi=3Dur=
n%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&m=
idToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_dige=
st-job_card-0-view_job&trkEmail=3Deml-jobs_jymbii_digest-job_card-0-view_jo=
b-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWI=
wMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0Zj=
VkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2N=
jlmNiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Coders - AI Training [Remote]
Braintrust
Cairo
This company is actively hiring
View job: https://www.linkedin.com/comm/jobs/view/4047619553/?trackingId=3D=
7ERyVzbhSvaLZmYINEysgw%3D%3D&refId=3DqOHUYOvNQAWOdsPmr58cAg%3D%3D&lipi=3Dur=
n%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&m=
idToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_dige=
st-job_card-0-view_job&trkEmail=3Deml-jobs_jymbii_digest-job_card-0-view_jo=
b-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWI=
wMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0Zj=
VkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2N=
jlmNiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Frontend Developer
Gini Talent
Egypt
This company is actively hiring
Apply with resume & profile
View job: https://www.linkedin.com/comm/jobs/view/4138070368/?trackingId=3D=
8DpS9j38S76tJTq56kmh5A%3D%3D&refId=3DdjXaN5KSQnGfXcJDAfJcgw%3D%3D&lipi=3Dur=
n%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&m=
idToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_dige=
st-job_card-0-view_job&trkEmail=3Deml-jobs_jymbii_digest-job_card-0-view_jo=
b-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWI=
wMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0Zj=
VkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2N=
jlmNiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Software Engineer Intern
Blink22
Alexandria
This company is actively hiring
Apply with resume & profile
View job: https://www.linkedin.com/comm/jobs/view/4069865121/?trackingId=3D=
0XqiOmGEQtSvZAbtvuDr6Q%3D%3D&refId=3Dosj173V7ROi2YuOAJxtWlg%3D%3D&lipi=3Dur=
n%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&m=
idToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_dige=
st-job_card-0-view_job&trkEmail=3Deml-jobs_jymbii_digest-job_card-0-view_jo=
b-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWI=
wMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0Zj=
VkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2N=
jlmNiwxLDE%3D

---------------------------------------------------------
 =20
See all jobs https://www.linkedin.com/comm/jobs/collections/recommended?ori=
gin=3DJYMBII_EMAIL&lgCta=3Deml-jymbii-bottom-see-all-jobs&lgTemp=3Djobs_jym=
bii_digest&lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2K=
WCe%2Bs1ExBg%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3D=
eml-jobs_jymbii_digest-null-0-null&trkEmail=3Deml-jobs_jymbii_digest-null-0=
-null-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=
=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4=
Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk=
4MTk2NjlmNiwxLDE%3D

Stand out and get ahead
Apply to jobs where you=E2=80=99re a top applicant based on your skills and=
 experience.
http://www.linkedin.com/comm/premium/products/?upsellOrderOrigin=3DTracking=
%3Av1%3Aemail_jymbii_upsell%3AEmail+Stork%3AMarketing&referenceId=3DAwhXOYq=
hS32MJ6KppD17lg%3D%3D&isSS=3Dfalse&lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymb=
ii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=
=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_digest-jymbii-0-premium~upsell~v2~t=
ext&trkEmail=3Deml-jobs_jymbii_digest-jymbii-0-premium~upsell~v2~text-null-=
fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWIwMTE2ZT=
IxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmM=
mYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwx=
LDE%3D
 =20


----------------------------------------

This email was intended for Abanoub Nashaat (CS Student || Aspiring Softwar=
e Developer)
Learn why we included this: https://www.linkedin.com/help/linkedin/answer/4=
788?lang=3Den&lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7S=
g2KWCe%2Bs1ExBg%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=
=3Deml-jobs_jymbii_digest-SecurityHelp-0-textfooterglimmer&trkEmail=3Deml-j=
obs_jymbii_digest-SecurityHelp-0-textfooterglimmer-null-fojfqf~m722jtfz~d9-=
null-null&eid=3Dfojfqf-m722jtfz-d9&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwN=
GVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2=
ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D
You are receiving Jobs You Might Be Interested In emails.
 https://www.linkedin.com/comm/jobs/alerts?lipi=3Durn%3Ali%3Apage%3Aemail_j=
obs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&midToken=3DAQHb-hvPD2eSG=
A&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_digest-null-0-null&trkEmail=
=3Deml-jobs_jymbii_digest-null-0-null-null-fojfqf~m722jtfz~d9-null-null&eid=
=3Dfojfqf-m722jtfz-d9&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0Yj=
M4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhY=
jVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D=20
Unsubscribe: https://www.linkedin.com/comm/psettings/email-unsubscribe?lipi=
=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D=
%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii=
_digest-unsubscribe-0-textfooterglimmer&trkEmail=3Deml-jobs_jymbii_digest-u=
nsubscribe-0-textfooterglimmer-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojf=
qf-m722jtfz-d9&loid=3DAQEDooge5KsMWgAAAZT6yZZt7mEiamn3vDLNwUO8_sLRqGuTCqq7O=
k7Ab9rbKsKpXRiyq6GWkX-tQvMiQI8Dj85auRAss-qMXf9m1AEO_Q
Help: https://www.linkedin.com/help/linkedin/answer/67?lang=3Den&lipi=3Durn=
%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&mi=
dToken=3DAQHb-hvPD2eSGA&midSig=3D1UWL5KEFK3mbE1&trk=3Deml-jobs_jymbii_diges=
t-help-0-textfooterglimmer&trkEmail=3Deml-jobs_jymbii_digest-help-0-textfoo=
terglimmer-null-fojfqf~m722jtfz~d9-null-null&eid=3Dfojfqf-m722jtfz-d9&otpTo=
ken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljN=
TA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMz=
NTk4MTk2NjlmNiwxLDE%3D

=C2=A9 2025 LinkedIn Corporation, 1zwnj000 West Maude Avenue, Sunnyvale, CA=
 94085.
LinkedIn and the LinkedIn logo are registered trademarks of LinkedIn.
------=_Part_3296108_404475567.1739374303617
Content-Type: text/html;charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-ID: html-body

<html xmlns=3D"http://www.w3.org/1999/xhtml" lang=3D"en" xml:lang=3D"en"> <=
head> <meta http-equiv=3D"Content-Type" content=3D"text/html;charset=3Dutf-=
8"> <meta name=3D"HandheldFriendly" content=3D"true"> <meta name=3D"viewpor=
t" content=3D"width=3Ddevice-width; initial-scale=3D0.666667; user-scalable=
=3D0"> <meta name=3D"viewport" content=3D"width=3Ddevice-width"> <title></t=
itle> <style>
            @media (max-width: 512px) { .mercado-container { width: 100% !i=
mportant; } }
          </style> <style>
            @media (max-width: 480px) { .inline-button, .inline-button tabl=
e { display: none !important; }
            .full-width-button, .full-width-button table { display: table !=
important; } }
          </style> <style>body {font-family: -apple-system, system-ui, Blin=
kMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
            'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid S=
ans', 'Apple Color Emoji', 'Segoe UI Emoji',
            'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica=
, Arial, sans-serif;}</style> <!--[if mso]><style type=3D"text/css"> </styl=
e><![endif]--> <!--[if IE]><style type=3D"text/css"> </style><![endif]--> <=
style>
@media (max-width: 480px) {
  .inline-button,
.inline-button table {
    display: none !important;
  }

  .full-width-button,
.full-width-button table {
    display: table !important;
  }
}
@media (min-width: 576px) {
  .container {
    max-width: 576px;
  }

  .\!container {
    max-width: 576px !important;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }

  .\!container {
    max-width: 768px !important;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 992px;
  }

  .\!container {
    max-width: 992px !important;
  }
}
@media (min-width: 1128px) {
  .container {
    max-width: 1128px;
  }

  .\!container {
    max-width: 1128px !important;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }

  .\!container {
    max-width: 1200px !important;
  }
}
@media (min-width: 1440px) {
  .container {
    max-width: 1440px;
  }

  .\!container {
    max-width: 1440px !important;
  }
}
@media (min-width: 1680px) {
  .container {
    max-width: 1680px;
  }

  .\!container {
    max-width: 1680px !important;
  }
}
@media (min-width: 1920px) {
  .container {
    max-width: 1920px;
  }

  .\!container {
    max-width: 1920px !important;
  }
}
@media (min-width: 992px) {
  .base-detail-page__header .nav {
    margin-left: auto;
    margin-right: auto;
    width: 1128px;
  }
}
@media (max-width: 767px) {
  .embedded-social-share .modal__outlet {
    flex-direction: row;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
}
@media (max-width: 767px) {
  .social-action-bar--grid .embedded-social-share .modal__outlet {
    padding-left: 0px;
    padding-right: 0px;
  }
}
@media (max-width: 991px) {
  .nav .search-bar {
    order: 4;
  }
}
@media (max-width: 767px) {
  .nav .sign-in-card {
    display: none;
  }
}
@media (max-width: 767px) {
  .nav--minified-mobile .nav__cta-container > *:not(.nav__link-person) {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .nav .search-bar--minified-mobile {
    margin-bottom: 0px;
    height: 100%;
    flex-grow: 1;
    padding-top: 5px;
    padding-bottom: 5px;
    order: initial;
  }

  .nav .search-bar--minified-mobile .search-bar__placeholder {
    margin-top: 0px;
  }
}
@media (max-width: 767px) {
  .tw-link-column-item {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
@media (min-width: 992px) {
  .tw-link-column-item {
    margin-bottom: 8px;
  }

  .tw-linkster .tw-link-column-item {
    margin-bottom: 0px;
  }
}
@media (max-width: 767px) {
  .member-nav-menu .collapsible-dropdown__button {
    margin-top: 24px;
    margin-bottom: 24px;
    margin-left: 16px;
  }
}
@media (max-width: 767px) {
  .member-nav-menu .collapsible-dropdown__list {
    right: 0px;
    max-height: calc(100vh - 52px);
    overflow-y: auto;
  }
}
@media (max-width: 991px) {
  .base-search-bar .typeahead-input,
.base-search-bar .search-input {
    margin-bottom: 8px;
    width: 100%;
  }
}
@media (max-width: 767px) {
  .recent-searches.recent-searches--show {
    position: fixed;
    top: 168px;
    left: 0px;
    width: 100vw;
    min-width: 100vw;
    border-width: 0;
  }
}
@media (max-width: 991px) {
  .search-bar .dismissable-input {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0.75);
    color: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 14px;
    padding-bottom: 14px;
    font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', =
Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Canta=
rell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji'=
, 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif;
    font-size: 16px;
  }

  .search-bar .dismissable-input:hover {
    background-color: rgba(0, 0, 0, 0.04);
    border-color: rgba(0, 0, 0, 0.9);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.9);
    cursor: pointer;
    border-width: 1px;
  }

  .search-bar .dismissable-input:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.9);
    cursor: pointer;
    border-width: 1px;
  }

  .search-bar .dismissable-input:disabled {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:disabled:hover {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:disabled:focus {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:disabled:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input {
    background-color: rgba(0, 0, 0, 0);
    border-color: #cf0007;
    color: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 14px;
    padding-bottom: 14px;
    font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', =
Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Canta=
rell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji'=
, 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif;
    font-size: 16px;
  }

  .input-error .search-bar .dismissable-input:hover {
    background-color: rgba(0, 0, 0, 0.04);
    border-color: #8a0005;
    box-shadow: 0 0 0 1px #8a0005;
    cursor: pointer;
    border-width: 1px;
  }

  .input-error .search-bar .dismissable-input:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: #8a0005;
    cursor: pointer;
    border-width: 1px;
  }

  .input-error .search-bar .dismissable-input:disabled {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input:disabled:hover {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input:disabled:focus {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input:disabled:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .base-search-bar .input-error .search-bar .dismissable-input.typeahead-in=
put {
    border-style: solid;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .base-search-bar .input-error .search-bar .dismissable-input.typeahead-in=
put:active {
    border-style: solid;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .base-search-bar .input-error .search-bar .dismissable-input.typeahead-in=
put:focus-within {
    border-style: solid;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }
}
@media (min-width: 992px) {
  .search-bar .dismissable-input {
    width: 0px;
    background-color: rgba(0, 0, 0, 0);
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:hover {
    border-right-color: rgba(0, 0, 0, 0.08);
  }
}
@media (max-width: 991px) {
  .search-bar:not(.isExpanded) .base-search-bar {
    display: none !important;
  }
}
@media screen and (max-width: 991px) {
  @media (max-width: 991px) {
    .search-bar.isExpanded .search-bar__placeholder {
      display: none;
    }

    .search-bar.isExpanded .base-search-bar__form {
      display: flex;
    }

    position: fixed;
        top: 0px;
        bottom: 0px;
        left: 0px;
        right: 0px;
        z-index: 1000;
        margin-bottom: 0px;
        height: 100%;
        background-color: #ffffff;
    .search-bar.isExpanded .switcher-tabs__cancel-btn,
.search-bar.isExpanded .switcher-tabs {
      display: inherit;
    }

  }
}
@media screen and (min-width: 992px) {
  .switcher-tabs .switcher-tabs__button:after {
    border-bottom: none;
  }
}
@media (max-width: 767px) {
  .tabs__list {
    padding-left: 16px;
    padding-right: 16px;
  }
}
@media (max-width: 767px) {
  .typeahead-input__dropdown.typeahead-input__dropdown--show {
    position: fixed;
    top: 158px;
    left: 0px;
    width: 100vw;
    min-width: 100vw;
  }
}
@media (max-width: 991px) {
  .base-search-bar .dismissable-input {
    max-height: 40px;
  }
}
@media (max-width: 767px) {
  .base-main-card__media ~ .base-main-card__info {
    margin-left: 0px;
    margin-top: 16px;
  }

  .base-main-card__media ~ .base-main-card__info.article-card-social-action=
s {
    margin-left: 12px;
    margin-top: 0px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-img {
    width: 48px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"][data-entity-type=3D"orga=
nization"] .pub-entity-img img {
    height: 48px;
    width: 48px;
    border-radius: 0px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"][data-entity-type=3D"memb=
er"] .pub-entity-img img {
    height: 56px;
    width: 56px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text {
    padding-left: 12px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text h1 {
    margin-bottom: 2px;
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text h3 {
    margin-bottom: 2px;
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text p {
    margin-bottom: 2px;
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .mx-main-feed-card-no-gutter {
    margin-left: -16px;
    margin-right: -16px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mx-main-feed-card-no-gutter {
    margin-left: -16px;
    margin-right: -16px;
  }
}
@media (max-width: 767px) {
  .px-main-feed-card-no-gutter {
    padding-left: 16px;
    padding-right: 16px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .px-main-feed-card-no-gutter {
    margin-left: -16px;
    margin-right: -16px;
    padding-left: 16px;
    padding-right: 16px;
  }
}
@media (max-width: 767px) {
  .main-feed-activity-card__header {
    padding-right: 40px;
  }
}
@media (min-width: 992px) {
  .filter .collapsible-dropdown__list {
    max-height: 400px;
  }
}
@media (forced-colors: active) {
  .filter-button--selected {
    border-width: 4px;
  }
}
@media (max-width: 767px) {
  .base-card .duration {
    font-size: 12px;
    font-weight: 400;
  }
}
@media (max-width: 767px) {
  .hide-on-mobile {
    display: none;
  }
}
@media (max-width: 767px) {
  .social-action-bar__button {
    flex-direction: row;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
}
@media (max-width: 767px) {
  .social-action-bar__icon--svg {
    margin: 0px;
    height: 24px;
    width: 24px;
  }

  .social-action-bar__button-text {
    padding-left: 4px;
  }
}
@media screen and (max-width: 767px) {
  .social-action-bar .social-action-bar__button--transcript {
    flex-direction: column;
    padding-top: 4px;
    padding-bottom: 4px;
    font-size: 12px;
  }

  .social-action-bar .social-action-bar__icon--transcript {
    margin-right: 0px !important;
    margin-bottom: 4px !important;
  }

  .social-action-bar__button-text--transcript {
    padding-left: 0px !important;
  }
}
@media (max-width: 767px) {
  .social-action-bar--grid .social-action-bar__button,
.social-action-bar--grid .social-share {
    padding-left: 0px;
    padding-right: 0px;
  }
}
@media (min-width: 245px) {
  .social-action-bar--grid {
    grid-template-columns: repeat(2,1fr);
  }

  .social-action-bar--grid .share-button {
    grid-column: span 2;
  }
}
@media (min-width: 330px) {
  .social-action-bar--grid {
    grid-template-columns: repeat(auto-fit,minmax(90px,1fr));
  }

  .social-action-bar--grid .share-button {
    grid-column: span 1;
  }
}
@media only screen and (min-width: 30.06em) {
  .card-cta-container-mobile {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
  }

  .card-cta-container-desktop {
    display: block !important;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    overflow: visible !important;
  }
}
@media (min-width: 768px) {
  .after\:pointer-events-none::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:absolute::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:top-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:right-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:bottom-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:left-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:\!left-\[-19px\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:\!top-\[6px\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:ml-0\.5::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:ml-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:ml-\[74px\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:block::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:h-full::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:w-full::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:rounded-full::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:border-1::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:border-solid::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:border-color-divider::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:bg-gradient-to-b::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:from-color-transparent::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:to-color-background-scrim::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:text-color-text-low-emphasis::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:no-underline::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:shadow-\[0_0_0_500px_var\(--color-scrim\)\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:content-\[\'\/\'\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:content-\[\"\"\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:content-\[\'\'\]::after {
    content: "";
  }
}
@media (prefers-color-scheme: dark) {
  .dark\:bg-color-surface-accent-4 {
    background-color: #dde7f1;
  }
}
@media (min-width: 992px) {
  .md\:overflow-x-hidden {
    overflow-x: hidden;
  }
}
@media (max-width: 767px) {
  .babybear\:\!-top-0\.5 {
    top: -4px !important;
  }

  .babybear\:\!-top-0 {
    top: -0px !important;
  }

  .babybear\:right-4 {
    right: 32px;
  }

  .babybear\:bottom-0 {
    bottom: 0px;
  }

  .babybear\:top-0\.5 {
    top: 4px;
  }

  .babybear\:top-0 {
    top: 0px;
  }

  .babybear\:bottom-\[120\%\] {
    bottom: 120%;
  }

  .babybear\:left-3 {
    left: 24px;
  }

  .babybear\:-top-1 {
    top: -8px;
  }

  .babybear\:z-0 {
    z-index: 0;
  }

  .babybear\:order-last {
    order: 9999;
  }

  .babybear\:btn-sm {
    height: min-content;
    min-height: 32px;
    border-radius: 24px;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 16px;
    padding-right: 16px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
  }

  .babybear\:btn-sm,
.babybear\:btn-sm:visited,
.babybear\:btn-sm:focus {
    cursor: pointer;
    text-decoration-line: none;
  }

  .babybear\:btn-sm:hover,
.babybear\:btn-sm:visited:hover {
    text-decoration-line: none;
  }

  .babybear\:btn-sm:disabled {
    cursor: not-allowed;
  }

  .babybear\:m-0 {
    margin: 0px;
  }

  .babybear\:my-2 {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .babybear\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .babybear\:mt-0\.5 {
    margin-top: 4px;
  }

  .babybear\:mt-0 {
    margin-top: 0px;
  }

  .babybear\:ml-0 {
    margin-left: 0px;
  }

  .babybear\:mr-0 {
    margin-right: 0px;
  }

  .babybear\:mb-2 {
    margin-bottom: 16px;
  }

  .babybear\:mb-0\.5 {
    margin-bottom: 4px;
  }

  .babybear\:mb-0 {
    margin-bottom: 0px;
  }

  .babybear\:mb-1 {
    margin-bottom: 8px;
  }

  .babybear\:mt-\[6px\] {
    margin-top: 6px;
  }

  .babybear\:-mt-0\.5 {
    margin-top: -4px;
  }

  .babybear\:-mt-0 {
    margin-top: -0px;
  }

  .babybear\:ml-1 {
    margin-left: 8px;
  }

  .babybear\:btn-secondary-emphasis {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(0, 0, 0, 0);
    color: #0a66c2;
  }

  .babybear\:btn-secondary-emphasis:visited {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(0, 0, 0, 0);
    color: #0a66c2;
  }

  .babybear\:btn-secondary-emphasis:focus {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(0, 0, 0, 0);
    color: #0a66c2;
  }

  .babybear\:btn-secondary-emphasis:hover {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(112, 181, 249, 0.1);
    color: #004182;
  }

  .babybear\:btn-secondary-emphasis:visited:hover {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(112, 181, 249, 0.1);
    color: #004182;
  }

  .babybear\:btn-secondary-emphasis:active {
    box-shadow: 0 0 0 1px #004182;
    background-color: rgba(112, 181, 249, 0.2);
    color: #004182;
  }

  .babybear\:btn-secondary-emphasis:disabled {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0);
    background-color: rgba(0, 0, 0, 0.08);
    color: rgba(0, 0, 0, 0.3);
  }

  .babybear\:hidden {
    display: none;
  }

  .babybear\:h-\[360px\] {
    height: 360px;
  }

  .babybear\:h-6 {
    height: 48px;
  }

  .babybear\:h-\[72px\] {
    height: 72px;
  }

  .babybear\:h-4 {
    height: 32px;
  }

  .babybear\:h-\[26px\] {
    height: 26px;
  }

  .babybear\:h-\[59px\] {
    height: 59px;
  }

  .babybear\:h-auto {
    height: auto;
  }

  .babybear\:h-\[180px\] {
    height: 180px;
  }

  .babybear\:max-h-\[400px\] {
    max-height: 400px;
  }

  .babybear\:max-h-\[225px\] {
    max-height: 225px;
  }

  .babybear\:max-h-4 {
    max-height: 32px;
  }

  .babybear\:max-h-\[250px\] {
    max-height: 250px;
  }

  .babybear\:min-h-\[134px\] {
    min-height: 134px;
  }

  .babybear\:w-\[360px\] {
    width: 360px;
  }

  .babybear\:w-full {
    width: 100%;
  }

  .babybear\:w-11\/12 {
    width: 91.666667%;
  }

  .babybear\:w-\[199px\] {
    width: 199px;
  }

  .babybear\:w-6 {
    width: 48px;
  }

  .babybear\:w-\[26px\] {
    width: 26px;
  }

  .babybear\:w-\[105px\] {
    width: 105px;
  }

  .babybear\:w-\[calc\(100\%-68px\)\] {
    width: calc(100% - 68px);
  }

  .babybear\:min-w-full {
    min-width: 100%;
  }

  .babybear\:max-w-\[790px\] {
    max-width: 790px;
  }

  .babybear\:max-w-\[32px\] {
    max-width: 32px;
  }

  .babybear\:max-w-\[300px\] {
    max-width: 300px;
  }

  .babybear\:flex-none {
    flex: none;
  }

  .babybear\:flex-auto {
    flex: 1 1 auto;
  }

  .babybear\:basis-0 {
    flex-basis: 0px;
  }

  .babybear\:-translate-x-50\% {
    transform: translate(0, 0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY=
(1);
  }

  @media (max-width: 767px) {
    .babybear\:-translate-x-50\% {
      transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(=
1) scaleY(1);
    }

    @media (max-width: 767px) {
      .babybear\:-translate-x-50\% {
        transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scale=
X(1) scaleY(1);
      }

      @media (max-width: 767px) {
        .babybear\:-translate-x-50\% {
          transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) sca=
leX(1) scaleY(1);
        }

        @media (max-width: 767px) {
          .babybear\:-translate-x-50\% {
            transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) s=
caleX(1) scaleY(1);
          }

          @media (max-width: 767px) {
            .babybear\:-translate-x-50\% {
              transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0)=
 scaleX(1) scaleY(1);
            }

            @media (max-width: 767px) {
              .babybear\:-translate-x-50\% {
                transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(=
0) scaleX(1) scaleY(1);
              }

              @media (max-width: 767px) {
                .babybear\:-translate-x-50\% {
                  transform: translate(-50%, 0) rotate(90deg) skewX(0) skew=
Y(0) scaleX(1) scaleY(1);
                }

                @media (max-width: 767px) {
                  .babybear\:-translate-x-50\% {
                    transform: translate(-50%, 0) rotate(90deg) skewX(0) sk=
ewY(0) scaleX(1) scaleY(1);
                  }

                  @media (max-width: 767px) {
                    .babybear\:-translate-x-50\% {
                      transform: translate(-50%, 0) rotate(90deg) skewX(0) =
skewY(0) scaleX(1) scaleY(1);
                    }

                    @media (max-width: 767px) {
                      .babybear\:-translate-x-50\% {
                        transform: translate(-50%, 0) rotate(90deg) skewX(0=
) skewY(0) scaleX(1) scaleY(1);
                      }

                      @media (max-width: 767px) {
                        .babybear\:-translate-x-50\% {
                          transform: translate(-50%, 0) rotate(90deg) skewX=
(0) skewY(0) scaleX(1) scaleY(1);
                        }

                        @media (max-width: 767px) {
                          .babybear\:-translate-x-50\% {
                            transform: translate(-50%, 0) rotate(90deg) ske=
wX(0) skewY(0) scaleX(1) scaleY(1);
                          }

                          @media (max-width: 767px) {
                            .babybear\:-translate-x-50\% {
                              transform: translate(-50%, 0) rotate(90deg) s=
kewX(0) skewY(0) scaleX(1) scaleY(1);
                            }

                            @media (max-width: 767px) {
                              .babybear\:-translate-x-50\% {
                                transform: translate(-50%, 0) rotate(90deg)=
 skewX(0) skewY(0) scaleX(1) scaleY(1);
                              }

                              @media transform: translate(0, 0) rotate(0) s=
kewX(0) skewY(0) scaleX(1) scaleY(1);
     (max-width: 767px) {
                                .babybear\:rotate-90 {
                                  transform: translate(-50%, 0) rotate(90de=
g) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                }

                                @media (max-width: 767px) {
                                  .babybear\:rotate-90 {
                                    transform: translate(-50%, 0) rotate(90=
deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                  }

                                  @media (max-width: 767px) {
                                    .babybear\:rotate-90 {
                                      transform: translate(-50%, 0) rotate(=
90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                    }

                                    @media (max-width: 767px) {
                                      .babybear\:rotate-90 {
                                        transform: translate(-50%, 0) rotat=
e(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                      }

                                      @media (max-width: 767px) {
                                        .babybear\:rotate-90 {
                                          transform: translate(-50%, 0) rot=
ate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                        }

                                        @media (max-width: 767px) {
                                          .babybear\:rotate-90 {
                                            transform: translate(-50%, 0) r=
otate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                          }

                                          @media (max-width: 767px) {
                                            .babybear\:rotate-90 {
                                              transform: translate(-50%, 0)=
 rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                            }

                                            @media (max-width: 767px) {
                                              .babybear\:rotate-90 {
                                                transform: translate(-50%, =
0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                              }

                                              @media (max-width: 767px) {
                                                .babybear\:rotate-90 {
                                                  transform: translate(-50%=
, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                }

                                                @media (max-width: 767px) {
                                                  .babybear\:rotate-90 {
                                                    transform: translate(-5=
0%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                  }

                                                  @media (max-width: 767px)=
 {
                                                    .babybear\:rotate-90 {
                                                      transform: translate(=
-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                    }

                                                    @media (max-width: 767p=
x) {
                                                      .babybear\:rotate-90 =
{
                                                        transform: translat=
e(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                      }

                                                      @media (max-width: 76=
7px) {
                                                        .babybear\:rotate-9=
0 {
                                                          transform: transl=
ate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                        }

                                                        @media (max-width: =
767px) {
                                                          .babybear\:rotate=
-90 {
                                                            transform: tran=
slate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                          }

                                                          border-radius: 8p=
x;
        border-width: 1px;
        border-style: solid;
        border-color: rgba(0,
0,
0,
0.08);
        background-color: #ffffff;
    .babybear\:flex-row {
                                                            flex-direction:=
 row;
                                                          }

                                                          .babybear\:flex-c=
ol {
                                                            flex-direction:=
 column;
                                                          }

                                                          .babybear\:flex-w=
rap {
                                                            flex-wrap: wrap=
;
                                                          }

                                                          .babybear\:items-=
start {
                                                            align-items: fl=
ex-start;
                                                          }

                                                          .babybear\:items-=
center {
                                                            align-items: ce=
nter;
                                                          }

                                                          .babybear\:justif=
y-start {
                                                            justify-content=
: flex-start;
                                                          }

                                                          .babybear\:justif=
y-end {
                                                            justify-content=
: flex-end;
                                                          }

                                                          .babybear\:justif=
y-center {
                                                            justify-content=
: center;
                                                          }

                                                          .babybear\:justif=
y-between {
                                                            justify-content=
: space-between;
                                                          }

                                                          .babybear\:justif=
y-around {
                                                            justify-content=
: space-around;
                                                          }

                                                          .babybear\:self-s=
tart {
                                                            align-self: fle=
x-start;
                                                          }

                                                          .babybear\:self-c=
enter {
                                                            align-self: cen=
ter;
                                                          }

                                                          .babybear\:overfl=
ow-y-auto {
                                                            overflow-y: aut=
o;
                                                          }

                                                          .babybear\:rounde=
d-\[0px\] {
                                                            border-radius: =
0px;
                                                          }

                                                          .babybear\:border=
-r-0 {
                                                            border-right-wi=
dth: 0;
                                                          }

                                                          .babybear\:border=
-b-1 {
                                                            border-bottom-w=
idth: 1px;
                                                          }

                                                          .babybear\:border=
-solid {
                                                            border-style: s=
olid;
                                                          }

                                                          .babybear\:border=
-color-border-low-emphasis {
                                                            border-color: r=
gba(0, 0, 0, 0.3);
                                                          }

                                                          .babybear\:bg-col=
or-brand {
                                                            background-colo=
r: #0a66c2;
                                                          }

                                                          .babybear\:p-2 {
                                                            padding: 16px;
                                                          }

                                                          .babybear\:py-1 {
                                                            padding-top: 8p=
x;
                                                            padding-bottom:=
 8px;
                                                          }

                                                          .babybear\:px-2 {
                                                            padding-left: 1=
6px;
                                                            padding-right: =
16px;
                                                          }

                                                          .babybear\:px-0 {
                                                            padding-left: 0=
px;
                                                            padding-right: =
0px;
                                                          }

                                                          .babybear\:px-mob=
ile-container-padding {
                                                            padding-left: 1=
6px;
                                                            padding-right: =
16px;
                                                          }

                                                          .babybear\:py-3 {
                                                            padding-top: 24=
px;
                                                            padding-bottom:=
 24px;
                                                          }

                                                          .babybear\:pt-1 {
                                                            padding-top: 8p=
x;
                                                          }

                                                          .babybear\:pr-0\.=
5 {
                                                            padding-right: =
4px;
                                                          }

                                                          .babybear\:pr-0 {
                                                            padding-right: =
0px;
                                                          }

                                                          .babybear\:pl-0\.=
5 {
                                                            padding-left: 4=
px;
                                                          }

                                                          .babybear\:pl-0 {
                                                            padding-left: 0=
px;
                                                          }

                                                          .babybear\:pt-0\.=
5 {
                                                            padding-top: 4p=
x;
                                                          }

                                                          .babybear\:pb-0 {
                                                            padding-bottom:=
 0px;
                                                          }

                                                          .babybear\:pt-0 {
                                                            padding-top: 0p=
x;
                                                          }

                                                          .babybear\:pb-2 {
                                                            padding-bottom:=
 16px;
                                                          }

                                                          .babybear\:pb-\[1=
80px\] {
                                                            padding-bottom:=
 180px;
                                                          }

                                                          .babybear\:text-x=
s {
                                                            font-size: 12px=
;
                                                          }

                                                          .babybear\:text-s=
m {
                                                            font-size: 14px=
;
                                                          }

                                                          .babybear\:font-b=
old {
                                                            font-weight: 60=
0;
                                                          }

                                                          .babybear\:leadin=
g-\[150px\] {
                                                            line-height: 15=
0px;
                                                          }

                                                          .babybear\:last\:=
border-b-0:last-child {
                                                            border-bottom-w=
idth: 0;
                                                          }

                                                        }

                                                      }

                                                    }

                                                  }

                                                }

                                              }

                                            }

                                          }

                                        }

                                      }

                                    }

                                  }

                                }

                              }

                            }

                          }

                        }

                      }

                    }

                  }

                }

              }

            }

          }

        }

      }

    }

  }
}
@media (max-width: 991px) {
  .babymamabear\:invisible {
    visibility: hidden;
  }

  .babymamabear\:static {
    position: static;
  }

  .babymamabear\:mx-mobile-container-padding {
    margin-left: 16px;
    margin-right: 16px;
  }

  .babymamabear\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .babymamabear\:mr-3 {
    margin-right: 24px;
  }

  .babymamabear\:ml-\[-9999px\] {
    margin-left: -9999px;
  }

  .babymamabear\:mb-1\.5 {
    margin-bottom: 12px;
  }

  .babymamabear\:mb-1 {
    margin-bottom: 8px;
  }

  .babymamabear\:flex {
    display: flex;
  }

  .babymamabear\:hidden {
    display: none;
  }

  .babymamabear\:h-\[1px\] {
    height: 1px;
  }

  .babymamabear\:h-\[48px\] {
    height: 48px;
  }

  .babymamabear\:w-\[1px\] {
    width: 1px;
  }

  .babymamabear\:w-full {
    width: 100%;
  }

  .babymamabear\:w-\[100vw\] {
    width: 100vw;
  }

  .babymamabear\:min-w-\[50px\] {
    min-width: 50px;
  }

  .babymamabear\:basis-1\/2 {
    flex-basis: 50%;
  }

  .babymamabear\:flex-col {
    flex-direction: column;
  }

  .babymamabear\:flex-wrap {
    flex-wrap: wrap;
  }

  .babymamabear\:bg-color-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .babymamabear\:p-0 {
    padding: 0px;
  }

  .babymamabear\:px-mobile-container-padding {
    padding-left: 16px;
    padding-right: 16px;
  }

  .babymamabear\:py-1\.5 {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .babymamabear\:py-1 {
    padding-top: 8px;
    padding-bottom: 8px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mamabear\:absolute {
    position: absolute;
  }

  .mamabear\:right-4 {
    right: 32px;
  }

  .mamabear\:-top-0\.5 {
    top: -4px;
  }

  .mamabear\:-top-0 {
    top: -0px;
  }

  .mamabear\:top-\[58px\] {
    top: 58px;
  }

  .mamabear\:-top-1 {
    top: -8px;
  }

  .mamabear\:mr-3 {
    margin-right: 24px;
  }

  .mamabear\:hidden {
    display: none;
  }

  .mamabear\:\!h-4 {
    height: 32px !important;
  }

  .mamabear\:h-\[100\%\] {
    height: 100%;
  }

  .mamabear\:\!min-h-\[32px\] {
    min-height: 32px !important;
  }

  .mamabear\:w-\[744px\] {
    width: 744px;
  }

  .mamabear\:w-48 {
    width: 384px;
  }

  .mamabear\:w-full {
    width: 100%;
  }

  .mamabear\:w-\[calc\(100\%-97px\)\] {
    width: calc(100% - 97px);
  }

  .mamabear\:min-w-0 {
    min-width: 0px;
  }

  .mamabear\:max-w-\[790px\] {
    max-width: 790px;
  }

  .mamabear\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .mamabear\:container-lined {
    border-radius: 8px;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
  }

  .mamabear\:container-raised {
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
  }

  .mamabear\:flex-row {
    flex-direction: row;
  }

  .mamabear\:items-start {
    align-items: flex-start;
  }

  .mamabear\:items-center {
    align-items: center;
  }

  .mamabear\:self-center {
    align-self: center;
  }

  .mamabear\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .mamabear\:px-3 {
    padding-left: 24px;
    padding-right: 24px;
  }

  .mamabear\:px-mobile-container-padding {
    padding-left: 16px;
    padding-right: 16px;
  }

  .mamabear\:py-3 {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .mamabear\:px-2 {
    padding-left: 16px;
    padding-right: 16px;
  }

  .mamabear\:pt-desktop-content-top-margin {
    padding-top: 16px;
  }
}
@media (min-width: 768px) {
  .papamamabear\:pt-desktop-content-top-margin {
    padding-top: 16px;
  }

  .after\:papamamabear\:\!h-\[37px\]::after {
    content: undefined;
    height: 37px !important;
  }

  @media (min-width: 768px) {
    .after\:papamamabear\:\!h-\[37px\]::after {
      content: "";
    }

    content: "".after\:papamamabear\:\!h-\[37px\]::after:content-\[\"\"\]::=
after {
      content: "";
    }

    .after\:papamamabear\:\!h-\[37px\]::after:content-\[\'\/\'\]::after {
      content: "";
    }

    .after\:papamamabear\:up-down-divider::after {
      display: flex;
      height: 100%;
      width: 1px;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.3);
      text-align: center;
      content: undefined;
    }

    @media (min-width: 768px) {
      .after\:papamamabear\:up-down-divider::after {
        content: "";
      }

      content: "".after\:papamamabear\:up-down-divider::after:content-\[\"\=
"\]::after {
        content: "";
      }

      .after\:papamamabear\:up-down-divider::after:content-\[\'\/\'\]::afte=
r {
        content: "";
      }

    }

  }
}
@media (min-width: 992px) {
  .papabear\:absolute {
    position: absolute;
  }

  .papabear\:top-\[58px\] {
    top: 58px;
  }

  .papabear\:-top-1 {
    top: -8px;
  }

  .papabear\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .papabear\:\!mx-2 {
    margin-left: 16px !important;
    margin-right: 16px !important;
  }

  .papabear\:mt-\[-100px\] {
    margin-top: -100px;
  }

  .papabear\:mb-\[18px\] {
    margin-bottom: 18px;
  }

  .papabear\:mt-0 {
    margin-top: 0px;
  }

  .papabear\:mt-0\.5 {
    margin-top: 4px;
  }

  .papabear\:mr-2 {
    margin-right: 16px;
  }

  .papabear\:mr-3 {
    margin-right: 24px;
  }

  .papabear\:ml-column-gutter {
    margin-left: 24px;
  }

  .papabear\:inline-block {
    display: inline-block;
  }

  .papabear\:flex {
    display: flex;
  }

  .papabear\:hidden {
    display: none;
  }

  .papabear\:\!h-4 {
    height: 32px !important;
  }

  .papabear\:h-6 {
    height: 48px;
  }

  .papabear\:h-\[100\%\] {
    height: 100%;
  }

  .papabear\:\!min-h-\[32px\] {
    min-height: 32px !important;
  }

  .papabear\:min-h-\[96px\] {
    min-height: 96px;
  }

  .papabear\:w-\[400px\] {
    width: 400px;
  }

  .papabear\:w-content-max-w {
    width: 1128px;
  }

  .papabear\:w-core-rail-width {
    width: 784px;
  }

  .papabear\:w-right-rail-width {
    width: calc(1128px - 24px - 784px);
  }

  .papabear\:w-48 {
    width: 384px;
  }

  .papabear\:w-auto {
    width: auto;
  }

  .papabear\:w-\[1128px\] {
    width: 1128px;
  }

  .papabear\:w-\[calc\(100\%-97px\)\] {
    width: calc(100% - 97px);
  }

  .papabear\:min-w-0 {
    min-width: 0px;
  }

  .papabear\:max-w-\[550px\] {
    max-width: 550px;
  }

  .papabear\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .papabear\:container-lined {
    border-radius: 8px;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
  }

  .papabear\:container-raised {
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
  }

  .papabear\:flex-col {
    flex-direction: column;
  }

  .papabear\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .papabear\:items-start {
    align-items: flex-start;
  }

  .papabear\:justify-start {
    justify-content: flex-start;
  }

  .papabear\:justify-center {
    justify-content: center;
  }

  .papabear\:self-start {
    align-self: flex-start;
  }

  .papabear\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .papabear\:border-4 {
    border-width: 4px;
  }

  .papabear\:p-details-container-padding {
    padding: 24px;
  }

  .papabear\:p-0 {
    padding: 0px;
  }

  .papabear\:py-2 {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .papabear\:pt-desktop-content-top-margin {
    padding-top: 16px;
  }

  .papabear\:text-xl {
    font-size: 24px;
  }

  .papabear\:tab-vertical.tab-sm,
.papabear\:tab-vertical .tab-md,
.papabear\:tab-vertical .tab-lg {
    justify-content: flex-start;
  }

  .papabear\:tab-vertical.tab-selected:after {
    position: absolute;
    left: 0px;
    height: 100%;
    content: undefined;
    border-left: 4px solid rgba(0, 0, 0, 0.9);
  }

  @media (min-width: 992px) {
    .papabear\:tab-vertical.tab-selected:after {
      content: "";
    }

  }
}
</style></head> <body dir=3D"ltr" class=3D"font-sans bg-color-background-ca=
nvas w-full m-0 p-0 pt-1" style=3D"-webkit-text-size-adjust: 100%; -ms-text=
-size-adjust: 100%; margin: 0px; width: 100%; background-color: #f3f2f0; pa=
dding: 0px; padding-top: 8px; font-family: -apple-system, system-ui, BlinkM=
acSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Ox=
ygen, 'Oxygen Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe U=
I Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, =
Arial, sans-serif;"> <div class=3D"h-0 opacity-0 text-transparent invisible=
 overflow-hidden w-0 max-h-[0]" style=3D"visibility: hidden; height: 0px; m=
ax-height: 0; width: 0px; overflow: hidden; opacity: 0; mso-hide: all;" dat=
a-email-preheader=3D"true">Posted on 1/27/2025</div> <div class=3D"h-0 opac=
ity-0 text-transparent invisible overflow-hidden w-0 max-h-[0]" style=3D"vi=
sibility: hidden; height: 0px; max-height: 0; width: 0px; overflow: hidden;=
 opacity: 0; mso-hide: all;"> =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 </div> <table role=3D"present=
ation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" widt=
h=3D"512" align=3D"center" class=3D"mercado-container w-[512px] max-w-[512p=
x] mx-auto my-0 p-0 " style=3D"-webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin-left: =
auto; margin-right: auto; margin-top: 0px; margin-bottom: 0px; width: 512px=
; max-width: 512px; padding: 0px;"> <tbody> <tr> <td style=3D"-webkit-text-=
size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-t=
able-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"0=
" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"bg-color-back=
ground-container " style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-a=
djust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color=
: #ffffff;"> <tbody> <tr> <td class=3D"text-center p-3" style=3D"-webkit-te=
xt-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; ms=
o-table-rspace: 0pt; padding: 24px; text-align: center;"> <table role=3D"pr=
esentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0"=
 width=3D"100%" class=3D"min-w-full" style=3D"-webkit-text-size-adjust: 100=
%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt=
; min-width: 100%;"> <tbody> <tr> <td align=3D"left" valign=3D"middle" styl=
e=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-=
lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://www.linkedin.com/c=
omm/feed/?lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KW=
Ce%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1=
&amp;trk=3Deml-jobs_jymbii_digest-header-0-home_glimmer_dynamic_badging_hig=
h_dpi&amp;trkEmail=3Deml-jobs_jymbii_digest-header-0-home_glimmer_dynamic_b=
adging_high_dpi-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz=
-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0Mzkx=
YWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDA=
xNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" class=3D"w-[84px]" s=
tyle=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decora=
tion: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; wid=
th: 84px;"> <img class=3D"h-[37px] w-[101px]" alt=3D"LinkedIn" src=3D"https=
://www.linkedin.com/comm/dms/logo/v2?badgeTheme=3Dmercado&amp;lipi=3Durn%3A=
li%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;m=
idSig=3D1UWL5KEFK3mbE1&amp;midToken=3DAQHb-hvPD2eSGA&amp;trkEmail=3Deml-job=
s_jymbii_digest-null-0-comms%7Ebadging%7Edynamic%7Eglimmer%7Ev2-null-fojfqf=
%7Em722jtfz%7Ed9-null-null&amp;trk=3Deml-jobs_jymbii_digest-null-0-comms%7E=
badging%7Edynamic%7Eglimmer%7Ev2&amp;_sig=3D0ACns7WLy3mbE1" data-test-heade=
r-dynamic-badging-img-high-dpi style=3D"outline: none; text-decoration: non=
e; -ms-interpolation-mode: bicubic; height: 37px; width: 101px;" width=3D"1=
01" height=3D"37"> </a> </td> <td valign=3D"middle" align=3D"right" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D=
"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" data-=
test-header-profile style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-=
adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> =
<td align=3D"right" valign=3D"middle" class=3D"w-[32px]" style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt; width: 32px;" width=3D"32"> <a href=3D"https://eg.lin=
kedin.com/comm/in/pep0x?lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3=
B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D=
1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-header-0-profile_glimmer&am=
p;trkEmail=3Deml-jobs_jymbii_digest-header-0-profile_glimmer-null-fojfqf~m7=
22jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTI=
xMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMm=
YyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxL=
DE%3D" target=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display:=
 inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%;"> <img alt=3D"Abanoub Nashaat" src=3D"https://media.=
licdn.com/dms/image/v2/D4D03AQEDH6nP3hqAEg/profile-displayphoto-shrink_200_=
200/profile-displayphoto-shrink_200_200/0/1705302302995?e=3D2147483647&amp;=
v=3Dbeta&amp;t=3D9nmr2MC1Wr3ArYEPRa75uP-u0klA1Y_Yel_-Q3Bm934" class=3D"roun=
ded-[100%] w-[32px] h-[32px]" style=3D"outline: none; text-decoration: none=
; -ms-interpolation-mode: bicubic; height: 32px; width: 32px; border-radius=
: 100%;" width=3D"32" height=3D"32"> </a> </td> </tr> </tbody> </table> </t=
d> </tr> </tbody> </table> </td> </tr> <tr> <td class=3D"px-3 pb-3" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt; padding-left: 24px; padding-right: 24px;=
 padding-bottom: 24px;"> <div> <table role=3D"presentation" valign=3D"top" =
border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"le=
ading-regular" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjus=
t: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; line-height: 1.25;">=
 <tbody> <tr> <td class=3D"jymbii-header" style=3D"-webkit-text-size-adjust=
: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace=
: 0pt;"> <h1 style=3D"margin: 0; font-weight: 500;"> <a href=3D"https://www=
.linkedin.com/comm/jobs/collections/recommended?origin=3DJYMBII_EMAIL&amp;l=
gCta=3Deml-jymbii-bottom-see-all-jobs&amp;lgTemp=3Djobs_jymbii_digest&amp;l=
ipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg=
%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3D=
eml-jobs_jymbii_digest-null-0-null&amp;trkEmail=3Deml-jobs_jymbii_digest-nu=
ll-0-null-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&am=
p;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4Yj=
YxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZ=
GMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" class=3D"text-display-sm f=
ont-semibold font-sans text-system-gray-90" style=3D"cursor: pointer; displ=
ay: inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -m=
s-text-size-adjust: 100%; font-family: -apple-system, system-ui, BlinkMacSy=
stemFont, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen=
, 'Oxygen Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Em=
oji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Aria=
l, sans-serif; font-size: 24px; font-weight: 600; color: #282828;"> Top job=
 picks for you </a> </h1> </td> </tr> <tr> <td class=3D"pt-3" data-test-id=
=3D"job-card" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 24px;"> =
<table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" =
cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> =
<tbody> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adj=
ust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"p=
resentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0=
" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adj=
ust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" val=
ign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%"=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-1 w=
-6" valign=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-a=
djust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 48px; pad=
ding-right: 8px;" width=3D"48"> <a href=3D"https://www.linkedin.com/comm/jo=
bs/view/*********9/?trackingId=3DjllNb3ItTLS9p7lsOE3%2F0w%3D%3D&amp;refId=
=3DrNe%2Fm0FZQzGnHiBX7lPz0w%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_j=
ymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA=
&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-co=
mpany_logo&amp;trkEmail=3Deml-jobs_jymbii_digest-job_card-0-company_logo-nu=
ll-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=
=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4=
Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk=
4MTk2NjlmNiwxLDE%3D" target=3D"_blank" style=3D"color: #0a66c2; cursor: poi=
nter; display: inline-block; text-decoration: none; -webkit-text-size-adjus=
t: 100%; -ms-text-size-adjust: 100%;"> <img class=3D"inline-block relative =
bg-color-entity-ghost-background w-6 h-6 rounded-[2px]" src=3D"https://medi=
a.licdn.com/dms/image/v2/C4D0BAQFqQhjoDkns5A/company-logo_100_100/company-l=
ogo_100_100/0/1677*********/ginitalent_recruitment_staffing_logo?e=3D214748=
3647&amp;v=3Dbeta&amp;t=3DSnfYSSFq8n7AJPPtkND760NW68nfbs-HgAVbO1Cb2QY" alt=
=3D"Gini Talent" style=3D"outline: none; text-decoration: none; -ms-interpo=
lation-mode: bicubic; position: relative; display: inline-block; height: 48=
px; width: 48px; border-radius: 2px; background-color: #eae6df;" width=3D"4=
8" height=3D"48"> </a> </td> <td valign=3D"top" style=3D"-webkit-text-size-=
adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-=
rspace: 0pt;"> <a href=3D"https://www.linkedin.com/comm/jobs/view/*********=
9/?trackingId=3DjllNb3ItTLS9p7lsOE3%2F0w%3D%3D&amp;refId=3DrNe%2Fm0FZQzGnHi=
BX7lPz0w%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfM=
tvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5=
KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-job_posting&amp;trkEm=
ail=3Deml-jobs_jymbii_digest-job_card-0-job_posting-null-fojfqf~m722jtfz~d9=
-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2Jj=
M2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI=
1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" ta=
rget=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display: inline-b=
lock; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-=
adjust: 100%;"> <table role=3D"presentation" valign=3D"top" border=3D"0" ce=
llspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size=
-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table=
-rspace: 0pt;"> <tbody> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size-=
adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-=
rspace: 0pt; padding-bottom: 0px;"> <a href=3D"https://www.linkedin.com/com=
m/jobs/view/*********9/?trackingId=3DjllNb3ItTLS9p7lsOE3%2F0w%3D%3D&amp;ref=
Id=3DrNe%2Fm0FZQzGnHiBX7lPz0w%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs=
_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eS=
GA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-=
jobcard_body&amp;trkEmail=3Deml-jobs_jymbii_digest-job_card-0-jobcard_body-=
null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=
=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4=
Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk=
4MTk2NjlmNiwxLDE%3D" target=3D"_blank" class=3D"font-bold text-md leading-r=
egular text-system-blue-50" style=3D"color: #0a66c2; cursor: pointer; displ=
ay: inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -m=
s-text-size-adjust: 100%; font-size: 16px; font-weight: 600; line-height: 1=
.25;"> Data Entry Specialist </a> </td> </tr> <tr> <td class=3D"pb-0" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;"> <p class=3D"text-=
system-gray-100 text-xs leading-regular mt-0.5 line-clamp-1 text-ellipsis" =
style=3D"margin: 0; font-weight: 400; margin-top: 4px; text-overflow: ellip=
sis; font-size: 12px; line-height: 1.25; color: #1f1f1f; overflow: hidden; =
display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1;"=
> Gini Talent &middot; Egypt (Remote) </p> </td> </tr> <tr> <td style=3D"-w=
ebkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace:=
 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" =
border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-w=
ebkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace:=
 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-=
adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-=
rspace: 0pt; font-size: 0;" align=3D"left"> <!--[if (gte mso 9)|(IE)]><tabl=
e cellpadding=3D"0" cellspacing=3D"0" border=3D"0" role=3D"presentation" st=
yle=3D"width: 100%;"><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> <td style=
=3D"padding-top: 4px;"><![endif]--> <table class=3D"inline-block" style=3D"=
-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspac=
e: 0pt; mso-table-rspace: 0pt; display: inline-block;"> <tr> <td class=3D"j=
ob-card-flavor__container_redesign_shaded_background" style=3D"-webkit-text=
-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-=
table-rspace: 0pt; margin-top: 4px; margin-right: 4px; display: inline-bloc=
k; border-radius: 4px; background-color: #f5f7f9; padding: 4px;"> <table ro=
le=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpaddi=
ng=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-s=
ize-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <=
tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width:=
 8px; padding-right: 4px;" width=3D"8"> <img src=3D"https://static.licdn.co=
m/aero-v1/sc/h/3le24jev2hig88cuokev26ksm" alt class=3D"w-2 h-2 block" style=
=3D"outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; =
display: block; height: 16px; width: 16px;" width=3D"16" height=3D"16"> </t=
d> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;=
 mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <p class=3D"job-card-flavo=
r__detail" style=3D"margin: 0; font-weight: 400; font-family: -apple-system=
, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fir=
a Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid Sans', 'Apple Col=
or Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida G=
rande', Helvetica, Arial, sans-serif; font-size: 12px; line-height: 1.25; c=
olor: #666666;"> Actively recruiting </p> </td> </tr> </tbody> </table> </t=
d> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> </tr><![endif]--> <!--[if=
 (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=3D"0" border=3D"0" =
role=3D"presentation" style=3D"width: 100%;"><![endif]--> </td> </tr> </tbo=
dy> </table> </td> </tr> </tbody> </table> </a> </td> </tr> </tbody> </tabl=
e> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> =
<tr> <td class=3D"pt-3" data-test-id=3D"job-card" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; padding-top: 24px;"> <table role=3D"presentation" valign=3D"=
top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text=
-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-=
table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"=
0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text=
-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-=
table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-adjust: 10=
0%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0p=
t;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=
=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: =
100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: =
0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=3D"top" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt; width: 48px; padding-right: 8px;" width=3D"48"> <a h=
ref=3D"https://www.linkedin.com/comm/jobs/view/4145750779/?trackingId=3DjLl=
3v0elSMqHW8ntnDYmuQ%3D%3D&amp;refId=3DOwUyj0JPTmuwNGBD2KJhew%3D%3D&amp;lipi=
=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D=
%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml=
-jobs_jymbii_digest-job_card-0-company_logo&amp;trkEmail=3Deml-jobs_jymbii_=
digest-job_card-0-company_logo-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3D=
fojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0Y=
jM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZh=
YjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" style=
=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration=
: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <img =
class=3D"inline-block relative bg-color-entity-ghost-background w-6 h-6 rou=
nded-[2px]" src=3D"https://media.licdn.com/dms/image/v2/D4D0BAQFwqcpc5dU5IA=
/company-logo_100_100/company-logo_100_100/0/1719851300671/turingcom_logo?e=
=3D2147483647&amp;v=3Dbeta&amp;t=3Dn_hgiKxCK1OLU4vTvome-M7gDuWBL0nFtitfNvsl=
a_A" alt=3D"Turing" style=3D"outline: none; text-decoration: none; -ms-inte=
rpolation-mode: bicubic; position: relative; display: inline-block; height:=
 48px; width: 48px; border-radius: 2px; background-color: #eae6df;" width=
=3D"48" height=3D"48"> </a> </td> <td valign=3D"top" style=3D"-webkit-text-=
size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-t=
able-rspace: 0pt;"> <a href=3D"https://www.linkedin.com/comm/jobs/view/4145=
750779/?trackingId=3DjLl3v0elSMqHW8ntnDYmuQ%3D%3D&amp;refId=3DOwUyj0JPTmuwN=
GBD2KJhew%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8Mf=
MtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL=
5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-job_posting&amp;trkE=
mail=3Deml-jobs_jymbii_digest-job_card-0-job_posting-null-fojfqf~m722jtfz~d=
9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2J=
jM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZm=
I1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" t=
arget=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display: inline-=
block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size=
-adjust: 100%;"> <table role=3D"presentation" valign=3D"top" border=3D"0" c=
ellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt;"> <tbody> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size=
-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table=
-rspace: 0pt; padding-bottom: 0px;"> <a href=3D"https://www.linkedin.com/co=
mm/jobs/view/4145750779/?trackingId=3DjLl3v0elSMqHW8ntnDYmuQ%3D%3D&amp;refI=
d=3DOwUyj0JPTmuwNGBD2KJhew%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jy=
mbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&=
amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-job=
card_body&amp;trkEmail=3Deml-jobs_jymbii_digest-job_card-0-jobcard_body-nul=
l-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3D=
MWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk=
0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MT=
k2NjlmNiwxLDE%3D" target=3D"_blank" class=3D"font-bold text-md leading-regu=
lar text-system-blue-50" style=3D"color: #0a66c2; cursor: pointer; display:=
 inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; font-size: 16px; font-weight: 600; line-height: 1.25=
;"> Remote Content Analyst(Arabic) - 20930 </a> </td> </tr> <tr> <td class=
=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 10=
0%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;"> <p=
 class=3D"text-system-gray-100 text-xs leading-regular mt-0.5 line-clamp-1 =
text-ellipsis" style=3D"margin: 0; font-weight: 400; margin-top: 4px; text-=
overflow: ellipsis; font-size: 12px; line-height: 1.25; color: #1f1f1f; ove=
rflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-=
line-clamp: 1;"> Turing &middot; Egypt (Remote) </p> </td> </tr> <tr> <td s=
tyle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tab=
le-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" valig=
n=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" s=
tyle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tab=
le-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt; font-size: 0;" align=3D"left"> <!--[if (gte mso 9)|(=
IE)]><table cellpadding=3D"0" cellspacing=3D"0" border=3D"0" role=3D"presen=
tation" style=3D"width: 100%;"><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> =
<td style=3D"padding-top: 4px;"><![endif]--> <table class=3D"inline-block" =
style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-ta=
ble-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block;"> <tr> <td c=
lass=3D"job-card-flavor__container_redesign_shaded_background" style=3D"-we=
bkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: =
0pt; mso-table-rspace: 0pt; margin-top: 4px; margin-right: 4px; display: in=
line-block; border-radius: 4px; background-color: #f5f7f9; padding: 4px;"> =
<table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" =
cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> =
<tbody> <tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-size-adjust: 10=
0%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0p=
t; width: 8px; padding-right: 4px;" width=3D"8"> <img src=3D"https://static=
.licdn.com/aero-v1/sc/h/3le24jev2hig88cuokev26ksm" alt class=3D"w-2 h-2 blo=
ck" style=3D"outline: none; text-decoration: none; -ms-interpolation-mode: =
bicubic; display: block; height: 16px; width: 16px;" width=3D"16" height=3D=
"16"> </td> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adju=
st: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <p class=3D"job-c=
ard-flavor__detail" style=3D"margin: 0; font-weight: 400; font-family: -app=
le-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Ne=
ue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid Sans', '=
Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', =
'Lucida Grande', Helvetica, Arial, sans-serif; font-size: 12px; line-height=
: 1.25; color: #666666;"> Actively recruiting </p> </td> </tr> </tbody> </t=
able> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> </tr><![endif]--=
> <!--[if (gte mso 9)|(IE)]><tr> <td style=3D"padding-top: 4px;"><![endif]-=
-> <table class=3D"inline-block" style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; di=
splay: inline-block;"> <tr> <td class=3D"job-card-flavor__container_redesig=
n_shaded_background" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size=
-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin-top: 4p=
x; margin-right: 4px; display: inline-block; border-radius: 4px; background=
-color: #f5f7f9; padding: 4px;"> <table role=3D"presentation" valign=3D"top=
" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"=
-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspac=
e: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-0.5 w-1" styl=
e=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-=
lspace: 0pt; mso-table-rspace: 0pt; width: 8px; padding-right: 4px;" width=
=3D"8"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/1sk0fbe9amaiat5el=
vybbtxsm" alt=3D"Easy Apply" class=3D"w-2 h-2 rounded-sm block" style=3D"ou=
tline: none; text-decoration: none; -ms-interpolation-mode: bicubic; displa=
y: block; height: 16px; width: 16px; border-radius: 4px;" width=3D"16" heig=
ht=3D"16"> </td> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size=
-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <p class=3D"=
job-card-flavor__detail" style=3D"margin: 0; font-weight: 400; font-family:=
 -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helveti=
ca Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid San=
s', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'Segoe UI Symb=
ol', 'Lucida Grande', Helvetica, Arial, sans-serif; font-size: 12px; line-h=
eight: 1.25; color: #666666;"> Easy Apply </p> </td> </tr> </tbody> </table=
> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> </tr><![endif]--> <!=
--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=3D"0" border=
=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endif]--> </td> </tr=
> </tbody> </table> </td> </tr> </tbody> </table> </a> </td> </tr> </tbody>=
 </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td>=
 </tr> <tr> <td class=3D"pt-3" data-test-id=3D"job-card" style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt; padding-top: 24px;"> <table role=3D"presentation" val=
ign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%"=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webki=
t-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt=
; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" bord=
er=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webki=
t-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt=
; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-adju=
st: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspa=
ce: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspa=
cing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adju=
st: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspa=
ce: 0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=3D"top" style=3D"-web=
kit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0=
pt; mso-table-rspace: 0pt; width: 48px; padding-right: 8px;" width=3D"48"> =
<a href=3D"https://www.linkedin.com/comm/jobs/view/4138308626/?trackingId=
=3DtidhVgfPT4CbB3go1EWA7w%3D%3D&amp;refId=3DnRLJ8MrxRqOWdEzamuiUnA%3D%3D&am=
p;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1E=
xBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=
=3Deml-jobs_jymbii_digest-job_card-0-company_logo&amp;trkEmail=3Deml-jobs_j=
ymbii_digest-job_card-0-company_logo-null-fojfqf~m722jtfz~d9-null-null&amp;=
eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDU=
xN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NT=
gyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank"=
 style=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-deco=
ration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">=
 <img class=3D"inline-block relative bg-color-entity-ghost-background w-6 h=
-6 rounded-[2px]" src=3D"https://media.licdn.com/dms/image/v2/D4E0BAQFTiUh1=
DUJ3fA/company-logo_100_100/company-logo_100_100/0/1719793387652/siemenssof=
tware_logo?e=3D2147483647&amp;v=3Dbeta&amp;t=3Do3SrSdxMeP1br7rpKknaX62uVBko=
mXcqvR0mjnb0kBc" alt=3D"Siemens Digital Industries Software" style=3D"outli=
ne: none; text-decoration: none; -ms-interpolation-mode: bicubic; position:=
 relative; display: inline-block; height: 48px; width: 48px; border-radius:=
 2px; background-color: #eae6df;" width=3D"48" height=3D"48"> </a> </td> <t=
d valign=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adj=
ust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https=
://www.linkedin.com/comm/jobs/view/4138308626/?trackingId=3DtidhVgfPT4CbB3g=
o1EWA7w%3D%3D&amp;refId=3DnRLJ8MrxRqOWdEzamuiUnA%3D%3D&amp;lipi=3Durn%3Ali%=
3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midT=
oken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii=
_digest-job_card-0-job_posting&amp;trkEmail=3Deml-jobs_jymbii_digest-job_ca=
rd-0-job_posting-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtf=
z-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0Mzk=
xYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyND=
AxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" style=3D"color: #0a=
66c2; cursor: pointer; display: inline-block; text-decoration: none; -webki=
t-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <table role=3D"pres=
entation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" w=
idth=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td cl=
ass=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust:=
 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;">=
 <a href=3D"https://www.linkedin.com/comm/jobs/view/4138308626/?trackingId=
=3DtidhVgfPT4CbB3go1EWA7w%3D%3D&amp;refId=3DnRLJ8MrxRqOWdEzamuiUnA%3D%3D&am=
p;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1E=
xBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=
=3Deml-jobs_jymbii_digest-job_card-0-jobcard_body&amp;trkEmail=3Deml-jobs_j=
ymbii_digest-job_card-0-jobcard_body-null-fojfqf~m722jtfz~d9-null-null&amp;=
eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDU=
xN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NT=
gyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank"=
 class=3D"font-bold text-md leading-regular text-system-blue-50" style=3D"c=
olor: #0a66c2; cursor: pointer; display: inline-block; text-decoration: non=
e; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; font-size: 1=
6px; font-weight: 600; line-height: 1.25;"> Software Frontend Development E=
ngineer </a> </td> </tr> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size=
-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table=
-rspace: 0pt; padding-bottom: 0px;"> <p class=3D"text-system-gray-100 text-=
xs leading-regular mt-0.5 line-clamp-1 text-ellipsis" style=3D"margin: 0; f=
ont-weight: 400; margin-top: 4px; text-overflow: ellipsis; font-size: 12px;=
 line-height: 1.25; color: #1f1f1f; overflow: hidden; display: -webkit-box;=
 -webkit-box-orient: vertical; -webkit-line-clamp: 1;"> Siemens Digital Ind=
ustries Software &middot; Qesm 2nd New Cairo </p> </td> </tr> <tr> <td styl=
e=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-=
lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=
=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" st=
yle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tabl=
e-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt; font-size: 0;" align=3D"left"> <!--[if (gte mso 9)|(I=
E)]><table cellpadding=3D"0" cellspacing=3D"0" border=3D"0" role=3D"present=
ation" style=3D"width: 100%;"><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> <=
td style=3D"padding-top: 4px;"><![endif]--> <table class=3D"inline-block" s=
tyle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tab=
le-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block;"> <tr> <td cl=
ass=3D"job-card-flavor__container_redesign_shaded_background" style=3D"-web=
kit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0=
pt; mso-table-rspace: 0pt; margin-top: 4px; margin-right: 4px; display: inl=
ine-block; border-radius: 4px; background-color: #f5f7f9; padding: 4px;"> <=
table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" c=
ellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -m=
s-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <=
tbody> <tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-size-adjust: 100=
%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt=
; width: 8px; padding-right: 4px;" width=3D"8"> <img src=3D"https://static.=
licdn.com/aero-v1/sc/h/3le24jev2hig88cuokev26ksm" alt class=3D"w-2 h-2 bloc=
k" style=3D"outline: none; text-decoration: none; -ms-interpolation-mode: b=
icubic; display: block; height: 16px; width: 16px;" width=3D"16" height=3D"=
16"> </td> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjus=
t: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <p class=3D"job-ca=
rd-flavor__detail" style=3D"margin: 0; font-weight: 400; font-family: -appl=
e-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neu=
e', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid Sans', 'A=
pple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', '=
Lucida Grande', Helvetica, Arial, sans-serif; font-size: 12px; line-height:=
 1.25; color: #666666;"> Actively recruiting </p> </td> </tr> </tbody> </ta=
ble> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> </tr><![endif]-->=
 <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=3D"0" borde=
r=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endif]--> </td> </t=
r> </tbody> </table> </td> </tr> </tbody> </table> </a> </td> </tr> </tbody=
> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td=
> </tr> <tr> <td class=3D"pt-3" data-test-id=3D"job-card" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt; padding-top: 24px;"> <table role=3D"presentation" va=
lign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%=
" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-=
table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0p=
t; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" bor=
der=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0p=
t; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-adj=
ust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rsp=
ace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellsp=
acing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adj=
ust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rsp=
ace: 0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=3D"top" style=3D"-we=
bkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: =
0pt; mso-table-rspace: 0pt; width: 48px; padding-right: 8px;" width=3D"48">=
 <a href=3D"https://www.linkedin.com/comm/jobs/view/4047619553/?trackingId=
=3D7ERyVzbhSvaLZmYINEysgw%3D%3D&amp;refId=3DqOHUYOvNQAWOdsPmr58cAg%3D%3D&am=
p;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1E=
xBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=
=3Deml-jobs_jymbii_digest-job_card-0-company_logo&amp;trkEmail=3Deml-jobs_j=
ymbii_digest-job_card-0-company_logo-null-fojfqf~m722jtfz~d9-null-null&amp;=
eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDU=
xN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NT=
gyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank"=
 style=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-deco=
ration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">=
 <img class=3D"inline-block relative bg-color-entity-ghost-background w-6 h=
-6 rounded-[2px]" src=3D"https://media.licdn.com/dms/image/v2/D560BAQElwT1k=
pc6sWw/company-logo_100_100/company-logo_100_100/0/1714395474106/usebraintr=
ust_logo?e=3D2147483647&amp;v=3Dbeta&amp;t=3DAPHLsDAnQ6u0-GV3Egv-SesyXtHIiN=
2TtFBSFW--Quo" alt=3D"Braintrust" style=3D"outline: none; text-decoration: =
none; -ms-interpolation-mode: bicubic; position: relative; display: inline-=
block; height: 48px; width: 48px; border-radius: 2px; background-color: #ea=
e6df;" width=3D"48" height=3D"48"> </a> </td> <td valign=3D"top" style=3D"-=
webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace=
: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://www.linkedin.com/comm/jo=
bs/view/4047619553/?trackingId=3D7ERyVzbhSvaLZmYINEysgw%3D%3D&amp;refId=3Dq=
OHUYOvNQAWOdsPmr58cAg%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_=
digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;m=
idSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-job_post=
ing&amp;trkEmail=3Deml-jobs_jymbii_digest-job_card-0-job_posting-null-fojfq=
f~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE=
2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNT=
RmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmN=
iwxLDE%3D" target=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; disp=
lay: inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%;"> <table role=3D"presentation" valign=3D"top" bo=
rder=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-web=
kit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0=
pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pb-0" style=3D"-webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0p=
t; mso-table-rspace: 0pt; padding-bottom: 0px;"> <a href=3D"https://www.lin=
kedin.com/comm/jobs/view/4047619553/?trackingId=3D7ERyVzbhSvaLZmYINEysgw%3D=
%3D&amp;refId=3DqOHUYOvNQAWOdsPmr58cAg%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Ae=
mail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQH=
b-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-jo=
b_card-0-jobcard_body&amp;trkEmail=3Deml-jobs_jymbii_digest-job_card-0-jobc=
ard_body-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp=
;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjY=
xNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZG=
MyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" class=3D"font-bold text-md =
leading-regular text-system-blue-50" style=3D"color: #0a66c2; cursor: point=
er; display: inline-block; text-decoration: none; -webkit-text-size-adjust:=
 100%; -ms-text-size-adjust: 100%; font-size: 16px; font-weight: 600; line-=
height: 1.25;"> Coders - AI Training [Remote] </a> </td> </tr> <tr> <td cla=
ss=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: =
100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;"> =
<p class=3D"text-system-gray-100 text-xs leading-regular mt-0.5 line-clamp-=
1 text-ellipsis" style=3D"margin: 0; font-weight: 400; margin-top: 4px; tex=
t-overflow: ellipsis; font-size: 12px; line-height: 1.25; color: #1f1f1f; o=
verflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webki=
t-line-clamp: 1;"> Braintrust &middot; Cairo, Egypt (Remote) </p> </td> </t=
r> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: =
100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presen=
tation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" wid=
th=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: =
100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td styl=
e=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-=
lspace: 0pt; mso-table-rspace: 0pt; font-size: 0;" align=3D"left"> <!--[if =
(gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=3D"0" border=3D"0" r=
ole=3D"presentation" style=3D"width: 100%;"><![endif]--> <!--[if (gte mso 9=
)|(IE)]><tr> <td style=3D"padding-top: 4px;"><![endif]--> <table class=3D"i=
nline-block" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust:=
 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block;=
"> <tr> <td class=3D"job-card-flavor__container_redesign_shaded_background"=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt; margin-top: 4px; margin-right: 4px=
; display: inline-block; border-radius: 4px; background-color: #f5f7f9; pad=
ding: 4px;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cells=
pacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-ad=
just: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rs=
pace: 0pt;"> <tbody> <tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-si=
ze-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tab=
le-rspace: 0pt; width: 8px; padding-right: 4px;" width=3D"8"> <img src=3D"h=
ttps://static.licdn.com/aero-v1/sc/h/3le24jev2hig88cuokev26ksm" alt class=
=3D"w-2 h-2 block" style=3D"outline: none; text-decoration: none; -ms-inter=
polation-mode: bicubic; display: block; height: 16px; width: 16px;" width=
=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> =
<p class=3D"job-card-flavor__detail" style=3D"margin: 0; font-weight: 400; =
font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Robo=
to, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell=
, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'S=
egoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif; font-size: =
12px; line-height: 1.25; color: #666666;"> Actively recruiting </p> </td> <=
/tr> </tbody> </table> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td>=
 </tr><![endif]--> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cells=
pacing=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100%;"><![e=
ndif]--> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </a> <=
/td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tb=
ody> </table> </td> </tr> <tr> <td class=3D"pt-3" data-test-id=3D"job-card"=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 24px;"> <table role=
=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=
=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr=
> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; =
mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation=
" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"=
100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; =
mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-=
webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace=
: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top"=
 border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-=
webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace=
: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=
=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100=
%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 48px; padding-right=
: 8px;" width=3D"48"> <a href=3D"https://www.linkedin.com/comm/jobs/view/41=
38070368/?trackingId=3D8DpS9j38S76tJTq56kmh5A%3D%3D&amp;refId=3DdjXaN5KSQnG=
fXcJDAfJcgw%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8=
MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1U=
WL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-company_logo&amp;t=
rkEmail=3Deml-jobs_jymbii_digest-job_card-0-company_logo-null-fojfqf~m722jt=
fz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJ=
lY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZD=
dkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3=
D" target=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display: inl=
ine-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-=
size-adjust: 100%;"> <img class=3D"inline-block relative bg-color-entity-gh=
ost-background w-6 h-6 rounded-[2px]" src=3D"https://media.licdn.com/dms/im=
age/v2/C4D0BAQFqQhjoDkns5A/company-logo_100_100/company-logo_100_100/0/1677=
*********/ginitalent_recruitment_staffing_logo?e=3D2147483647&amp;v=3Dbeta&=
amp;t=3DSnfYSSFq8n7AJPPtkND760NW68nfbs-HgAVbO1Cb2QY" alt=3D"Gini Talent" st=
yle=3D"outline: none; text-decoration: none; -ms-interpolation-mode: bicubi=
c; position: relative; display: inline-block; height: 48px; width: 48px; bo=
rder-radius: 2px; background-color: #eae6df;" width=3D"48" height=3D"48"> <=
/a> </td> <td valign=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <a h=
ref=3D"https://www.linkedin.com/comm/jobs/view/4138070368/?trackingId=3D8Dp=
S9j38S76tJTq56kmh5A%3D%3D&amp;refId=3DdjXaN5KSQnGfXcJDAfJcgw%3D%3D&amp;lipi=
=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D=
%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml=
-jobs_jymbii_digest-job_card-0-job_posting&amp;trkEmail=3Deml-jobs_jymbii_d=
igest-job_card-0-job_posting-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfo=
jfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM=
4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYj=
VlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" style=
=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration=
: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <tabl=
e role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellp=
adding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-te=
xt-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbod=
y> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -ms-tex=
t-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-=
bottom: 0px;"> <a href=3D"https://www.linkedin.com/comm/jobs/view/413807036=
8/?trackingId=3D8DpS9j38S76tJTq56kmh5A%3D%3D&amp;refId=3DdjXaN5KSQnGfXcJDAf=
Jcgw%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7=
Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK=
3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-jobcard_body&amp;trkEmail=
=3Deml-jobs_jymbii_digest-job_card-0-jobcard_body-null-fojfqf~m722jtfz~d9-n=
ull-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2=
JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1N=
zNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" targ=
et=3D"_blank" class=3D"font-bold text-md leading-regular text-system-blue-5=
0" style=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-de=
coration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;=
 font-size: 16px; font-weight: 600; line-height: 1.25;"> Frontend Developer=
 </a> </td> </tr> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size-adjust=
: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace=
: 0pt; padding-bottom: 0px;"> <p class=3D"text-system-gray-100 text-xs lead=
ing-regular mt-0.5 line-clamp-1 text-ellipsis" style=3D"margin: 0; font-wei=
ght: 400; margin-top: 4px; text-overflow: ellipsis; font-size: 12px; line-h=
eight: 1.25; color: #1f1f1f; overflow: hidden; display: -webkit-box; -webki=
t-box-orient: vertical; -webkit-line-clamp: 1;"> Gini Talent &middot; Egypt=
 (Remote) </p> </td> </tr> <tr> <td style=3D"-webkit-text-size-adjust: 100%=
; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;=
"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"=
0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%=
; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;=
"> <tbody> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-=
adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0;" =
align=3D"left"> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspac=
ing=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endi=
f]--> <!--[if (gte mso 9)|(IE)]><tr> <td style=3D"padding-top: 4px;"><![end=
if]--> <table class=3D"inline-block" style=3D"-webkit-text-size-adjust: 100=
%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt=
; display: inline-block;"> <tr> <td class=3D"job-card-flavor__container_red=
esign_shaded_background" style=3D"-webkit-text-size-adjust: 100%; -ms-text-=
size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin-top=
: 4px; margin-right: 4px; display: inline-block; border-radius: 4px; backgr=
ound-color: #f5f7f9; padding: 4px;"> <table role=3D"presentation" valign=3D=
"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-0.5 w-1" =
style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-ta=
ble-lspace: 0pt; mso-table-rspace: 0pt; width: 8px; padding-right: 4px;" wi=
dth=3D"8"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/3le24jev2hig88=
cuokev26ksm" alt class=3D"w-2 h-2 block" style=3D"outline: none; text-decor=
ation: none; -ms-interpolation-mode: bicubic; display: block; height: 16px;=
 width: 16px;" width=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text-=
size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-t=
able-rspace: 0pt;"> <p class=3D"job-card-flavor__detail" style=3D"margin: 0=
; font-weight: 400; font-family: -apple-system, system-ui, BlinkMacSystemFo=
nt, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxy=
gen Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', =
'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, san=
s-serif; font-size: 12px; line-height: 1.25; color: #666666;"> Actively rec=
ruiting </p> </td> </tr> </tbody> </table> </td> </tr> </table> <!--[if (gt=
e mso 9)|(IE)]></td> </tr><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> <td s=
tyle=3D"padding-top: 4px;"><![endif]--> <table class=3D"inline-block" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt; display: inline-block;"> <tr> <td class=
=3D"job-card-flavor__container_redesign_shaded_background" style=3D"-webkit=
-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt;=
 mso-table-rspace: 0pt; margin-top: 4px; margin-right: 4px; display: inline=
-block; border-radius: 4px; background-color: #f5f7f9; padding: 4px;"> <tab=
le role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cell=
padding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbo=
dy> <tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-size-adjust: 100%; =
-ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; w=
idth: 8px; padding-right: 4px;" width=3D"8"> <img src=3D"https://static.lic=
dn.com/aero-v1/sc/h/1sk0fbe9amaiat5elvybbtxsm" alt=3D"Easy Apply" class=3D"=
w-2 h-2 rounded-sm block" style=3D"outline: none; text-decoration: none; -m=
s-interpolation-mode: bicubic; display: block; height: 16px; width: 16px; b=
order-radius: 4px;" width=3D"16" height=3D"16"> </td> <td style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt;"> <p class=3D"job-card-flavor__detail" style=3D"marg=
in: 0; font-weight: 400; font-family: -apple-system, system-ui, BlinkMacSys=
temFont, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen,=
 'Oxygen Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emo=
ji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial=
, sans-serif; font-size: 12px; line-height: 1.25; color: #666666;"> Easy Ap=
ply </p> </td> </tr> </tbody> </table> </td> </tr> </table> <!--[if (gte ms=
o 9)|(IE)]></td> </tr><![endif]--> <!--[if (gte mso 9)|(IE)]><table cellpad=
ding=3D"0" cellspacing=3D"0" border=3D"0" role=3D"presentation" style=3D"wi=
dth: 100%;"><![endif]--> </td> </tr> </tbody> </table> </td> </tr> </tbody>=
 </table> </a> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> =
</td> </tr> </tbody> </table> </td> </tr> <tr> <td class=3D"pt-3" data-test=
-id=3D"job-card" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adj=
ust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 24px;=
"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"=
0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%=
; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;=
"> <tbody> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-=
adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=
=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=
=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr=
> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; =
mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation=
" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"=
100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; =
mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"p=
r-1 w-6" valign=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-s=
ize-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 48px=
; padding-right: 8px;" width=3D"48"> <a href=3D"https://www.linkedin.com/co=
mm/jobs/view/4069865121/?trackingId=3D0XqiOmGEQtSvZAbtvuDr6Q%3D%3D&amp;refI=
d=3Dosj173V7ROi2YuOAJxtWlg%3D%3D&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jy=
mbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&=
amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-job_card-0-com=
pany_logo&amp;trkEmail=3Deml-jobs_jymbii_digest-job_card-0-company_logo-nul=
l-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3D=
MWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk=
0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MT=
k2NjlmNiwxLDE%3D" target=3D"_blank" style=3D"color: #0a66c2; cursor: pointe=
r; display: inline-block; text-decoration: none; -webkit-text-size-adjust: =
100%; -ms-text-size-adjust: 100%;"> <img class=3D"inline-block relative bg-=
color-entity-ghost-background w-6 h-6 rounded-[2px]" src=3D"https://media.l=
icdn.com/dms/image/v2/D4D0BAQFH_S6F2N_mHA/company-logo_100_100/company-logo=
_100_100/0/1727961507909/blink22_logo?e=3D2147483647&amp;v=3Dbeta&amp;t=3DW=
oIKMenqdfbbOrUdOZiS9z2ZdyLaTB5RHteLXIy6zhc" alt=3D"Blink22" style=3D"outlin=
e: none; text-decoration: none; -ms-interpolation-mode: bicubic; position: =
relative; display: inline-block; height: 48px; width: 48px; border-radius: =
2px; background-color: #eae6df;" width=3D"48" height=3D"48"> </a> </td> <td=
 valign=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adju=
st: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https:=
//www.linkedin.com/comm/jobs/view/4069865121/?trackingId=3D0XqiOmGEQtSvZAbt=
vuDr6Q%3D%3D&amp;refId=3Dosj173V7ROi2YuOAJxtWlg%3D%3D&amp;lipi=3Durn%3Ali%3=
Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midTo=
ken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_=
digest-job_card-0-job_posting&amp;trkEmail=3Deml-jobs_jymbii_digest-job_car=
d-0-job_posting-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz=
-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0Mzkx=
YWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDA=
xNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" style=3D"color: #0a6=
6c2; cursor: pointer; display: inline-block; text-decoration: none; -webkit=
-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <table role=3D"prese=
ntation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" wi=
dth=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust:=
 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td cla=
ss=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: =
100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;"> =
<a href=3D"https://www.linkedin.com/comm/jobs/view/4069865121/?trackingId=
=3D0XqiOmGEQtSvZAbtvuDr6Q%3D%3D&amp;refId=3Dosj173V7ROi2YuOAJxtWlg%3D%3D&am=
p;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1E=
xBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=
=3Deml-jobs_jymbii_digest-job_card-0-jobcard_body&amp;trkEmail=3Deml-jobs_j=
ymbii_digest-job_card-0-jobcard_body-null-fojfqf~m722jtfz~d9-null-null&amp;=
eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDU=
xN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NT=
gyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank"=
 class=3D"font-bold text-md leading-regular text-system-blue-50" style=3D"c=
olor: #0a66c2; cursor: pointer; display: inline-block; text-decoration: non=
e; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; font-size: 1=
6px; font-weight: 600; line-height: 1.25;"> Software Engineer Intern </a> <=
/td> </tr> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%;=
 -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; =
padding-bottom: 0px;"> <p class=3D"text-system-gray-100 text-xs leading-reg=
ular mt-0.5 line-clamp-1 text-ellipsis" style=3D"margin: 0; font-weight: 40=
0; margin-top: 4px; text-overflow: ellipsis; font-size: 12px; line-height: =
1.25; color: #1f1f1f; overflow: hidden; display: -webkit-box; -webkit-box-o=
rient: vertical; -webkit-line-clamp: 1;"> Blink22 &middot; Alexandria (Remo=
te) </p> </td> </tr> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <ta=
ble role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cel=
lpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tb=
ody> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0;" align=
=3D"left"> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=
=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endif]-=
-> <!--[if (gte mso 9)|(IE)]><tr> <td style=3D"padding-top: 4px;"><![endif]=
--> <table class=3D"inline-block" style=3D"-webkit-text-size-adjust: 100%; =
-ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; d=
isplay: inline-block;"> <tr> <td class=3D"job-card-flavor__container_redesi=
gn_shaded_background" style=3D"-webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin-top: 4=
px; margin-right: 4px; display: inline-block; border-radius: 4px; backgroun=
d-color: #f5f7f9; padding: 4px;"> <table role=3D"presentation" valign=3D"to=
p" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D=
"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspa=
ce: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-0.5 w-1" sty=
le=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table=
-lspace: 0pt; mso-table-rspace: 0pt; width: 8px; padding-right: 4px;" width=
=3D"8"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/3le24jev2hig88cuo=
kev26ksm" alt class=3D"w-2 h-2 block" style=3D"outline: none; text-decorati=
on: none; -ms-interpolation-mode: bicubic; display: block; height: 16px; wi=
dth: 16px;" width=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt;"> <p class=3D"job-card-flavor__detail" style=3D"margin: 0; f=
ont-weight: 400; font-family: -apple-system, system-ui, BlinkMacSystemFont,=
 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen=
 Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Se=
goe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-s=
erif; font-size: 12px; line-height: 1.25; color: #666666;"> Actively recrui=
ting </p> </td> </tr> </tbody> </table> </td> </tr> </table> <!--[if (gte m=
so 9)|(IE)]></td> </tr><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> <td styl=
e=3D"padding-top: 4px;"><![endif]--> <table class=3D"inline-block" style=3D=
"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspa=
ce: 0pt; mso-table-rspace: 0pt; display: inline-block;"> <tr> <td class=3D"=
job-card-flavor__container_redesign_shaded_background" style=3D"-webkit-tex=
t-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso=
-table-rspace: 0pt; margin-top: 4px; margin-right: 4px; display: inline-blo=
ck; border-radius: 4px; background-color: #f5f7f9; padding: 4px;"> <table r=
ole=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadd=
ing=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-=
size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> =
<tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width=
: 8px; padding-right: 4px;" width=3D"8"> <img src=3D"https://static.licdn.c=
om/aero-v1/sc/h/1sk0fbe9amaiat5elvybbtxsm" alt=3D"Easy Apply" class=3D"w-2 =
h-2 rounded-sm block" style=3D"outline: none; text-decoration: none; -ms-in=
terpolation-mode: bicubic; display: block; height: 16px; width: 16px; borde=
r-radius: 4px;" width=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text=
-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-=
table-rspace: 0pt;"> <p class=3D"job-card-flavor__detail" style=3D"margin: =
0; font-weight: 400; font-family: -apple-system, system-ui, BlinkMacSystemF=
ont, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Ox=
ygen Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji',=
 'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sa=
ns-serif; font-size: 12px; line-height: 1.25; color: #666666;"> Easy Apply =
</p> </td> </tr> </tbody> </table> </td> </tr> </table> <!--[if (gte mso 9)=
|(IE)]></td> </tr><![endif]--> <!--[if (gte mso 9)|(IE)]><table cellpadding=
=3D"0" cellspacing=3D"0" border=3D"0" role=3D"presentation" style=3D"width:=
 100%;"><![endif]--> </td> </tr> </tbody> </table> </td> </tr> </tbody> </t=
able> </a> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td=
> </tr> </tbody> </table> </td> </tr> <tr data-test-id=3D"see-more-jobs-but=
ton"> <td class=3D"pt-3 text-left" style=3D"-webkit-text-size-adjust: 100%;=
 -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; =
padding-top: 24px; text-align: left;"> <table role=3D"presentation" valign=
=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" cl=
ass=3D"email-button " data-test-id=3D"email-button" style=3D"-webkit-text-s=
ize-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-ta=
ble-rspace: 0pt;"> <tbody> <tr> <td valign=3D"middle" align=3D"left" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://www.linkedin.com/co=
mm/jobs/collections/recommended?origin=3DJYMBII_EMAIL&amp;lgCta=3Deml-jymbi=
i-bottom-see-all-jobs&amp;lgTemp=3Djobs_jymbii_digest&amp;lipi=3Durn%3Ali%3=
Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midTo=
ken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_=
digest-jymbii-0-all~job~postings&amp;trkEmail=3Deml-jobs_jymbii_digest-jymb=
ii-0-all~job~postings-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m7=
22jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZD=
A0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiN=
DUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" aria-label=3D"=
See all jobs" class=3D"align-top no-underline " style=3D"color: #0a66c2; cu=
rsor: pointer; display: inline-block; text-decoration: none; -webkit-text-s=
ize-adjust: 100%; -ms-text-size-adjust: 100%; vertical-align: top; text-dec=
oration-line: none;"> <table role=3D"presentation" valign=3D"top" border=3D=
"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"auto" class=3D"border-sepa=
rate " style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;=
 mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: separate;">=
 <tbody> <tr> <td class=3D"btn-md btn-primary border-color-brand button-lin=
k leading-regular !min-h-[auto] !shadow-none border-1 border-solid primary-=
cta" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; m=
so-table-lspace: 0pt; mso-table-rspace: 0pt; height: min-content; border-ra=
dius: 24px; padding-top: 12px; padding-bottom: 12px; padding-left: 24px; pa=
dding-right: 24px; text-align: center; font-size: 16px; font-weight: 600; c=
ursor: pointer; text-decoration-line: none; background-color: #0a66c2; colo=
r: #ffffff; border-width: 1px; border-style: solid; border-color: #0a66c2; =
line-height: 1.25; min-height: auto !important; box-shadow: 0 0 #0000, 0 0 =
#0000, 0 0 #0000 !important;"> <a href=3D"https://www.linkedin.com/comm/job=
s/collections/recommended?origin=3DJYMBII_EMAIL&amp;lgCta=3Deml-jymbii-bott=
om-see-all-jobs&amp;lgTemp=3Djobs_jymbii_digest&amp;lipi=3Durn%3Ali%3Apage%=
3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3D=
AQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest=
-jymbii-0-all~job~postings&amp;trkEmail=3Deml-jobs_jymbii_digest-jymbii-0-a=
ll~job~postings-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz=
-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0Mzkx=
YWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDA=
xNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" tabindex=3D"-1" aria=
-hidden=3D"true" class=3D"no-underline" style=3D"color: #0a66c2; cursor: po=
inter; display: inline-block; text-decoration: none; -webkit-text-size-adju=
st: 100%; -ms-text-size-adjust: 100%; text-decoration-line: none;"> <span c=
lass=3D"no-underline text-white" style=3D"color: #ffffff; text-decoration-l=
ine: none;"> See all jobs </span> </a> </td> </tr> </tbody> </table> </a> <=
/td> </tr> </tbody> </table> </td> </tr> <tr data-test-id=3D"premium-upsell=
-divider"> <td class=3D"pt-3" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; paddi=
ng-top: 24px;"> <hr class=3D"bg-[#D9D9D9] border-0 h-[1px] m-0" style=3D"ma=
rgin: 0px; height: 1px; border-width: 0; background-color: #D9D9D9;"> </td>=
 </tr> <tr> <td class=3D"pt-3" data-test-id=3D"premium-upsell-v2" style=3D"=
-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspac=
e: 0pt; mso-table-rspace: 0pt; padding-top: 24px;"> <table role=3D"presenta=
tion" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=
=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 10=
0%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=
=3D"text-lg leading-regular text-color-text font-bold pt-1.5" data-test-id=
=3D"premium-upsell-title" style=3D"-webkit-text-size-adjust: 100%; -ms-text=
-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-t=
op: 12px; font-size: 20px; font-weight: 600; line-height: 1.25; color: rgba=
(0, 0, 0, 0.9);"> Stand out and get ahead </td> </tr> <tr> <td class=3D"tex=
t-sm leading-regular text-color-text pt-1.5" data-test-id=3D"premium-upsell=
-subtitle" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 1=
00%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 12px; font-=
size: 14px; line-height: 1.25; color: rgba(0, 0, 0, 0.9);"> Apply to jobs w=
here you=E2=80=99re a top applicant based on your skills and experience. </=
td> </tr> <tr> <td class=3D"pt-1.5" data-test-id=3D"premium-upsell-social-p=
roof" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; =
mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 12px;"> <table r=
ole=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadd=
ing=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-=
size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> =
<tr> <td class=3D"w-4 pr-1" data-test-id=3D"social-proof-image" style=3D"-w=
ebkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace:=
 0pt; mso-table-rspace: 0pt; width: 32px; padding-right: 8px;" width=3D"32"=
> <img class=3D"inline-block relative bg-color-entity-ghost-background clip=
-path-circle-50 rounded-full w-4 h-4 !block" src=3D"https://media.licdn.com=
/dms/image/v2/C4D03AQEIM_jZftZjEg/profile-displayphoto-shrink_400_400/profi=
le-displayphoto-shrink_400_400/0/1654426617609?e=3D2147483647&amp;v=3Dbeta&=
amp;t=3Dt6Shz_myCBXunkWzsmslyPxIQzmncdlSNf3NmapMLbg" alt=3D"Social Proof Pr=
ofile Picture" style=3D"outline: none; text-decoration: none; -ms-interpola=
tion-mode: bicubic; position: relative; height: 32px; width: 32px; border-r=
adius: 9999px; background-color: #eae6df; clip-path: circle(50%); display: =
block !important;" width=3D"32" height=3D"32"> </td> <td class=3D"text-xs l=
eading-regular text-color-text-low-emphasis" data-test-id=3D"social-proof-t=
ext" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; m=
so-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 12px; line-height: =
1.25; color: rgba(0, 0, 0, 0.6);"> Ziad and millions of members use Premium=
 </td> </tr> </tbody> </table> </td> </tr> <tr> <td class=3D"pt-1.5" data-t=
est-id=3D"premium-upsell-cta" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; paddi=
ng-top: 12px;"> <table role=3D"presentation" valign=3D"top" border=3D"0" ce=
llspacing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"email-button " da=
ta-test-id=3D"email-button" style=3D"-webkit-text-size-adjust: 100%; -ms-te=
xt-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbod=
y> <tr> <td valign=3D"middle" align=3D"left" style=3D"-webkit-text-size-adj=
ust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rsp=
ace: 0pt;"> <a href=3D"http://www.linkedin.com/comm/premium/products/?upsel=
lOrderOrigin=3DTracking%3Av1%3Aemail_jymbii_upsell%3AEmail+Stork%3AMarketin=
g&amp;referenceId=3DAwhXOYqhS32MJ6KppD17lg%3D%3D&amp;isSS=3Dfalse&amp;lipi=
=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D=
%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml=
-jobs_jymbii_digest-jymbii-0-premium~upsell~v2&amp;trkEmail=3Deml-jobs_jymb=
ii_digest-jymbii-0-premium~upsell~v2-null-fojfqf~m722jtfz~d9-null-null&amp;=
eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDU=
xN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NT=
gyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank"=
 aria-label=3D"Try Premium for EGP0" class=3D"align-top no-underline " styl=
e=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decoratio=
n: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; vertic=
al-align: top; text-decoration-line: none;"> <table role=3D"presentation" v=
align=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"aut=
o" class=3D"border-separate " style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; borde=
r-collapse: separate;"> <tbody> <tr> <td class=3D"btn-md btn-premium border=
-none button-link leading-regular !min-h-[auto] !shadow-none border-1 borde=
r-solid" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100=
%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; height: min-content; borde=
r-radius: 24px; padding-top: 12px; padding-bottom: 12px; padding-left: 24px=
; padding-right: 24px; text-align: center; font-size: 16px; font-weight: 60=
0; cursor: pointer; text-decoration-line: none; background-color: #f8c77e; =
border-width: 1px; border-style: none; line-height: 1.25; color: rgba(0, 0,=
 0, 0.9) !important; padding: 6px 12px !important; min-height: auto !import=
ant; box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000 !important;"> <a href=3D"h=
ttp://www.linkedin.com/comm/premium/products/?upsellOrderOrigin=3DTracking%=
3Av1%3Aemail_jymbii_upsell%3AEmail+Stork%3AMarketing&amp;referenceId=3DAwhX=
OYqhS32MJ6KppD17lg%3D%3D&amp;isSS=3Dfalse&amp;lipi=3Durn%3Ali%3Apage%3Aemai=
l_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-h=
vPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-jymbi=
i-0-premium~upsell~v2&amp;trkEmail=3Deml-jobs_jymbii_digest-jymbii-0-premiu=
m~upsell~v2-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&=
amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4=
YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmU=
zZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blank" tabindex=3D"-1" aria-hid=
den=3D"true" class=3D"no-underline" style=3D"color: #0a66c2; cursor: pointe=
r; display: inline-block; text-decoration: none; -webkit-text-size-adjust: =
100%; -ms-text-size-adjust: 100%; text-decoration-line: none;"> <span class=
=3D"no-underline text-color-text" style=3D"color: rgba(0, 0, 0, 0.9); text-=
decoration-line: none;"> Try Premium for EGP0 </span> </a> </td> </tr> </tb=
ody> </table> </a> </td> </tr> </tbody> </table> </td> </tr> <tr> <td class=
=3D"text-xs pt-1.5 text-color-text-low-emphasis" data-test-id=3D"premium-up=
sell-footer-text" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-ad=
just: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 12px=
; font-size: 12px; color: rgba(0, 0, 0, 0.6);"> 1-month free trial. Cancel =
anytime. </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </div>=
 </td> </tr> <tr> <td class=3D"bg-color-background-canvas p-3" style=3D"-we=
bkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: =
0pt; mso-table-rspace: 0pt; background-color: #f3f2f0; padding: 24px;"> <ta=
ble role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cel=
lpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tb=
ody> <tr> <td class=3D"text-center pb-2" style=3D"-webkit-text-size-adjust:=
 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace:=
 0pt; padding-bottom: 16px; text-align: center;"> <h2 class=3D"text-lg text=
-teal-80" style=3D"margin: 0; font-weight: 500; font-size: 20px; color: #11=
4951;"> Get the new LinkedIn desktop app </h2> </td> </tr> <tr> <td class=
=3D"text-center pb-2" data-test-id=3D"footer-app-store-icon" style=3D"-webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0p=
t; mso-table-rspace: 0pt; padding-bottom: 16px; text-align: center;"> <a hr=
ef=3D"https://apps.microsoft.com/store/detail/9WZDNCRFJ4Q7?launch=3Dtrue&am=
p;cid=3Dlinkedin_email_upsell&amp;mode=3Dfull" target=3D"_blank" rel=3D"noo=
pener noreferrer" style=3D"color: #0a66c2; cursor: pointer; display: inline=
-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%;"> <img alt=3D"Get it from Microsoft" src=3D"https://static.=
licdn.com/aero-v1/sc/h/ejpkkpwvqks31a3cjqokb7fbm" class=3D"h-[40px] w-[112p=
x]" style=3D"outline: none; text-decoration: none; -ms-interpolation-mode: =
bicubic; height: 40px; width: 112px;" width=3D"112" height=3D"40"> </a> </t=
d> </tr> <tr> <td class=3D"text-center pb-2" style=3D"-webkit-text-size-adj=
ust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rsp=
ace: 0pt; padding-bottom: 16px; text-align: center;"> <h2 class=3D"text-md =
text-teal-80" style=3D"margin: 0; font-weight: 500; font-size: 16px; color:=
 #114951;"> Also available on mobile </h2> </td> </tr> <tr> <td class=3D"te=
xt-center" data-test-id=3D"footer-app-store-icon" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; text-align: center;"> <a href=3D"https://itunes.apple.com/us=
/app/linkedin/id288429040?pt=3D10746&amp;ct=3Dst_appsite_flagship&amp;mt=3D=
8" target=3D"_blank" rel=3D"noopener noreferrer" style=3D"color: #0a66c2; c=
ursor: pointer; display: inline-block; text-decoration: none; -webkit-text-=
size-adjust: 100%; -ms-text-size-adjust: 100%;"> <img alt=3D"Download on th=
e App Store" src=3D"https://static.licdn.com/aero-v1/sc/h/76yzkd0h5kiv27lrd=
4yaenylk" class=3D"h-[40px] w-[120px] pr-1" style=3D"outline: none; text-de=
coration: none; -ms-interpolation-mode: bicubic; height: 40px; width: 120px=
; padding-right: 8px;" width=3D"120" height=3D"40"> </a> <a href=3D"https:/=
/play.google.com/store/apps/details?id=3Dcom.linkedin.android&amp;referrer=
=3Dst_appsite_flagship" target=3D"_blank" rel=3D"noopener noreferrer" style=
=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration=
: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <img =
alt=3D"Get it on Google Play" src=3D"https://static.licdn.com/aero-v1/sc/h/=
142qudwblp58zwmc9vkqfplug" class=3D"h-[40px] w-[134px]" style=3D"outline: n=
one; text-decoration: none; -ms-interpolation-mode: bicubic; height: 40px; =
width: 134px;" width=3D"134" height=3D"40"> </a> </td> </tr> <tr> <td class=
=3D"py-2" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 10=
0%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 16px; paddin=
g-bottom: 16px;"><hr class=3D"border-none bg-[#e0dfdd] h-[1px]" style=3D"he=
ight: 1px; border-style: none; background-color: #e0dfdd;"></td> </tr> </tb=
ody> </table> <table role=3D"presentation" valign=3D"top" border=3D"0" cell=
spacing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"text-xs" style=3D"-=
webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace=
: 0pt; mso-table-rspace: 0pt; font-size: 12px;"> <tbody> <tr> <td class=3D"=
pb-1 m-0" data-test-id=3D"email-footer__intended" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; margin: 0px; padding-bottom: 8px;"> This email was intended =
for Abanoub Nashaat (CS Student || Aspiring Software Developer) </td> </tr>=
 <tr> <td class=3D"pb-1 m-0" style=3D"-webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin=
: 0px; padding-bottom: 8px;"> <a href=3D"https://www.linkedin.com/help/link=
edin/answer/4788?lang=3Den&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_d=
igest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;mi=
dSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-SecurityHelp-0-foote=
rglimmer&amp;trkEmail=3Deml-jobs_jymbii_digest-SecurityHelp-0-footerglimmer=
-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToke=
n=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA=
4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNT=
k4MTk2NjlmNiwxLDE%3D" target=3D"_blank" class=3D"text-inherit underline" st=
yle=3D"cursor: pointer; display: inline-block; text-decoration: none; -webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; color: inherit; text=
-decoration-line: underline;">Learn why we included this.</a> </td> </tr> <=
tr> <td class=3D"pb-1 m-0" style=3D"-webkit-text-size-adjust: 100%; -ms-tex=
t-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin: =
0px; padding-bottom: 8px;">You are receiving Jobs You Might Be Interested I=
n emails.</td> </tr> <tr> <td class=3D"pb-1 m-0" style=3D"-webkit-text-size=
-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table=
-rspace: 0pt; margin: 0px; padding-bottom: 8px;"> <a href=3D"https://www.li=
nkedin.com/comm/jobs/alerts?lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_dige=
st%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSi=
g=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-null-0-null&amp;trkEmai=
l=3Deml-jobs_jymbii_digest-null-0-null-null-fojfqf~m722jtfz~d9-null-null&am=
p;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkN=
DUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU2ZmI1=
NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"_blan=
k" trackingmodule=3D"footer" trackingsuffix=3D"manage_alert_mercado" style=
=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration=
: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> Manag=
e recommendations</a> =C2=A0=C2=A0=C2=B7=C2=A0=C2=A0 <a href=3D"https://www=
.linkedin.com/comm/psettings/email-unsubscribe?lipi=3Durn%3Ali%3Apage%3Aema=
il_jobs_jymbii_digest%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-=
hvPD2eSGA&amp;midSig=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-unsu=
bscribe-0-footerGlimmer&amp;trkEmail=3Deml-jobs_jymbii_digest-unsubscribe-0=
-footerGlimmer-null-fojfqf~m722jtfz~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-=
d9&amp;loid=3DAQEDooge5KsMWgAAAZT6yZZt7mEiamn3vDLNwUO8_sLRqGuTCqq7Ok7Ab9rbK=
sKpXRiyq6GWkX-tQvMiQI8Dj85auRAss-qMXf9m1AEO_Q" target=3D"_blank" class=3D"t=
ext-inherit underline" style=3D"cursor: pointer; display: inline-block; tex=
t-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 1=
00%; color: inherit; text-decoration-line: underline;">Unsubscribe</a> =C2=
=A0=C2=A0=C2=B7=C2=A0=C2=A0 <a href=3D"https://www.linkedin.com/help/linked=
in/answer/67?lang=3Den&amp;lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_diges=
t%3B8MfMtvH7Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=
=3D1UWL5KEFK3mbE1&amp;trk=3Deml-jobs_jymbii_digest-help-0-footerglimmer&amp=
;trkEmail=3Deml-jobs_jymbii_digest-help-0-footerglimmer-null-fojfqf~m722jtf=
z~d9-null-null&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJl=
Y2JjM2JkMjQwNGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDd=
kZmI1NzNjOGU2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D=
" target=3D"_blank" class=3D"text-inherit underline" style=3D"cursor: point=
er; display: inline-block; text-decoration: none; -webkit-text-size-adjust:=
 100%; -ms-text-size-adjust: 100%; color: inherit; text-decoration-line: un=
derline;">Help</a> </td> </tr> <tr> <td class=3D"pb-1" style=3D"-webkit-tex=
t-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso=
-table-rspace: 0pt; padding-bottom: 8px;"> <a href=3D"https://www.linkedin.=
com/comm/feed/?lipi=3Durn%3Ali%3Apage%3Aemail_jobs_jymbii_digest%3B8MfMtvH7=
Sg2KWCe%2Bs1ExBg%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1UWL5KEFK=
3mbE1&amp;trk=3Deml-jobs_jymbii_digest-footer-0-logoGlimmer&amp;trkEmail=3D=
eml-jobs_jymbii_digest-footer-0-logoGlimmer-null-fojfqf~m722jtfz~d9-null-nu=
ll&amp;eid=3Dfojfqf-m722jtfz-d9&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQw=
NGVkNDUxN2U0YjM4YmNkZDA0MzkxYWI4YjYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmI1NzNjOGU=
2ZmI1NTgyZjZhYjVlMGFiNDUyNDAxNmUzZGMyNjMzNTk4MTk2NjlmNiwxLDE%3D" target=3D"=
_blank" style=3D"color: #0a66c2; cursor: pointer; display: inline-block; te=
xt-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: =
100%;"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/9ehe6n39fa07dc5ed=
zv0rla4e" alt=3D"LinkedIn" class=3D"block h-[14px] w-[56px] image-rendering=
-crisp" style=3D"outline: none; text-decoration: none; image-rendering: -mo=
z-crisp-edges; image-rendering: -o-crisp-edges; image-rendering: -webkit-op=
timize-contrast; image-rendering: crisp-edges; -ms-interpolation-mode: near=
est-neighbor; display: block; height: 14px; width: 56px;" width=3D"56" heig=
ht=3D"14"> </a> </td> </tr> <tr> <td data-test-copyright-text style=3D"-web=
kit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0=
pt; mso-table-rspace: 0pt;"> =C2=A9 2025 LinkedIn Corporation, 1&zwnj;000 W=
est Maude Avenue, Sunnyvale, CA 94085. <span data-test-trademarks-text> Lin=
kedIn and the LinkedIn logo are registered trademarks of LinkedIn. </span> =
</td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </t=
body> </table> <img alt role=3D"presentation" src=3D"https://www.linkedin.c=
om/emimp/ip_Wm05cVpuRm1MVzAzTWpKcWRHWjZMV1E1OmFtOWljMTlxZVcxaWFXbGZaR2xuWlh=
OMDo=3D.gif" style=3D"outline: none; text-decoration: none; -ms-interpolati=
on-mode: bicubic; width: 1px; height: 1px;" width=3D"1" height=3D"1"> </bod=
y> </html>
------=_Part_3296108_404475567.1739374303617--

