Delivered-To: <EMAIL>
Received: by 2002:a59:a3ad:0:b0:4b7:725a:7004 with SMTP id w13csp909603vqq;
        Wed, 12 Feb 2025 03:31:59 -0800 (PST)
X-Google-Smtp-Source: AGHT+IHl9d8gokFL9UR91bpCdymhUPm0W9BlBKsS4OxsnOMwhQ1qe/j6Rb/0HfnY79paQFCxjTiC
X-Received: by 2002:a05:6a20:7288:b0:1e1:aba4:20ab with SMTP id adf61e73a8af0-1ee5c72e3b5mr5015941637.5.1739359918667;
        Wed, 12 Feb 2025 03:31:58 -0800 (PST)
ARC-Seal: i=1; a=rsa-sha256; t=1739359918; cv=none;
        d=google.com; s=arc-20240605;
        b=HCOHO8OCt27lhDmCOd7f0ILW27UKLaQN3g6kD3lC1wPjOfKvajD2de4naSE9x2hnwK
         mXoIsLyJrsE9xDpDyQJDLwoq4y+WaDBM5N0tVV83JGkIB006daMiYWQlOZdhqgn3iati
         YM5cwvHIEeR6gTghGZ3lQSZ/ZrRPEylC4gtv6qgTJm3FwZzgFH2OqGF1hug/2wKKFJ8Q
         77HOf7zENj+p33Z3acBiKbe52s2ekYainHQPhZiM9s31AHb9pC2M7oAl9U8uLYVXwkp0
         ivWXsyH4nIGh0GccrDmSj0rfaDDdq3FdLmkAM5eQSFq4cBSMQJhyVlITRdH/KcrVrInQ
         7tcA==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=require-recipient-valid-since:feedback-id:list-unsubscribe-post
         :list-unsubscribe:date:to:mime-version:subject:message-id:from
         :dkim-signature:dkim-signature;
        bh=+vP4dm2KzLDYqV0IIr/nCc0opEM/IW4nX2L+goZ472c=;
        fh=wWzJLaixNKcc85d8NSKuGZjgZUuCbmE6EP1F5UkZyc0=;
        b=ZKFj+Ym1ZPKa+k5ASgDg8hCtFFanheOmYxsHLq7VLuTxlAbGuXSDuHeJJydJTK0zkn
         AvFttCJs5DqrzGdR7NHmn79Xf8UD36QZaWAPwLHidkYdT6G7y7uWyIKFrED1uIjQ/j+z
         96uSgn6We42Hb4NTuDP6BO+jtZTEGXAlEqmUxN4T2/+RwQW5KN2ztGAxWuxVU/Habavm
         iUUOPMltIVOqzbKbtYJ3Gpj62lckXWFcOAC9bxY5eOnOC2IIeJ62CBSggyAjNrbANUpE
         Fzm2TwYIBEAqOVFe0g0gsuRo0zlD6U/Y+rfCO+Xuy5LO+lc41yRSsUm1FTEC5c4bl1lU
         ESlg==;
        dara=google.com
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@maild.linkedin.com header.s=d2048-202308-0d header.b="Hdw/l53k";
       dkim=pass header.i=@linkedin.com header.s=d2048-202308-00 header.b=VYuCDGMW;
       spf=pass (google.com: <NAME_EMAIL> designates 2620:109:c003:104::186 as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=linkedin.com
Return-Path: <<EMAIL>>
Received: from maild-db.linkedin.com (maild-db.linkedin.com. [2620:109:c003:104::186])
        by mx.google.com with ESMTPS id 41be03b00d2f7-ad54486c8c3si12376066a12.620.2025.***********.57
        for <<EMAIL>>
        (version=TLS1_2 cipher=ECDHE-ECDSA-AES128-GCM-SHA256 bits=128/128);
        Wed, 12 Feb 2025 03:31:58 -0800 (PST)
Received-SPF: pass (google.com: <NAME_EMAIL> designates 2620:109:c003:104::186 as permitted sender) client-ip=2620:109:c003:104::186;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@maild.linkedin.com header.s=d2048-202308-0d header.b="Hdw/l53k";
       dkim=pass header.i=@linkedin.com header.s=d2048-202308-00 header.b=VYuCDGMW;
       spf=pass (google.com: <NAME_EMAIL> designates 2620:109:c003:104::186 as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=linkedin.com
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=maild.linkedin.com;
	s=d2048-202308-0d; t=1739359903;
	bh=+vP4dm2KzLDYqV0IIr/nCc0opEM/IW4nX2L+goZ472c=;
	h=From:Subject:MIME-Version:Content-Type:To:Date:X-LinkedIn-Class:
	 X-LinkedIn-Template:X-LinkedIn-fbl;
	b=Hdw/l53kg75fWtLHhg8fXjOyDDeij1AFLLIyoixdPSGooG149r7l5hpz2GoLUGdmC
	 gumOGcMhKPu+KLVu++43AnX0UiLko5IBor6k1JpVT4xXZQZnc3IxYF3hKF9Z7aqG+u
	 KL4I2Nhy7lx5mWGegrSuC4rmzMraQeNywWgb09ZdRvYSrHESSwTlUaaIdKu7PBRhep
	 cwUWJhov9iSMh7nFO/4jMkKyxkrTBv9oxWFKEya3CQjlRt2voPc/jhv4JYJ5d2FVRu
	 XfBPjlifY1Uje5pdmJJNLGt1XY0IvZHWlNqDNt4NOFJNCvOFxP7TJe1Kdm2GAS7VKk
	 o7S18cl6XBLrA==
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=linkedin.com;
	s=d2048-202308-00; t=1739359903;
	bh=+vP4dm2KzLDYqV0IIr/nCc0opEM/IW4nX2L+goZ472c=;
	h=From:Subject:MIME-Version:Content-Type:To:Date:X-LinkedIn-Class:
	 X-LinkedIn-Template:X-LinkedIn-fbl;
	b=VYuCDGMWKBjSadlUX8lnGKI2cBKj6Exqzsv2iRFLV11+1s7BqSrZxpyAdUSXuFhlM
	 XUkGFWI0xOwCuB6e0YFyMvA0ujyDy5SYdfUKwfLvCsM6ybDFyWBV/O/FGiBN75OnL3
	 x4pmx8XeYBCsLM/O27Q3VBIZqNFPSJV7HB8xT5p4CG10ILpfetJjo3c/poFoY0WStB
	 Kz7kHf+970mS7/TeovoOVf3FD5eTjNilspSwQQOYXzYSB2oyzpKi6r0Upfm3zEaQCs
	 9aPjRKZTfVdS5ufjSQ3e3Zz73ZNxm35gKOcKXATqCyZLGlQu6vqtWaoDHXEN7bsqyL
	 dwH2HO1p0SZcQ==
From: LinkedIn Job Alerts <<EMAIL>>
Message-ID: <<EMAIL>>
Subject: =?UTF-8?Q?3_new_jobs_for_=E2=80=9Cfull_stack_engineer=E2=80=9D?=
MIME-Version: 1.0
Content-Type: multipart/alternative; 
	boundary="----=_Part_2278920_1046509514.1739359903669"
To: Abanoub Nashaat <<EMAIL>>
Date: Wed, 12 Feb 2025 11:31:43 +0000 (UTC)
X-LinkedIn-Class: SAVEDSEARCH
X-LinkedIn-Template: email_job_alert_digest_01
X-LinkedIn-fbl: m2-at002lzf1lpllfsvfwmnqo20y8lsnuik962cmkvd16l8zcvbwtn0x6mad3xhko6v1wnuqx75sw2a6nbtdr145dnskhuqyn1fa62uya
X-LinkedIn-Id: fojfqf-m71tz67j-zy
List-Unsubscribe: <https://www.linkedin.com/job-alert-email-unsubscribe?savedSearchId=1740684387&lipi=urn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=AQHb-hvPD2eSGA&midSig=1HngcRkFijlXE1&ek=email_job_alert_digest_01&e=fojfqf-m71tz67j-zy&eid=fojfqf-m71tz67j-zy&m=unsub&ts=unsub&li=0&t=plh>
List-Unsubscribe-Post: List-Unsubscribe=One-Click
Feedback-ID: email_job_alert_digest_01:linkedin
Require-Recipient-Valid-Since: <EMAIL>; Thu, 4 Nov 2021 20:25:25 +0000

------=_Part_2278920_1046509514.1739359903669
Content-Type: text/plain;charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-ID: text-body

Your job alert for full stack engineer in Cairo
3 new jobs match your preferences.
         =20
Full Stack Engineer
Cyrafa
Cairo, Egypt
Apply with resume & profile
View job: https://www.linkedin.com/comm/jobs/view/4148715389/?trackingId=3D=
8jwU6q9Nbu3iJA51rld1Xg%3D%3D&refId=3DByteString%28length%3D16%2Cbytes%3D340=
64aa2...9118f29c%29&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_0=
1%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1Hngc=
RkFijlXE1&trk=3Deml-email_job_alert_digest_01-job_card-0-view_job&trkEmail=
=3Deml-email_job_alert_digest_01-job_card-0-view_job-null-fojfqf~m71tz67j~z=
y-null-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQ=
wNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2=
RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Full Stack Engineer
Premier Services and Recruitment
Cairo, Egypt
This company is actively hiring
Apply with resume & profile
View job: https://www.linkedin.com/comm/jobs/view/4149650574/?trackingId=3D=
hVRtYLJz40PW%2F5rwjNGP0Q%3D%3D&refId=3DByteString%28length%3D16%2Cbytes%3D3=
4064aa2...9118f29c%29&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest=
_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1Hn=
gcRkFijlXE1&trk=3Deml-email_job_alert_digest_01-job_card-0-view_job&trkEmai=
l=3Deml-email_job_alert_digest_01-job_card-0-view_job-null-fojfqf~m71tz67j~=
zy-null-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMj=
QwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM=
2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D

---------------------------------------------------------
 =20
         =20
Full Stack Software Engineer
RAMYRO Inc.
Cairo, Egypt
Apply with resume & profile
View job: https://www.linkedin.com/comm/jobs/view/4147655692/?trackingId=3D=
YWTV4iLhqrW425PHagFpbA%3D%3D&refId=3DByteString%28length%3D16%2Cbytes%3D340=
64aa2...9118f29c%29&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_0=
1%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1Hngc=
RkFijlXE1&trk=3Deml-email_job_alert_digest_01-job_card-0-view_job&trkEmail=
=3Deml-email_job_alert_digest_01-job_card-0-view_job-null-fojfqf~m71tz67j~z=
y-null-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQ=
wNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2=
RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D

---------------------------------------------------------
 =20
See all jobs on LinkedIn:  https://www.linkedin.com/comm/jobs/search?f_TPR=
=3Da1739267222-&savedSearchId=3D1740684387&alertAction=3Dviewjobs&origin=3D=
JOB_ALERT_EMAIL&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3B=
Yvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1HngcRkFi=
jlXE1&trk=3Deml-email_job_alert_digest_01-job~alert-0-see~all~jobs~text&trk=
Email=3Deml-email_job_alert_digest_01-job~alert-0-see~all~jobs~text-null-fo=
jfqf~m71tz67j~zy-null-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=3DMWIwMTE2ZTIx=
MzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmY=
yZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLD=
E%3D


Job search smarter with Premium
https://www.linkedin.com/comm/premium/products/?upsellOrderOrigin=3Demail_j=
ob_alert_digest_taj_upsell&utype=3Djob&lipi=3Durn%3Ali%3Apage%3Aemail_email=
_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2=
eSGA&midSig=3D1HngcRkFijlXE1&trk=3Deml-email_job_alert_digest_01-job~alert-=
0-premium~upsell~text&trkEmail=3Deml-email_job_alert_digest_01-job~alert-0-=
premium~upsell~text-null-fojfqf~m71tz67j~zy-null-null&eid=3Dfojfqf-m71tz67j=
-zy&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4=
ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGI=
wYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D
 =20

----------------------------------------

This email was intended for Abanoub Nashaat (CS Student || Aspiring Softwar=
e Developer)
Learn why we included this: https://www.linkedin.com/help/linkedin/answer/4=
788?lang=3Den&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYv=
x9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1HngcRkFijl=
XE1&trk=3Deml-email_job_alert_digest_01-SecurityHelp-0-textfooterglimmer&tr=
kEmail=3Deml-email_job_alert_digest_01-SecurityHelp-0-textfooterglimmer-nul=
l-fojfqf~m71tz67j~zy-null-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=3DMWIwMTE2=
ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTR=
mMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4Yi=
wxLDE%3D
You are receiving Job Alert emails.
Manage your job alerts:  https://www.linkedin.com/comm/jobs/alerts?lipi=3Du=
rn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ=
%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1HngcRkFijlXE1&trk=3Deml-email_jo=
b_alert_digest_01-null-0-null&trkEmail=3Deml-email_job_alert_digest_01-null=
-0-null-null-fojfqf~m71tz67j~zy-null-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=
=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4=
Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk=
3YmVlYTQ4YiwxLDE%3D=20
Unsubscribe: https://www.linkedin.com/job-alert-email-unsubscribe?savedSear=
chId=3D1740684387&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%=
3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1HngcRk=
FijlXE1&ek=3Demail_job_alert_digest_01&e=3Dfojfqf-m71tz67j-zy&eid=3Dfojfqf-=
m71tz67j-zy&m=3Dunsubscribe&ts=3DfooterGlimmer&li=3D0&t=3Dplh  =C2=A0=C2=A0=
=C2=B7=C2=A0=C2=A0Help: https://www.linkedin.com/help/linkedin/answer/67?la=
ng=3Den&lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvze=
T82uLzB%2FLbOwrQ%3D%3D&midToken=3DAQHb-hvPD2eSGA&midSig=3D1HngcRkFijlXE1&tr=
k=3Deml-email_job_alert_digest_01-help-0-textfooterglimmer&trkEmail=3Deml-e=
mail_job_alert_digest_01-help-0-textfooterglimmer-null-fojfqf~m71tz67j~zy-n=
ull-null&eid=3Dfojfqf-m71tz67j-zy&otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNG=
VkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZ=
GUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D

=C2=A9 2025 LinkedIn Corporation, 1zwnj000 West Maude Avenue, Sunnyvale, CA=
 94085.
LinkedIn and the LinkedIn logo are registered trademarks of LinkedIn.
------=_Part_2278920_1046509514.1739359903669
Content-Type: text/html;charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-ID: html-body

<html xmlns=3D"http://www.w3.org/1999/xhtml" lang=3D"en" xml:lang=3D"en"> <=
head> <meta http-equiv=3D"Content-Type" content=3D"text/html;charset=3Dutf-=
8"> <meta name=3D"HandheldFriendly" content=3D"true"> <meta name=3D"viewpor=
t" content=3D"width=3Ddevice-width; initial-scale=3D0.666667; user-scalable=
=3D0"> <meta name=3D"viewport" content=3D"width=3Ddevice-width"> <title></t=
itle> <style>
            @media (max-width: 512px) { .mercado-container { width: 100% !i=
mportant; } }
          </style> <style>
            @media (max-width: 480px) { .inline-button, .inline-button tabl=
e { display: none !important; }
            .full-width-button, .full-width-button table { display: table !=
important; } }
          </style> <style>body {font-family: -apple-system, system-ui, Blin=
kMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
            'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid S=
ans', 'Apple Color Emoji', 'Segoe UI Emoji',
            'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica=
, Arial, sans-serif;}</style> <!--[if mso]><style type=3D"text/css"> </styl=
e><![endif]--> <!--[if IE]><style type=3D"text/css"> </style><![endif]--> <=
style>
@media (max-width: 480px) {
  .inline-button,
.inline-button table {
    display: none !important;
  }

  .full-width-button,
.full-width-button table {
    display: table !important;
  }
}
@media (min-width: 576px) {
  .container {
    max-width: 576px;
  }

  .\!container {
    max-width: 576px !important;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }

  .\!container {
    max-width: 768px !important;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 992px;
  }

  .\!container {
    max-width: 992px !important;
  }
}
@media (min-width: 1128px) {
  .container {
    max-width: 1128px;
  }

  .\!container {
    max-width: 1128px !important;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }

  .\!container {
    max-width: 1200px !important;
  }
}
@media (min-width: 1440px) {
  .container {
    max-width: 1440px;
  }

  .\!container {
    max-width: 1440px !important;
  }
}
@media (min-width: 1680px) {
  .container {
    max-width: 1680px;
  }

  .\!container {
    max-width: 1680px !important;
  }
}
@media (min-width: 1920px) {
  .container {
    max-width: 1920px;
  }

  .\!container {
    max-width: 1920px !important;
  }
}
@media (min-width: 992px) {
  .base-detail-page__header .nav {
    margin-left: auto;
    margin-right: auto;
    width: 1128px;
  }
}
@media (max-width: 767px) {
  .embedded-social-share .modal__outlet {
    flex-direction: row;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
}
@media (max-width: 767px) {
  .social-action-bar--grid .embedded-social-share .modal__outlet {
    padding-left: 0px;
    padding-right: 0px;
  }
}
@media (max-width: 991px) {
  .nav .search-bar {
    order: 4;
  }
}
@media (max-width: 767px) {
  .nav .sign-in-card {
    display: none;
  }
}
@media (max-width: 767px) {
  .nav--minified-mobile .nav__cta-container > *:not(.nav__link-person) {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .nav .search-bar--minified-mobile {
    margin-bottom: 0px;
    height: 100%;
    flex-grow: 1;
    padding-top: 5px;
    padding-bottom: 5px;
    order: initial;
  }

  .nav .search-bar--minified-mobile .search-bar__placeholder {
    margin-top: 0px;
  }
}
@media (max-width: 767px) {
  .tw-link-column-item {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
@media (min-width: 992px) {
  .tw-link-column-item {
    margin-bottom: 8px;
  }

  .tw-linkster .tw-link-column-item {
    margin-bottom: 0px;
  }
}
@media (max-width: 767px) {
  .member-nav-menu .collapsible-dropdown__button {
    margin-top: 24px;
    margin-bottom: 24px;
    margin-left: 16px;
  }
}
@media (max-width: 767px) {
  .member-nav-menu .collapsible-dropdown__list {
    right: 0px;
    max-height: calc(100vh - 52px);
    overflow-y: auto;
  }
}
@media (max-width: 991px) {
  .base-search-bar .typeahead-input,
.base-search-bar .search-input {
    margin-bottom: 8px;
    width: 100%;
  }
}
@media (max-width: 767px) {
  .recent-searches.recent-searches--show {
    position: fixed;
    top: 168px;
    left: 0px;
    width: 100vw;
    min-width: 100vw;
    border-width: 0;
  }
}
@media (max-width: 991px) {
  .search-bar .dismissable-input {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0.75);
    color: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 14px;
    padding-bottom: 14px;
    font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', =
Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Canta=
rell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji'=
, 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif;
    font-size: 16px;
  }

  .search-bar .dismissable-input:hover {
    background-color: rgba(0, 0, 0, 0.04);
    border-color: rgba(0, 0, 0, 0.9);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.9);
    cursor: pointer;
    border-width: 1px;
  }

  .search-bar .dismissable-input:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.9);
    cursor: pointer;
    border-width: 1px;
  }

  .search-bar .dismissable-input:disabled {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:disabled:hover {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:disabled:focus {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:disabled:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input {
    background-color: rgba(0, 0, 0, 0);
    border-color: #cf0007;
    color: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 14px;
    padding-bottom: 14px;
    font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', =
Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Canta=
rell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji'=
, 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif;
    font-size: 16px;
  }

  .input-error .search-bar .dismissable-input:hover {
    background-color: rgba(0, 0, 0, 0.04);
    border-color: #8a0005;
    box-shadow: 0 0 0 1px #8a0005;
    cursor: pointer;
    border-width: 1px;
  }

  .input-error .search-bar .dismissable-input:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: #8a0005;
    cursor: pointer;
    border-width: 1px;
  }

  .input-error .search-bar .dismissable-input:disabled {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input:disabled:hover {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input:disabled:focus {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .input-error .search-bar .dismissable-input:disabled:active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0);
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    border-width: 1px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .base-search-bar .input-error .search-bar .dismissable-input.typeahead-in=
put {
    border-style: solid;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .base-search-bar .input-error .search-bar .dismissable-input.typeahead-in=
put:active {
    border-style: solid;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .base-search-bar .input-error .search-bar .dismissable-input.typeahead-in=
put:focus-within {
    border-style: solid;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }
}
@media (min-width: 992px) {
  .search-bar .dismissable-input {
    width: 0px;
    background-color: rgba(0, 0, 0, 0);
    box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000;
  }

  .search-bar .dismissable-input:hover {
    border-right-color: rgba(0, 0, 0, 0.08);
  }
}
@media (max-width: 991px) {
  .search-bar:not(.isExpanded) .base-search-bar {
    display: none !important;
  }
}
@media screen and (max-width: 991px) {
  @media (max-width: 991px) {
    .search-bar.isExpanded .search-bar__placeholder {
      display: none;
    }

    .search-bar.isExpanded .base-search-bar__form {
      display: flex;
    }

    position: fixed;
        top: 0px;
        bottom: 0px;
        left: 0px;
        right: 0px;
        z-index: 1000;
        margin-bottom: 0px;
        height: 100%;
        background-color: #ffffff;
    .search-bar.isExpanded .switcher-tabs__cancel-btn,
.search-bar.isExpanded .switcher-tabs {
      display: inherit;
    }

  }
}
@media screen and (min-width: 992px) {
  .switcher-tabs .switcher-tabs__button:after {
    border-bottom: none;
  }
}
@media (max-width: 767px) {
  .tabs__list {
    padding-left: 16px;
    padding-right: 16px;
  }
}
@media (max-width: 767px) {
  .typeahead-input__dropdown.typeahead-input__dropdown--show {
    position: fixed;
    top: 158px;
    left: 0px;
    width: 100vw;
    min-width: 100vw;
  }
}
@media (max-width: 991px) {
  .base-search-bar .dismissable-input {
    max-height: 40px;
  }
}
@media (max-width: 767px) {
  .base-main-card__media ~ .base-main-card__info {
    margin-left: 0px;
    margin-top: 16px;
  }

  .base-main-card__media ~ .base-main-card__info.article-card-social-action=
s {
    margin-left: 12px;
    margin-top: 0px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-img {
    width: 48px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"][data-entity-type=3D"orga=
nization"] .pub-entity-img img {
    height: 48px;
    width: 48px;
    border-radius: 0px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"][data-entity-type=3D"memb=
er"] .pub-entity-img img {
    height: 56px;
    width: 56px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text {
    padding-left: 12px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text h1 {
    margin-bottom: 2px;
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text h3 {
    margin-bottom: 2px;
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .series-article-body [data-type=3D"entityEmbed"] .pub-entity-text p {
    margin-bottom: 2px;
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .mx-main-feed-card-no-gutter {
    margin-left: -16px;
    margin-right: -16px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mx-main-feed-card-no-gutter {
    margin-left: -16px;
    margin-right: -16px;
  }
}
@media (max-width: 767px) {
  .px-main-feed-card-no-gutter {
    padding-left: 16px;
    padding-right: 16px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .px-main-feed-card-no-gutter {
    margin-left: -16px;
    margin-right: -16px;
    padding-left: 16px;
    padding-right: 16px;
  }
}
@media (max-width: 767px) {
  .main-feed-activity-card__header {
    padding-right: 40px;
  }
}
@media (min-width: 992px) {
  .filter .collapsible-dropdown__list {
    max-height: 400px;
  }
}
@media (forced-colors: active) {
  .filter-button--selected {
    border-width: 4px;
  }
}
@media (max-width: 767px) {
  .base-card .duration {
    font-size: 12px;
    font-weight: 400;
  }
}
@media (max-width: 767px) {
  .hide-on-mobile {
    display: none;
  }
}
@media (max-width: 767px) {
  .social-action-bar__button {
    flex-direction: row;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
}
@media (max-width: 767px) {
  .social-action-bar__icon--svg {
    margin: 0px;
    height: 24px;
    width: 24px;
  }

  .social-action-bar__button-text {
    padding-left: 4px;
  }
}
@media screen and (max-width: 767px) {
  .social-action-bar .social-action-bar__button--transcript {
    flex-direction: column;
    padding-top: 4px;
    padding-bottom: 4px;
    font-size: 12px;
  }

  .social-action-bar .social-action-bar__icon--transcript {
    margin-right: 0px !important;
    margin-bottom: 4px !important;
  }

  .social-action-bar__button-text--transcript {
    padding-left: 0px !important;
  }
}
@media (max-width: 767px) {
  .social-action-bar--grid .social-action-bar__button,
.social-action-bar--grid .social-share {
    padding-left: 0px;
    padding-right: 0px;
  }
}
@media (min-width: 245px) {
  .social-action-bar--grid {
    grid-template-columns: repeat(2,1fr);
  }

  .social-action-bar--grid .share-button {
    grid-column: span 2;
  }
}
@media (min-width: 330px) {
  .social-action-bar--grid {
    grid-template-columns: repeat(auto-fit,minmax(90px,1fr));
  }

  .social-action-bar--grid .share-button {
    grid-column: span 1;
  }
}
@media only screen and (min-width: 30.06em) {
  .card-cta-container-mobile {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
  }

  .card-cta-container-desktop {
    display: block !important;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    overflow: visible !important;
  }
}
@media (min-width: 768px) {
  .after\:pointer-events-none::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:absolute::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:top-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:right-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:bottom-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:left-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:\!left-\[-19px\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:\!top-\[6px\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:ml-0\.5::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:ml-0::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:ml-\[74px\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:block::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:h-full::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:w-full::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:rounded-full::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:border-1::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:border-solid::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:border-color-divider::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:bg-gradient-to-b::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:from-color-transparent::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:to-color-background-scrim::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:text-color-text-low-emphasis::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:no-underline::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:shadow-\[0_0_0_500px_var\(--color-scrim\)\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:content-\[\'\/\'\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:content-\[\"\"\]::after {
    content: "";
  }
}
@media (min-width: 768px) {
  .after\:content-\[\'\'\]::after {
    content: "";
  }
}
@media (prefers-color-scheme: dark) {
  .dark\:bg-color-surface-accent-4 {
    background-color: #dde7f1;
  }
}
@media (min-width: 992px) {
  .md\:overflow-x-hidden {
    overflow-x: hidden;
  }
}
@media (max-width: 767px) {
  .babybear\:\!-top-0\.5 {
    top: -4px !important;
  }

  .babybear\:\!-top-0 {
    top: -0px !important;
  }

  .babybear\:right-4 {
    right: 32px;
  }

  .babybear\:bottom-0 {
    bottom: 0px;
  }

  .babybear\:top-0\.5 {
    top: 4px;
  }

  .babybear\:top-0 {
    top: 0px;
  }

  .babybear\:bottom-\[120\%\] {
    bottom: 120%;
  }

  .babybear\:left-3 {
    left: 24px;
  }

  .babybear\:-top-1 {
    top: -8px;
  }

  .babybear\:z-0 {
    z-index: 0;
  }

  .babybear\:order-last {
    order: 9999;
  }

  .babybear\:btn-sm {
    height: min-content;
    min-height: 32px;
    border-radius: 24px;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 16px;
    padding-right: 16px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
  }

  .babybear\:btn-sm,
.babybear\:btn-sm:visited,
.babybear\:btn-sm:focus {
    cursor: pointer;
    text-decoration-line: none;
  }

  .babybear\:btn-sm:hover,
.babybear\:btn-sm:visited:hover {
    text-decoration-line: none;
  }

  .babybear\:btn-sm:disabled {
    cursor: not-allowed;
  }

  .babybear\:m-0 {
    margin: 0px;
  }

  .babybear\:my-2 {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .babybear\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .babybear\:mt-0\.5 {
    margin-top: 4px;
  }

  .babybear\:mt-0 {
    margin-top: 0px;
  }

  .babybear\:ml-0 {
    margin-left: 0px;
  }

  .babybear\:mr-0 {
    margin-right: 0px;
  }

  .babybear\:mb-2 {
    margin-bottom: 16px;
  }

  .babybear\:mb-0\.5 {
    margin-bottom: 4px;
  }

  .babybear\:mb-0 {
    margin-bottom: 0px;
  }

  .babybear\:mb-1 {
    margin-bottom: 8px;
  }

  .babybear\:mt-\[6px\] {
    margin-top: 6px;
  }

  .babybear\:-mt-0\.5 {
    margin-top: -4px;
  }

  .babybear\:-mt-0 {
    margin-top: -0px;
  }

  .babybear\:ml-1 {
    margin-left: 8px;
  }

  .babybear\:btn-secondary-emphasis {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(0, 0, 0, 0);
    color: #0a66c2;
  }

  .babybear\:btn-secondary-emphasis:visited {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(0, 0, 0, 0);
    color: #0a66c2;
  }

  .babybear\:btn-secondary-emphasis:focus {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(0, 0, 0, 0);
    color: #0a66c2;
  }

  .babybear\:btn-secondary-emphasis:hover {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(112, 181, 249, 0.1);
    color: #004182;
  }

  .babybear\:btn-secondary-emphasis:visited:hover {
    box-shadow: 0 0 0 1px #0a66c2;
    background-color: rgba(112, 181, 249, 0.1);
    color: #004182;
  }

  .babybear\:btn-secondary-emphasis:active {
    box-shadow: 0 0 0 1px #004182;
    background-color: rgba(112, 181, 249, 0.2);
    color: #004182;
  }

  .babybear\:btn-secondary-emphasis:disabled {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0);
    background-color: rgba(0, 0, 0, 0.08);
    color: rgba(0, 0, 0, 0.3);
  }

  .babybear\:hidden {
    display: none;
  }

  .babybear\:h-\[360px\] {
    height: 360px;
  }

  .babybear\:h-6 {
    height: 48px;
  }

  .babybear\:h-\[72px\] {
    height: 72px;
  }

  .babybear\:h-4 {
    height: 32px;
  }

  .babybear\:h-\[26px\] {
    height: 26px;
  }

  .babybear\:h-\[59px\] {
    height: 59px;
  }

  .babybear\:h-auto {
    height: auto;
  }

  .babybear\:h-\[180px\] {
    height: 180px;
  }

  .babybear\:max-h-\[400px\] {
    max-height: 400px;
  }

  .babybear\:max-h-\[225px\] {
    max-height: 225px;
  }

  .babybear\:max-h-4 {
    max-height: 32px;
  }

  .babybear\:max-h-\[250px\] {
    max-height: 250px;
  }

  .babybear\:min-h-\[134px\] {
    min-height: 134px;
  }

  .babybear\:w-\[360px\] {
    width: 360px;
  }

  .babybear\:w-full {
    width: 100%;
  }

  .babybear\:w-11\/12 {
    width: 91.666667%;
  }

  .babybear\:w-\[199px\] {
    width: 199px;
  }

  .babybear\:w-6 {
    width: 48px;
  }

  .babybear\:w-\[26px\] {
    width: 26px;
  }

  .babybear\:w-\[105px\] {
    width: 105px;
  }

  .babybear\:w-\[calc\(100\%-68px\)\] {
    width: calc(100% - 68px);
  }

  .babybear\:min-w-full {
    min-width: 100%;
  }

  .babybear\:max-w-\[790px\] {
    max-width: 790px;
  }

  .babybear\:max-w-\[32px\] {
    max-width: 32px;
  }

  .babybear\:max-w-\[300px\] {
    max-width: 300px;
  }

  .babybear\:flex-none {
    flex: none;
  }

  .babybear\:flex-auto {
    flex: 1 1 auto;
  }

  .babybear\:basis-0 {
    flex-basis: 0px;
  }

  .babybear\:-translate-x-50\% {
    transform: translate(0, 0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY=
(1);
  }

  @media (max-width: 767px) {
    .babybear\:-translate-x-50\% {
      transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(=
1) scaleY(1);
    }

    @media (max-width: 767px) {
      .babybear\:-translate-x-50\% {
        transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scale=
X(1) scaleY(1);
      }

      @media (max-width: 767px) {
        .babybear\:-translate-x-50\% {
          transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) sca=
leX(1) scaleY(1);
        }

        @media (max-width: 767px) {
          .babybear\:-translate-x-50\% {
            transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0) s=
caleX(1) scaleY(1);
          }

          @media (max-width: 767px) {
            .babybear\:-translate-x-50\% {
              transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(0)=
 scaleX(1) scaleY(1);
            }

            @media (max-width: 767px) {
              .babybear\:-translate-x-50\% {
                transform: translate(-50%, 0) rotate(90deg) skewX(0) skewY(=
0) scaleX(1) scaleY(1);
              }

              @media (max-width: 767px) {
                .babybear\:-translate-x-50\% {
                  transform: translate(-50%, 0) rotate(90deg) skewX(0) skew=
Y(0) scaleX(1) scaleY(1);
                }

                @media (max-width: 767px) {
                  .babybear\:-translate-x-50\% {
                    transform: translate(-50%, 0) rotate(90deg) skewX(0) sk=
ewY(0) scaleX(1) scaleY(1);
                  }

                  @media (max-width: 767px) {
                    .babybear\:-translate-x-50\% {
                      transform: translate(-50%, 0) rotate(90deg) skewX(0) =
skewY(0) scaleX(1) scaleY(1);
                    }

                    @media (max-width: 767px) {
                      .babybear\:-translate-x-50\% {
                        transform: translate(-50%, 0) rotate(90deg) skewX(0=
) skewY(0) scaleX(1) scaleY(1);
                      }

                      @media (max-width: 767px) {
                        .babybear\:-translate-x-50\% {
                          transform: translate(-50%, 0) rotate(90deg) skewX=
(0) skewY(0) scaleX(1) scaleY(1);
                        }

                        @media (max-width: 767px) {
                          .babybear\:-translate-x-50\% {
                            transform: translate(-50%, 0) rotate(90deg) ske=
wX(0) skewY(0) scaleX(1) scaleY(1);
                          }

                          @media (max-width: 767px) {
                            .babybear\:-translate-x-50\% {
                              transform: translate(-50%, 0) rotate(90deg) s=
kewX(0) skewY(0) scaleX(1) scaleY(1);
                            }

                            @media (max-width: 767px) {
                              .babybear\:-translate-x-50\% {
                                transform: translate(-50%, 0) rotate(90deg)=
 skewX(0) skewY(0) scaleX(1) scaleY(1);
                              }

                              @media transform: translate(0, 0) rotate(0) s=
kewX(0) skewY(0) scaleX(1) scaleY(1);
     (max-width: 767px) {
                                .babybear\:rotate-90 {
                                  transform: translate(-50%, 0) rotate(90de=
g) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                }

                                @media (max-width: 767px) {
                                  .babybear\:rotate-90 {
                                    transform: translate(-50%, 0) rotate(90=
deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                  }

                                  @media (max-width: 767px) {
                                    .babybear\:rotate-90 {
                                      transform: translate(-50%, 0) rotate(=
90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                    }

                                    @media (max-width: 767px) {
                                      .babybear\:rotate-90 {
                                        transform: translate(-50%, 0) rotat=
e(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                      }

                                      @media (max-width: 767px) {
                                        .babybear\:rotate-90 {
                                          transform: translate(-50%, 0) rot=
ate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                        }

                                        @media (max-width: 767px) {
                                          .babybear\:rotate-90 {
                                            transform: translate(-50%, 0) r=
otate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                          }

                                          @media (max-width: 767px) {
                                            .babybear\:rotate-90 {
                                              transform: translate(-50%, 0)=
 rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                            }

                                            @media (max-width: 767px) {
                                              .babybear\:rotate-90 {
                                                transform: translate(-50%, =
0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                              }

                                              @media (max-width: 767px) {
                                                .babybear\:rotate-90 {
                                                  transform: translate(-50%=
, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                }

                                                @media (max-width: 767px) {
                                                  .babybear\:rotate-90 {
                                                    transform: translate(-5=
0%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                  }

                                                  @media (max-width: 767px)=
 {
                                                    .babybear\:rotate-90 {
                                                      transform: translate(=
-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                    }

                                                    @media (max-width: 767p=
x) {
                                                      .babybear\:rotate-90 =
{
                                                        transform: translat=
e(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                      }

                                                      @media (max-width: 76=
7px) {
                                                        .babybear\:rotate-9=
0 {
                                                          transform: transl=
ate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                        }

                                                        @media (max-width: =
767px) {
                                                          .babybear\:rotate=
-90 {
                                                            transform: tran=
slate(-50%, 0) rotate(90deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                                                          }

                                                          border-radius: 8p=
x;
        border-width: 1px;
        border-style: solid;
        border-color: rgba(0,
0,
0,
0.08);
        background-color: #ffffff;
    .babybear\:flex-row {
                                                            flex-direction:=
 row;
                                                          }

                                                          .babybear\:flex-c=
ol {
                                                            flex-direction:=
 column;
                                                          }

                                                          .babybear\:flex-w=
rap {
                                                            flex-wrap: wrap=
;
                                                          }

                                                          .babybear\:items-=
start {
                                                            align-items: fl=
ex-start;
                                                          }

                                                          .babybear\:items-=
center {
                                                            align-items: ce=
nter;
                                                          }

                                                          .babybear\:justif=
y-start {
                                                            justify-content=
: flex-start;
                                                          }

                                                          .babybear\:justif=
y-end {
                                                            justify-content=
: flex-end;
                                                          }

                                                          .babybear\:justif=
y-center {
                                                            justify-content=
: center;
                                                          }

                                                          .babybear\:justif=
y-between {
                                                            justify-content=
: space-between;
                                                          }

                                                          .babybear\:justif=
y-around {
                                                            justify-content=
: space-around;
                                                          }

                                                          .babybear\:self-s=
tart {
                                                            align-self: fle=
x-start;
                                                          }

                                                          .babybear\:self-c=
enter {
                                                            align-self: cen=
ter;
                                                          }

                                                          .babybear\:overfl=
ow-y-auto {
                                                            overflow-y: aut=
o;
                                                          }

                                                          .babybear\:rounde=
d-\[0px\] {
                                                            border-radius: =
0px;
                                                          }

                                                          .babybear\:border=
-r-0 {
                                                            border-right-wi=
dth: 0;
                                                          }

                                                          .babybear\:border=
-b-1 {
                                                            border-bottom-w=
idth: 1px;
                                                          }

                                                          .babybear\:border=
-solid {
                                                            border-style: s=
olid;
                                                          }

                                                          .babybear\:border=
-color-border-low-emphasis {
                                                            border-color: r=
gba(0, 0, 0, 0.3);
                                                          }

                                                          .babybear\:bg-col=
or-brand {
                                                            background-colo=
r: #0a66c2;
                                                          }

                                                          .babybear\:p-2 {
                                                            padding: 16px;
                                                          }

                                                          .babybear\:py-1 {
                                                            padding-top: 8p=
x;
                                                            padding-bottom:=
 8px;
                                                          }

                                                          .babybear\:px-2 {
                                                            padding-left: 1=
6px;
                                                            padding-right: =
16px;
                                                          }

                                                          .babybear\:px-0 {
                                                            padding-left: 0=
px;
                                                            padding-right: =
0px;
                                                          }

                                                          .babybear\:px-mob=
ile-container-padding {
                                                            padding-left: 1=
6px;
                                                            padding-right: =
16px;
                                                          }

                                                          .babybear\:py-3 {
                                                            padding-top: 24=
px;
                                                            padding-bottom:=
 24px;
                                                          }

                                                          .babybear\:pt-1 {
                                                            padding-top: 8p=
x;
                                                          }

                                                          .babybear\:pr-0\.=
5 {
                                                            padding-right: =
4px;
                                                          }

                                                          .babybear\:pr-0 {
                                                            padding-right: =
0px;
                                                          }

                                                          .babybear\:pl-0\.=
5 {
                                                            padding-left: 4=
px;
                                                          }

                                                          .babybear\:pl-0 {
                                                            padding-left: 0=
px;
                                                          }

                                                          .babybear\:pt-0\.=
5 {
                                                            padding-top: 4p=
x;
                                                          }

                                                          .babybear\:pb-0 {
                                                            padding-bottom:=
 0px;
                                                          }

                                                          .babybear\:pt-0 {
                                                            padding-top: 0p=
x;
                                                          }

                                                          .babybear\:pb-2 {
                                                            padding-bottom:=
 16px;
                                                          }

                                                          .babybear\:pb-\[1=
80px\] {
                                                            padding-bottom:=
 180px;
                                                          }

                                                          .babybear\:text-x=
s {
                                                            font-size: 12px=
;
                                                          }

                                                          .babybear\:text-s=
m {
                                                            font-size: 14px=
;
                                                          }

                                                          .babybear\:font-b=
old {
                                                            font-weight: 60=
0;
                                                          }

                                                          .babybear\:leadin=
g-\[150px\] {
                                                            line-height: 15=
0px;
                                                          }

                                                          .babybear\:last\:=
border-b-0:last-child {
                                                            border-bottom-w=
idth: 0;
                                                          }

                                                        }

                                                      }

                                                    }

                                                  }

                                                }

                                              }

                                            }

                                          }

                                        }

                                      }

                                    }

                                  }

                                }

                              }

                            }

                          }

                        }

                      }

                    }

                  }

                }

              }

            }

          }

        }

      }

    }

  }
}
@media (max-width: 991px) {
  .babymamabear\:invisible {
    visibility: hidden;
  }

  .babymamabear\:static {
    position: static;
  }

  .babymamabear\:mx-mobile-container-padding {
    margin-left: 16px;
    margin-right: 16px;
  }

  .babymamabear\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .babymamabear\:mr-3 {
    margin-right: 24px;
  }

  .babymamabear\:ml-\[-9999px\] {
    margin-left: -9999px;
  }

  .babymamabear\:mb-1\.5 {
    margin-bottom: 12px;
  }

  .babymamabear\:mb-1 {
    margin-bottom: 8px;
  }

  .babymamabear\:flex {
    display: flex;
  }

  .babymamabear\:hidden {
    display: none;
  }

  .babymamabear\:h-\[1px\] {
    height: 1px;
  }

  .babymamabear\:h-\[48px\] {
    height: 48px;
  }

  .babymamabear\:w-\[1px\] {
    width: 1px;
  }

  .babymamabear\:w-full {
    width: 100%;
  }

  .babymamabear\:w-\[100vw\] {
    width: 100vw;
  }

  .babymamabear\:min-w-\[50px\] {
    min-width: 50px;
  }

  .babymamabear\:basis-1\/2 {
    flex-basis: 50%;
  }

  .babymamabear\:flex-col {
    flex-direction: column;
  }

  .babymamabear\:flex-wrap {
    flex-wrap: wrap;
  }

  .babymamabear\:bg-color-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .babymamabear\:p-0 {
    padding: 0px;
  }

  .babymamabear\:px-mobile-container-padding {
    padding-left: 16px;
    padding-right: 16px;
  }

  .babymamabear\:py-1\.5 {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .babymamabear\:py-1 {
    padding-top: 8px;
    padding-bottom: 8px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mamabear\:absolute {
    position: absolute;
  }

  .mamabear\:right-4 {
    right: 32px;
  }

  .mamabear\:-top-0\.5 {
    top: -4px;
  }

  .mamabear\:-top-0 {
    top: -0px;
  }

  .mamabear\:top-\[58px\] {
    top: 58px;
  }

  .mamabear\:-top-1 {
    top: -8px;
  }

  .mamabear\:mr-3 {
    margin-right: 24px;
  }

  .mamabear\:hidden {
    display: none;
  }

  .mamabear\:\!h-4 {
    height: 32px !important;
  }

  .mamabear\:h-\[100\%\] {
    height: 100%;
  }

  .mamabear\:\!min-h-\[32px\] {
    min-height: 32px !important;
  }

  .mamabear\:w-\[744px\] {
    width: 744px;
  }

  .mamabear\:w-48 {
    width: 384px;
  }

  .mamabear\:w-full {
    width: 100%;
  }

  .mamabear\:w-\[calc\(100\%-97px\)\] {
    width: calc(100% - 97px);
  }

  .mamabear\:min-w-0 {
    min-width: 0px;
  }

  .mamabear\:max-w-\[790px\] {
    max-width: 790px;
  }

  .mamabear\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .mamabear\:container-lined {
    border-radius: 8px;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
  }

  .mamabear\:container-raised {
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
  }

  .mamabear\:flex-row {
    flex-direction: row;
  }

  .mamabear\:items-start {
    align-items: flex-start;
  }

  .mamabear\:items-center {
    align-items: center;
  }

  .mamabear\:self-center {
    align-self: center;
  }

  .mamabear\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .mamabear\:px-3 {
    padding-left: 24px;
    padding-right: 24px;
  }

  .mamabear\:px-mobile-container-padding {
    padding-left: 16px;
    padding-right: 16px;
  }

  .mamabear\:py-3 {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .mamabear\:px-2 {
    padding-left: 16px;
    padding-right: 16px;
  }

  .mamabear\:pt-desktop-content-top-margin {
    padding-top: 16px;
  }
}
@media (min-width: 768px) {
  .papamamabear\:pt-desktop-content-top-margin {
    padding-top: 16px;
  }

  .after\:papamamabear\:\!h-\[37px\]::after {
    content: undefined;
    height: 37px !important;
  }

  @media (min-width: 768px) {
    .after\:papamamabear\:\!h-\[37px\]::after {
      content: "";
    }

    content: "".after\:papamamabear\:\!h-\[37px\]::after:content-\[\"\"\]::=
after {
      content: "";
    }

    .after\:papamamabear\:\!h-\[37px\]::after:content-\[\'\/\'\]::after {
      content: "";
    }

    .after\:papamamabear\:up-down-divider::after {
      display: flex;
      height: 100%;
      width: 1px;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.3);
      text-align: center;
      content: undefined;
    }

    @media (min-width: 768px) {
      .after\:papamamabear\:up-down-divider::after {
        content: "";
      }

      content: "".after\:papamamabear\:up-down-divider::after:content-\[\"\=
"\]::after {
        content: "";
      }

      .after\:papamamabear\:up-down-divider::after:content-\[\'\/\'\]::afte=
r {
        content: "";
      }

    }

  }
}
@media (min-width: 992px) {
  .papabear\:absolute {
    position: absolute;
  }

  .papabear\:top-\[58px\] {
    top: 58px;
  }

  .papabear\:-top-1 {
    top: -8px;
  }

  .papabear\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .papabear\:\!mx-2 {
    margin-left: 16px !important;
    margin-right: 16px !important;
  }

  .papabear\:mt-\[-100px\] {
    margin-top: -100px;
  }

  .papabear\:mb-\[18px\] {
    margin-bottom: 18px;
  }

  .papabear\:mt-0 {
    margin-top: 0px;
  }

  .papabear\:mt-0\.5 {
    margin-top: 4px;
  }

  .papabear\:mr-2 {
    margin-right: 16px;
  }

  .papabear\:mr-3 {
    margin-right: 24px;
  }

  .papabear\:ml-column-gutter {
    margin-left: 24px;
  }

  .papabear\:inline-block {
    display: inline-block;
  }

  .papabear\:flex {
    display: flex;
  }

  .papabear\:hidden {
    display: none;
  }

  .papabear\:\!h-4 {
    height: 32px !important;
  }

  .papabear\:h-6 {
    height: 48px;
  }

  .papabear\:h-\[100\%\] {
    height: 100%;
  }

  .papabear\:\!min-h-\[32px\] {
    min-height: 32px !important;
  }

  .papabear\:min-h-\[96px\] {
    min-height: 96px;
  }

  .papabear\:w-\[400px\] {
    width: 400px;
  }

  .papabear\:w-content-max-w {
    width: 1128px;
  }

  .papabear\:w-core-rail-width {
    width: 784px;
  }

  .papabear\:w-right-rail-width {
    width: calc(1128px - 24px - 784px);
  }

  .papabear\:w-48 {
    width: 384px;
  }

  .papabear\:w-auto {
    width: auto;
  }

  .papabear\:w-\[1128px\] {
    width: 1128px;
  }

  .papabear\:w-\[calc\(100\%-97px\)\] {
    width: calc(100% - 97px);
  }

  .papabear\:min-w-0 {
    min-width: 0px;
  }

  .papabear\:max-w-\[550px\] {
    max-width: 550px;
  }

  .papabear\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .papabear\:container-lined {
    border-radius: 8px;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
  }

  .papabear\:container-raised {
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
  }

  .papabear\:flex-col {
    flex-direction: column;
  }

  .papabear\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .papabear\:items-start {
    align-items: flex-start;
  }

  .papabear\:justify-start {
    justify-content: flex-start;
  }

  .papabear\:justify-center {
    justify-content: center;
  }

  .papabear\:self-start {
    align-self: flex-start;
  }

  .papabear\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .papabear\:border-4 {
    border-width: 4px;
  }

  .papabear\:p-details-container-padding {
    padding: 24px;
  }

  .papabear\:p-0 {
    padding: 0px;
  }

  .papabear\:py-2 {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .papabear\:pt-desktop-content-top-margin {
    padding-top: 16px;
  }

  .papabear\:text-xl {
    font-size: 24px;
  }

  .papabear\:tab-vertical.tab-sm,
.papabear\:tab-vertical .tab-md,
.papabear\:tab-vertical .tab-lg {
    justify-content: flex-start;
  }

  .papabear\:tab-vertical.tab-selected:after {
    position: absolute;
    left: 0px;
    height: 100%;
    content: undefined;
    border-left: 4px solid rgba(0, 0, 0, 0.9);
  }

  @media (min-width: 992px) {
    .papabear\:tab-vertical.tab-selected:after {
      content: "";
    }

  }
}
</style></head> <body dir=3D"ltr" class=3D"font-sans bg-color-background-ca=
nvas w-full m-0 p-0 pt-1" style=3D"-webkit-text-size-adjust: 100%; -ms-text=
-size-adjust: 100%; margin: 0px; width: 100%; background-color: #f3f2f0; pa=
dding: 0px; padding-top: 8px; font-family: -apple-system, system-ui, BlinkM=
acSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Ox=
ygen, 'Oxygen Sans', Cantarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe U=
I Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, =
Arial, sans-serif;"> <div class=3D"h-0 opacity-0 text-transparent invisible=
 overflow-hidden w-0 max-h-[0]" style=3D"visibility: hidden; height: 0px; m=
ax-height: 0; width: 0px; overflow: hidden; opacity: 0; mso-hide: all;" dat=
a-email-preheader=3D"true">View jobs in Cairo</div> <div class=3D"h-0 opaci=
ty-0 text-transparent invisible overflow-hidden w-0 max-h-[0]" style=3D"vis=
ibility: hidden; height: 0px; max-height: 0; width: 0px; overflow: hidden; =
opacity: 0; mso-hide: all;"> =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=
=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=
=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0 =CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 =
=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=
=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0=CD=8F=C2=A0 </div> <table role=3D"present=
ation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" widt=
h=3D"512" align=3D"center" class=3D"mercado-container w-[512px] max-w-[512p=
x] mx-auto my-0 p-0 " style=3D"-webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin-left: =
auto; margin-right: auto; margin-top: 0px; margin-bottom: 0px; width: 512px=
; max-width: 512px; padding: 0px;"> <tbody> <tr> <td style=3D"-webkit-text-=
size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-t=
able-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"0=
" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"bg-color-back=
ground-container " style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-a=
djust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color=
: #ffffff;"> <tbody> <tr> <td class=3D"text-center p-3" style=3D"-webkit-te=
xt-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; ms=
o-table-rspace: 0pt; padding: 24px; text-align: center;"> <table role=3D"pr=
esentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0"=
 width=3D"100%" class=3D"min-w-full" style=3D"-webkit-text-size-adjust: 100=
%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt=
; min-width: 100%;"> <tbody> <tr> <td align=3D"left" valign=3D"middle" styl=
e=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-=
lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://www.linkedin.com/c=
omm/feed/?lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hv=
zeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRk=
FijlXE1&amp;trk=3Deml-email_job_alert_digest_01-header-0-home_glimmer_dynam=
ic_badging_high_dpi&amp;trkEmail=3Deml-email_job_alert_digest_01-header-0-h=
ome_glimmer_dynamic_badging_high_dpi-null-fojfqf~m71tz67j~zy-null-null&amp;=
eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDU=
xN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMG=
IwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank"=
 class=3D"w-[84px]" style=3D"color: #0a66c2; cursor: pointer; display: inli=
ne-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-s=
ize-adjust: 100%; width: 84px;"> <img class=3D"h-[37px] w-[101px]" alt=3D"L=
inkedIn" src=3D"https://www.linkedin.com/comm/dms/logo/v2?badgeTheme=3Dmerc=
ado&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvze=
T82uLzB%2FLbOwrQ%3D%3D&amp;midSig=3D1HngcRkFijlXE1&amp;midToken=3DAQHb-hvPD=
2eSGA&amp;trkEmail=3Deml-email_job_alert_digest_01-null-0-comms%7Ebadging%7=
Edynamic%7Eglimmer%7Ev2-null-fojfqf%7Em71tz67j%7Ezy-null-null&amp;trk=3Deml=
-email_job_alert_digest_01-null-0-comms%7Ebadging%7Edynamic%7Eglimmer%7Ev2&=
amp;_sig=3D28_4y_uRijlXE1" data-test-header-dynamic-badging-img-high-dpi st=
yle=3D"outline: none; text-decoration: none; -ms-interpolation-mode: bicubi=
c; height: 37px; width: 101px;" width=3D"101" height=3D"37"> </a> </td> <td=
 valign=3D"middle" align=3D"right" style=3D"-webkit-text-size-adjust: 100%;=
 -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"=
> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0=
" cellpadding=3D"0" width=3D"100%" data-test-header-profile style=3D"-webki=
t-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt=
; mso-table-rspace: 0pt;"> <tbody> <tr> <td align=3D"right" valign=3D"middl=
e" class=3D"w-[32px]" style=3D"-webkit-text-size-adjust: 100%; -ms-text-siz=
e-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 32px;"=
 width=3D"32"> <a href=3D"https://eg.linkedin.com/comm/in/pep0x?lipi=3Durn%=
3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D=
%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml=
-email_job_alert_digest_01-header-0-profile_glimmer&amp;trkEmail=3Deml-emai=
l_job_alert_digest_01-header-0-profile_glimmer-null-fojfqf~m71tz67j~zy-null=
-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkM=
jQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2Fk=
M2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=
=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display: inline-block=
; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adju=
st: 100%;"> <img alt=3D"Abanoub Nashaat" src=3D"https://media.licdn.com/dms=
/image/v2/D4D03AQEDH6nP3hqAEg/profile-displayphoto-shrink_200_200/profile-d=
isplayphoto-shrink_200_200/0/1705302302995?e=3D2147483647&amp;v=3Dbeta&amp;=
t=3D9nmr2MC1Wr3ArYEPRa75uP-u0klA1Y_Yel_-Q3Bm934" class=3D"rounded-[100%] w-=
[32px] h-[32px]" style=3D"outline: none; text-decoration: none; -ms-interpo=
lation-mode: bicubic; height: 32px; width: 32px; border-radius: 100%;" widt=
h=3D"32" height=3D"32"> </a> </td> </tr> </tbody> </table> </td> </tr> </tb=
ody> </table> </td> </tr> <tr> <td class=3D"px-3 pb-3" style=3D"-webkit-tex=
t-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso=
-table-rspace: 0pt; padding-left: 24px; padding-right: 24px; padding-bottom=
: 24px;"> <div> <table role=3D"presentation" valign=3D"top" border=3D"0" ce=
llspacing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"leading-regular" =
style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-ta=
ble-lspace: 0pt; mso-table-rspace: 0pt; line-height: 1.25;"> <tbody> <tr> <=
td class=3D"job-alert-header" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <h1=
 class=3D"text-display-sm font-normal leading-regular" data-test-id=3D"emai=
l-header" style=3D"margin: 0; font-size: 24px; font-weight: 400; line-heigh=
t: 1.25;"> <a href=3D"https://www.linkedin.com/comm/jobs/search?f_TPR=3Da17=
39267222-&amp;savedSearchId=3D1740684387&amp;alertAction=3Dviewjobs&amp;ori=
gin=3DJOB_ALERT_EMAIL&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_di=
gest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;=
midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-email~job~a=
lert~digest-0-header&amp;trkEmail=3Deml-email_job_alert_digest_01-email~job=
~alert~digest-0-header-null-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfqf-m=
71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3Z=
DA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdl=
MGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" class=3D"text=
-system-gray-90" style=3D"cursor: pointer; display: inline-block; text-deco=
ration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; c=
olor: #282828;"> Your job alert for <strong class=3D"font-bold" style=3D"fo=
nt-weight: 600;">full stack engineer</strong> </a> </h1> </td> </tr> <tr> <=
td class=3D"pt-1 job-alert-subheader" style=3D"-webkit-text-size-adjust: 10=
0%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0p=
t; padding-top: 8px;"> <table role=3D"presentation" valign=3D"top" border=
=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-adjust=
: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace=
: 0pt;"> <h2 class=3D"text-md font-normal" data-test-id=3D"email-subheader"=
 style=3D"margin: 0; font-size: 16px; font-weight: 400;"> 3 new jobs in Cai=
ro match your preferences. </h2> </td> </tr> </tbody> </table> </td> </tr> =
<tr> <td class=3D"pt-3" data-test-id=3D"job-card" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; padding-top: 24px;"> <table role=3D"presentation" valign=3D"=
top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text=
-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-=
table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"=
0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text=
-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-=
table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-adjust: 10=
0%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0p=
t;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=
=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: =
100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: =
0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=3D"top" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt; width: 48px; padding-right: 8px;" width=3D"48"> <a h=
ref=3D"https://www.linkedin.com/comm/jobs/view/4148715389/?trackingId=3D8jw=
U6q9Nbu3iJA51rld1Xg%3D%3D&amp;refId=3DByteString%28length%3D16%2Cbytes%3D34=
064aa2...9118f29c%29&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_dig=
est_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;m=
idSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-job_card-0-c=
ompany_logo&amp;trkEmail=3Deml-email_job_alert_digest_01-job_card-0-company=
_logo-null-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;ot=
pToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNz=
ljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjM=
zIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" style=3D"color: #0a66c2; curso=
r: pointer; display: inline-block; text-decoration: none; -webkit-text-size=
-adjust: 100%; -ms-text-size-adjust: 100%;"> <img class=3D"inline-block rel=
ative bg-color-entity-ghost-background w-6 h-6 rounded-[2px]" src=3D"https:=
//media.licdn.com/dms/image/v2/C4D0BAQGXsoivRx5hjw/company-logo_100_100/com=
pany-logo_100_100/0/1672129896242?e=3D2147483647&amp;v=3Dbeta&amp;t=3Dfd7RW=
484RhwX3DRLVGz3QmHAYED57rd8AwJv8kFMJww" alt=3D"Cyrafa" style=3D"outline: no=
ne; text-decoration: none; -ms-interpolation-mode: bicubic; position: relat=
ive; display: inline-block; height: 48px; width: 48px; border-radius: 2px; =
background-color: #eae6df;" width=3D"48" height=3D"48"> </a> </td> <td vali=
gn=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 1=
00%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://www=
.linkedin.com/comm/jobs/view/4148715389/?trackingId=3D8jwU6q9Nbu3iJA51rld1X=
g%3D%3D&amp;refId=3DByteString%28length%3D16%2Cbytes%3D34064aa2...9118f29c%=
29&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT=
82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFij=
lXE1&amp;trk=3Deml-email_job_alert_digest_01-job_card-0-job_posting&amp;trk=
Email=3Deml-email_job_alert_digest_01-job_card-0-job_posting-null-fojfqf~m7=
1tz67j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTI=
xMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMm=
YyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxL=
DE%3D" target=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display:=
 inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%;"> <table role=3D"presentation" valign=3D"top" border=
=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pb-0" style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt; padding-bottom: 0px;"> <a href=3D"https://www.linkedi=
n.com/comm/jobs/view/4148715389/?trackingId=3D8jwU6q9Nbu3iJA51rld1Xg%3D%3D&=
amp;refId=3DByteString%28length%3D16%2Cbytes%3D34064aa2...9118f29c%29&amp;l=
ipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2=
FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp=
;trk=3Deml-email_job_alert_digest_01-job_card-0-jobcard_body&amp;trkEmail=
=3Deml-email_job_alert_digest_01-job_card-0-jobcard_body-null-fojfqf~m71tz6=
7j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJ=
lY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZD=
dkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3=
D" target=3D"_blank" class=3D"font-bold text-md leading-regular text-system=
-blue-50" style=3D"color: #0a66c2; cursor: pointer; display: inline-block; =
text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; font-size: 16px; font-weight: 600; line-height: 1.25;"> Full Stack =
Engineer </a> </td> </tr> <tr> <td class=3D"pb-0" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; padding-bottom: 0px;"> <p class=3D"text-system-gray-100 text=
-xs leading-regular mt-0.5 line-clamp-1 text-ellipsis" style=3D"margin: 0; =
font-weight: 400; margin-top: 4px; text-overflow: ellipsis; font-size: 12px=
; line-height: 1.25; color: #1f1f1f; overflow: hidden; display: -webkit-box=
; -webkit-box-orient: vertical; -webkit-line-clamp: 1;"> Cyrafa &middot; Ca=
iro, Egypt (On-site) </p> </td> </tr> <tr> <td style=3D"-webkit-text-size-a=
djust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-r=
space: 0pt;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cell=
spacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-a=
djust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-r=
space: 0pt;"> <tbody> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms=
-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font=
-size: 0;" align=3D"left"> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"=
0" cellspacing=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100=
%;"><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> <td style=3D"padding-top: 4=
px;"><![endif]--> <table class=3D"inline-block" style=3D"-webkit-text-size-=
adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-=
rspace: 0pt; display: inline-block;"> <tr> <td class=3D"job-card-flavor__co=
ntainer_redesign_shaded_background" style=3D"-webkit-text-size-adjust: 100%=
; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;=
 margin-top: 4px; margin-right: 4px; display: inline-block; border-radius: =
4px; background-color: #f5f7f9; padding: 4px;"> <table role=3D"presentation=
" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"=
100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; =
mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"p=
r-0.5 w-1" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 1=
00%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 8px; padding-righ=
t: 4px;" width=3D"8"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/1sk=
0fbe9amaiat5elvybbtxsm" alt=3D"Easy Apply" class=3D"w-2 h-2 rounded-sm bloc=
k" style=3D"outline: none; text-decoration: none; -ms-interpolation-mode: b=
icubic; display: block; height: 16px; width: 16px; border-radius: 4px;" wid=
th=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text-size-adjust: 100%;=
 -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"=
> <p class=3D"job-card-flavor__detail" style=3D"margin: 0; font-weight: 400=
; font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Ro=
boto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantare=
ll, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', =
'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif; font-size=
: 12px; line-height: 1.25; color: #666666;"> Easy Apply </p> </td> </tr> </=
tbody> </table> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> </tr><=
![endif]--> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=
=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endif]-=
-> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </a> </td> <=
/tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> <=
/table> </td> </tr> <tr> <td class=3D"pt-3" data-test-id=3D"job-card" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt; padding-top: 24px;"> <table role=3D"pres=
entation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" w=
idth=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td st=
yle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tabl=
e-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=
=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" st=
yle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tabl=
e-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=
=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=3D"top"=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt; width: 48px; padding-right: 8px;" =
width=3D"48"> <a href=3D"https://www.linkedin.com/comm/jobs/view/4149650574=
/?trackingId=3DhVRtYLJz40PW%2F5rwjNGP0Q%3D%3D&amp;refId=3DByteString%28leng=
th%3D16%2Cbytes%3D34064aa2...9118f29c%29&amp;lipi=3Durn%3Ali%3Apage%3Aemail=
_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3D=
AQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_di=
gest_01-job_card-0-company_logo&amp;trkEmail=3Deml-email_job_alert_digest_0=
1-job_card-0-company_logo-null-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfq=
f-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4Nm=
M3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3M=
DdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" style=3D"c=
olor: #0a66c2; cursor: pointer; display: inline-block; text-decoration: non=
e; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <img class=
=3D"inline-block relative bg-color-entity-ghost-background w-6 h-6 rounded-=
[2px]" src=3D"https://media.licdn.com/dms/image/v2/D560BAQFqH_Hp4Ce4_g/comp=
any-logo_100_100/company-logo_100_100/0/1712132933560/premier_services_and_=
recruitment_logo?e=3D2147483647&amp;v=3Dbeta&amp;t=3DqLxkg4IniXxGGcwJGqdunx=
hF2-3zCBV5n8aXq0Fr8oM" alt=3D"Premier Services and Recruitment" style=3D"ou=
tline: none; text-decoration: none; -ms-interpolation-mode: bicubic; positi=
on: relative; display: inline-block; height: 48px; width: 48px; border-radi=
us: 2px; background-color: #eae6df;" width=3D"48" height=3D"48"> </a> </td>=
 <td valign=3D"top" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-=
adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"ht=
tps://www.linkedin.com/comm/jobs/view/4149650574/?trackingId=3DhVRtYLJz40PW=
%2F5rwjNGP0Q%3D%3D&amp;refId=3DByteString%28length%3D16%2Cbytes%3D34064aa2.=
..9118f29c%29&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%=
3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=
=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-job_card-0-job_po=
sting&amp;trkEmail=3Deml-email_job_alert_digest_01-job_card-0-job_posting-n=
ull-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=
=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4=
Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk=
3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" style=3D"color: #0a66c2; cursor: poi=
nter; display: inline-block; text-decoration: none; -webkit-text-size-adjus=
t: 100%; -ms-text-size-adjust: 100%;"> <table role=3D"presentation" valign=
=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" st=
yle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tabl=
e-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pb-0" sty=
le=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table=
-lspace: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;"> <a href=3D"http=
s://www.linkedin.com/comm/jobs/view/4149650574/?trackingId=3DhVRtYLJz40PW%2=
F5rwjNGP0Q%3D%3D&amp;refId=3DByteString%28length%3D16%2Cbytes%3D34064aa2...=
9118f29c%29&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3B=
Yvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1=
HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-job_card-0-jobcard_bo=
dy&amp;trkEmail=3Deml-email_job_alert_digest_01-job_card-0-jobcard_body-nul=
l-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3D=
MWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk=
0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3Ym=
VlYTQ4YiwxLDE%3D" target=3D"_blank" class=3D"font-bold text-md leading-regu=
lar text-system-blue-50" style=3D"color: #0a66c2; cursor: pointer; display:=
 inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; font-size: 16px; font-weight: 600; line-height: 1.25=
;"> Full Stack Engineer </a> </td> </tr> <tr> <td class=3D"pb-0" style=3D"-=
webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace=
: 0pt; mso-table-rspace: 0pt; padding-bottom: 0px;"> <p class=3D"text-syste=
m-gray-100 text-xs leading-regular mt-0.5 line-clamp-1 text-ellipsis" style=
=3D"margin: 0; font-weight: 400; margin-top: 4px; text-overflow: ellipsis; =
font-size: 12px; line-height: 1.25; color: #1f1f1f; overflow: hidden; displ=
ay: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1;"> Pre=
mier Services and Recruitment &middot; Cairo, Egypt (On-site) </p> </td> </=
tr> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust:=
 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"prese=
ntation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" wi=
dth=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust:=
 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td sty=
le=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table=
-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0;" align=3D"left"> <!--[if=
 (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=3D"0" border=3D"0" =
role=3D"presentation" style=3D"width: 100%;"><![endif]--> <!--[if (gte mso =
9)|(IE)]><tr> <td style=3D"padding-top: 4px;"><![endif]--> <table class=3D"=
inline-block" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block=
;"> <tr> <td class=3D"job-card-flavor__container_redesign_shaded_background=
" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-=
table-lspace: 0pt; mso-table-rspace: 0pt; margin-top: 4px; margin-right: 4p=
x; display: inline-block; border-radius: 4px; background-color: #f5f7f9; pa=
dding: 4px;"> <table role=3D"presentation" valign=3D"top" border=3D"0" cell=
spacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-a=
djust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-r=
space: 0pt;"> <tbody> <tr> <td class=3D"pr-0.5 w-1" style=3D"-webkit-text-s=
ize-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-ta=
ble-rspace: 0pt; width: 8px; padding-right: 4px;" width=3D"8"> <img src=3D"=
https://static.licdn.com/aero-v1/sc/h/3le24jev2hig88cuokev26ksm" alt class=
=3D"w-2 h-2 block" style=3D"outline: none; text-decoration: none; -ms-inter=
polation-mode: bicubic; display: block; height: 16px; width: 16px;" width=
=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> =
<p class=3D"job-card-flavor__detail" style=3D"margin: 0; font-weight: 400; =
font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Robo=
to, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell=
, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'S=
egoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif; font-size: =
12px; line-height: 1.25; color: #666666;"> Actively recruiting </p> </td> <=
/tr> </tbody> </table> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td>=
 </tr><![endif]--> <!--[if (gte mso 9)|(IE)]><tr> <td style=3D"padding-top:=
 4px;"><![endif]--> <table class=3D"inline-block" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; display: inline-block;"> <tr> <td class=3D"job-card-flavor__=
container_redesign_shaded_background" style=3D"-webkit-text-size-adjust: 10=
0%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0p=
t; margin-top: 4px; margin-right: 4px; display: inline-block; border-radius=
: 4px; background-color: #f5f7f9; padding: 4px;"> <table role=3D"presentati=
on" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=
=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 10=
0%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=
=3D"pr-0.5 w-1" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adju=
st: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 8px; padding=
-right: 4px;" width=3D"8"> <img src=3D"https://static.licdn.com/aero-v1/sc/=
h/1sk0fbe9amaiat5elvybbtxsm" alt=3D"Easy Apply" class=3D"w-2 h-2 rounded-sm=
 block" style=3D"outline: none; text-decoration: none; -ms-interpolation-mo=
de: bicubic; display: block; height: 16px; width: 16px; border-radius: 4px;=
" width=3D"16" height=3D"16"> </td> <td style=3D"-webkit-text-size-adjust: =
100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: =
0pt;"> <p class=3D"job-card-flavor__detail" style=3D"margin: 0; font-weight=
: 400; font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI=
', Roboto, 'Helvetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Ca=
ntarell, 'Droid Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emo=
ji', 'Segoe UI Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif; font=
-size: 12px; line-height: 1.25; color: #666666;"> Easy Apply </p> </td> </t=
r> </tbody> </table> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> <=
/tr><![endif]--> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspa=
cing=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100%;"><![end=
if]--> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </a> </t=
d> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbod=
y> </table> </td> </tr> <tr> <td class=3D"pt-3" data-test-id=3D"job-card" s=
tyle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tab=
le-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 24px;"> <table role=3D"=
presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"=
0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-ad=
just: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <t=
d style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-=
table-lspace: 0pt; mso-table-rspace: 0pt;"> <table role=3D"presentation" va=
lign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%=
" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-=
table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0p=
t; mso-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" bor=
der=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webk=
it-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0p=
t; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-1 w-6" valign=3D"t=
op" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; ms=
o-table-lspace: 0pt; mso-table-rspace: 0pt; width: 48px; padding-right: 8px=
;" width=3D"48"> <a href=3D"https://www.linkedin.com/comm/jobs/view/4147655=
692/?trackingId=3DYWTV4iLhqrW425PHagFpbA%3D%3D&amp;refId=3DByteString%28len=
gth%3D16%2Cbytes%3D34064aa2...9118f29c%29&amp;lipi=3Durn%3Ali%3Apage%3Aemai=
l_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=
=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert=
_digest_01-job_card-0-company_logo&amp;trkEmail=3Deml-email_job_alert_diges=
t_01-job_card-0-company_logo-null-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfo=
jfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE=
4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yj=
c3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" style=
=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration=
: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <img =
class=3D"inline-block relative bg-color-entity-ghost-background w-6 h-6 rou=
nded-[2px]" src=3D"https://media.licdn.com/dms/image/v2/D4D0BAQEf2zS7vDikFw=
/company-logo_100_100/company-logo_100_100/0/1727367113340/ramyro_inc_logo?=
e=3D2147483647&amp;v=3Dbeta&amp;t=3DBU6cvaPQUa0F9aRo7tvUWTI0vzfyIiAjL6KSP-R=
9xB0" alt=3D"RAMYRO Inc." style=3D"outline: none; text-decoration: none; -m=
s-interpolation-mode: bicubic; position: relative; display: inline-block; h=
eight: 48px; width: 48px; border-radius: 2px; background-color: #eae6df;" w=
idth=3D"48" height=3D"48"> </a> </td> <td valign=3D"top" style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt;"> <a href=3D"https://www.linkedin.com/comm/jobs/view/=
4147655692/?trackingId=3DYWTV4iLhqrW425PHagFpbA%3D%3D&amp;refId=3DByteStrin=
g%28length%3D16%2Cbytes%3D34064aa2...9118f29c%29&amp;lipi=3Durn%3Ali%3Apage=
%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;mid=
Token=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_=
alert_digest_01-job_card-0-job_posting&amp;trkEmail=3Deml-email_job_alert_d=
igest_01-job_card-0-job_posting-null-fojfqf~m71tz67j~zy-null-null&amp;eid=
=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2=
U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZ=
TA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" st=
yle=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decorat=
ion: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <t=
able role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" ce=
llpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms=
-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <t=
body> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -ms-=
text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; paddi=
ng-bottom: 0px;"> <a href=3D"https://www.linkedin.com/comm/jobs/view/414765=
5692/?trackingId=3DYWTV4iLhqrW425PHagFpbA%3D%3D&amp;refId=3DByteString%28le=
ngth%3D16%2Cbytes%3D34064aa2...9118f29c%29&amp;lipi=3Durn%3Ali%3Apage%3Aema=
il_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=
=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert=
_digest_01-job_card-0-jobcard_body&amp;trkEmail=3Deml-email_job_alert_diges=
t_01-job_card-0-jobcard_body-null-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfo=
jfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE=
4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yj=
c3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" class=
=3D"font-bold text-md leading-regular text-system-blue-50" style=3D"color: =
#0a66c2; cursor: pointer; display: inline-block; text-decoration: none; -we=
bkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; font-size: 16px; f=
ont-weight: 600; line-height: 1.25;"> Full Stack Software Engineer </a> </t=
d> </tr> <tr> <td class=3D"pb-0" style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; pa=
dding-bottom: 0px;"> <p class=3D"text-system-gray-100 text-xs leading-regul=
ar mt-0.5 line-clamp-1 text-ellipsis" style=3D"margin: 0; font-weight: 400;=
 margin-top: 4px; text-overflow: ellipsis; font-size: 12px; line-height: 1.=
25; color: #1f1f1f; overflow: hidden; display: -webkit-box; -webkit-box-ori=
ent: vertical; -webkit-line-clamp: 1;"> RAMYRO Inc. &middot; Cairo, Egypt (=
Hybrid) </p> </td> </tr> <tr> <td style=3D"-webkit-text-size-adjust: 100%; =
-ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">=
 <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0"=
 cellpadding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; =
-ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">=
 <tbody> <tr> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-ad=
just: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0;" al=
ign=3D"left"> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacin=
g=3D"0" border=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endif]=
--> <!--[if (gte mso 9)|(IE)]><tr> <td style=3D"padding-top: 4px;"><![endif=
]--> <table class=3D"inline-block" style=3D"-webkit-text-size-adjust: 100%;=
 -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; =
display: inline-block;"> <tr> <td class=3D"job-card-flavor__container_redes=
ign_shaded_background" style=3D"-webkit-text-size-adjust: 100%; -ms-text-si=
ze-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin-top: =
4px; margin-right: 4px; display: inline-block; border-radius: 4px; backgrou=
nd-color: #f5f7f9; padding: 4px;"> <table role=3D"presentation" valign=3D"t=
op" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td class=3D"pr-0.5 w-1" =
style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-ta=
ble-lspace: 0pt; mso-table-rspace: 0pt; width: 8px; padding-right: 4px;" wi=
dth=3D"8"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/1sk0fbe9amaiat=
5elvybbtxsm" alt=3D"Easy Apply" class=3D"w-2 h-2 rounded-sm block" style=3D=
"outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; dis=
play: block; height: 16px; width: 16px; border-radius: 4px;" width=3D"16" h=
eight=3D"16"> </td> <td style=3D"-webkit-text-size-adjust: 100%; -ms-text-s=
ize-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <p class=
=3D"job-card-flavor__detail" style=3D"margin: 0; font-weight: 400; font-fam=
ily: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Hel=
vetica Neue', 'Fira Sans', Ubuntu, Oxygen, 'Oxygen Sans', Cantarell, 'Droid=
 Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Emoji', 'Segoe UI =
Symbol', 'Lucida Grande', Helvetica, Arial, sans-serif; font-size: 12px; li=
ne-height: 1.25; color: #666666;"> Easy Apply </p> </td> </tr> </tbody> </t=
able> </td> </tr> </table> <!--[if (gte mso 9)|(IE)]></td> </tr><![endif]--=
> <!--[if (gte mso 9)|(IE)]><table cellpadding=3D"0" cellspacing=3D"0" bord=
er=3D"0" role=3D"presentation" style=3D"width: 100%;"><![endif]--> </td> </=
tr> </tbody> </table> </td> </tr> </tbody> </table> </a> </td> </tr> </tbod=
y> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </t=
d> </tr> <tr> <td class=3D"pt-3 text-left" style=3D"-webkit-text-size-adjus=
t: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspac=
e: 0pt; padding-top: 24px; text-align: left;"> <table role=3D"presentation"=
 valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"1=
00%" class=3D"email-button " data-test-id=3D"email-button" style=3D"-webkit=
-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt;=
 mso-table-rspace: 0pt;"> <tbody> <tr> <td valign=3D"middle" align=3D"left"=
 style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-t=
able-lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://www.linkedin.=
com/comm/jobs/search?f_TPR=3Da1739267222-&amp;savedSearchId=3D1740684387&am=
p;alertAction=3Dviewjobs&amp;origin=3DJOB_ALERT_EMAIL&amp;lipi=3Durn%3Ali%3=
Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&am=
p;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email=
_job_alert_digest_01-job~alert-0-see~all~jobs&amp;trkEmail=3Deml-email_job_=
alert_digest_01-job~alert-0-see~all~jobs-null-fojfqf~m71tz67j~zy-null-null&=
amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGV=
kNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZG=
UwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_bl=
ank" aria-label=3D"See all jobs" class=3D"align-top no-underline " style=3D=
"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration: n=
one; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; vertical-a=
lign: top; text-decoration-line: none;"> <table role=3D"presentation" valig=
n=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"auto" c=
lass=3D"border-separate " style=3D"-webkit-text-size-adjust: 100%; -ms-text=
-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-co=
llapse: separate;"> <tbody> <tr> <td class=3D"btn-md btn-primary border-col=
or-brand button-link leading-regular !min-h-[auto] !shadow-none border-1 bo=
rder-solid primary-cta " style=3D"-webkit-text-size-adjust: 100%; -ms-text-=
size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; height: mi=
n-content; border-radius: 24px; padding-top: 12px; padding-bottom: 12px; pa=
dding-left: 24px; padding-right: 24px; text-align: center; font-size: 16px;=
 font-weight: 600; cursor: pointer; text-decoration-line: none; background-=
color: #0a66c2; color: #ffffff; border-width: 1px; border-style: solid; bor=
der-color: #0a66c2; line-height: 1.25; min-height: auto !important; box-sha=
dow: 0 0 #0000, 0 0 #0000, 0 0 #0000 !important;"> <a href=3D"https://www.l=
inkedin.com/comm/jobs/search?f_TPR=3Da1739267222-&amp;savedSearchId=3D17406=
84387&amp;alertAction=3Dviewjobs&amp;origin=3DJOB_ALERT_EMAIL&amp;lipi=3Dur=
n%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%=
3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3De=
ml-email_job_alert_digest_01-job~alert-0-see~all~jobs&amp;trkEmail=3Deml-em=
ail_job_alert_digest_01-job~alert-0-see~all~jobs-null-fojfqf~m71tz67j~zy-nu=
ll-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2J=
kMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2=
FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" targe=
t=3D"_blank" tabindex=3D"-1" aria-hidden=3D"true" class=3D"no-underline" st=
yle=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decorat=
ion: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; text=
-decoration-line: none;"> <span class=3D"no-underline text-white" style=3D"=
color: #ffffff; text-decoration-line: none;"> See all jobs </span> </a> </t=
d> </tr> </tbody> </table> </a> </td> </tr> </tbody> </table> </td> </tr> <=
tr data-test-id=3D"premium-upsell-divider"> <td class=3D"pt-3" style=3D"-we=
bkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: =
0pt; mso-table-rspace: 0pt; padding-top: 24px;"> <hr class=3D"bg-[#D9D9D9] =
border-0 h-[1px] m-0" style=3D"margin: 0px; height: 1px; border-width: 0; b=
ackground-color: #D9D9D9;"> </td> </tr> <tr data-test-id=3D"premium-upsell"=
> <td class=3D"pt-3" data-test-id=3D"premium-upsell" style=3D"-webkit-text-=
size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-t=
able-rspace: 0pt; padding-top: 24px;"> <table role=3D"presentation" valign=
=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" st=
yle=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-tabl=
e-lspace: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td style=3D"-webkit-t=
ext-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; m=
so-table-rspace: 0pt;"> <table role=3D"presentation" valign=3D"top" border=
=3D"0" cellspacing=3D"0" cellpadding=3D"0" width=3D"100%" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt;"> <tbody> <tr> <td align=3D"left" valign=3D"top" cla=
ss=3D"w-0" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 1=
00%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 0px;" width=3D"0"=
> <a href=3D"https://eg.linkedin.com/comm/in/pep0x?lipi=3Durn%3Ali%3Apage%3=
Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midTo=
ken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_al=
ert_digest_01-job~alert-0-premium~upsell&amp;trkEmail=3Deml-email_job_alert=
_digest_01-job~alert-0-premium~upsell-null-fojfqf~m71tz67j~zy-null-null&amp=
;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkND=
UxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwM=
GIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank=
" style=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-dec=
oration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"=
> <img class=3D"w-[84px]" alt=3D"Premium icon" src=3D"https://static.licdn.=
com/aero-v1/sc/h/6mjomvgg6rdiwm5rnormbdyja" style=3D"outline: none; text-de=
coration: none; -ms-interpolation-mode: bicubic; width: 84px;" width=3D"84"=
> </a> </td> </tr> <tr> <td class=3D"text-center" align=3D"center" style=3D=
"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspa=
ce: 0pt; mso-table-rspace: 0pt; text-align: center;"> <img class=3D"inline-=
block relative bg-color-entity-ghost-background clip-path-circle-50 rounded=
-full w-16 h-16" src=3D"https://media.licdn.com/dms/image/v2/D4D03AQEDH6nP3=
hqAEg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_2=
00/0/1705302302995?e=3D2147483647&amp;v=3Dbeta&amp;t=3D9nmr2MC1Wr3ArYEPRa75=
uP-u0klA1Y_Yel_-Q3Bm934" alt=3D"Abanoub Nashaat" style=3D"outline: none; te=
xt-decoration: none; -ms-interpolation-mode: bicubic; position: relative; d=
isplay: inline-block; height: 128px; width: 128px; border-radius: 9999px; b=
ackground-color: #eae6df; clip-path: circle(50%);" width=3D"128" height=3D"=
128"> </td> </tr> </tbody> </table> </td> </tr> <tr> <td class=3D"text-lg l=
eading-regular text-center pt-2" style=3D"-webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; pa=
dding-top: 16px; text-align: center; font-size: 20px; line-height: 1.25;"> =
Job search smarter with Premium </td> </tr> <tr> <td class=3D"pt-2" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt; padding-top: 16px;"> <table role=3D"pres=
entation" valign=3D"top" border=3D"0" cellspacing=3D"0" cellpadding=3D"0" w=
idth=3D"100%" class=3D"email-button " data-test-id=3D"email-button" style=
=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-l=
space: 0pt; mso-table-rspace: 0pt;"> <tbody> <tr> <td valign=3D"middle" ali=
gn=3D"middle" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust=
: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <a href=3D"https://=
www.linkedin.com/comm/premium/products/?upsellOrderOrigin=3Demail_job_alert=
_digest_taj_upsell&amp;utype=3Djob&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email=
_job_alert_digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-h=
vPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_0=
1-null-0-null&amp;trkEmail=3Deml-email_job_alert_digest_01-null-0-null-null=
-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DM=
WIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0=
ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmV=
lYTQ4YiwxLDE%3D" target=3D"_blank" aria-label=3D"Try 1 month for $0" class=
=3D"align-top no-underline " style=3D"color: #0a66c2; cursor: pointer; disp=
lay: inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; vertical-align: top; text-decoration-line: none;=
"> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"=
0" cellpadding=3D"0" width=3D"auto" class=3D"border-separate " style=3D"-we=
bkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: =
0pt; mso-table-rspace: 0pt; border-collapse: separate;"> <tbody> <tr> <td c=
lass=3D"btn-sm btn-secondary !border-[#424242] button-link leading-regular =
!min-h-[auto] !shadow-none border-1 border-solid" style=3D"-webkit-text-siz=
e-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-tabl=
e-rspace: 0pt; height: min-content; border-radius: 24px; padding-top: 7px; =
padding-bottom: 7px; padding-left: 16px; padding-right: 16px; text-align: c=
enter; font-size: 14px; font-weight: 600; cursor: pointer; text-decoration-=
line: none; background-color: rgba(0, 0, 0, 0); color: rgba(0, 0, 0, 0.75);=
 border-width: 1px; border-style: solid; line-height: 1.25; min-height: aut=
o !important; border-color: #424242 !important; box-shadow: 0 0 #0000, 0 0 =
#0000, 0 0 #0000 !important;"> <a href=3D"https://www.linkedin.com/comm/pre=
mium/products/?upsellOrderOrigin=3Demail_job_alert_digest_taj_upsell&amp;ut=
ype=3Djob&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYv=
x9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1Hn=
gcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-null-0-null&amp;trkEmai=
l=3Deml-email_job_alert_digest_01-null-0-null-null-fojfqf~m71tz67j~zy-null-=
null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMj=
QwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM=
2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=
=3D"_blank" tabindex=3D"-1" aria-hidden=3D"true" class=3D"no-underline" sty=
le=3D"color: #0a66c2; cursor: pointer; display: inline-block; text-decorati=
on: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; text-=
decoration-line: none;"> <span class=3D"no-underline !text-[#424242]" style=
=3D"text-decoration-line: none; color: #424242 !important;"> Try 1 month fo=
r $0 </span> </a> </td> </tr> </tbody> </table> </a> </td> </tr> </tbody> <=
/table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </div> =
</td> </tr> <tr> <td class=3D"bg-color-background-canvas p-3" style=3D"-web=
kit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0=
pt; mso-table-rspace: 0pt; background-color: #f3f2f0; padding: 24px;"> <tab=
le role=3D"presentation" valign=3D"top" border=3D"0" cellspacing=3D"0" cell=
padding=3D"0" width=3D"100%" style=3D"-webkit-text-size-adjust: 100%; -ms-t=
ext-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tbo=
dy> <tr> <td class=3D"text-center pb-2" style=3D"-webkit-text-size-adjust: =
100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: =
0pt; padding-bottom: 16px; text-align: center;"> <h2 class=3D"text-lg text-=
teal-80" style=3D"margin: 0; font-weight: 500; font-size: 20px; color: #114=
951;"> Get the new LinkedIn desktop app </h2> </td> </tr> <tr> <td class=3D=
"text-center pb-2" data-test-id=3D"footer-app-store-icon" style=3D"-webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; =
mso-table-rspace: 0pt; padding-bottom: 16px; text-align: center;"> <a href=
=3D"https://apps.microsoft.com/store/detail/9WZDNCRFJ4Q7?launch=3Dtrue&amp;=
cid=3Dlinkedin_email_upsell&amp;mode=3Dfull" target=3D"_blank" rel=3D"noope=
ner noreferrer" style=3D"color: #0a66c2; cursor: pointer; display: inline-b=
lock; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-=
adjust: 100%;"> <img alt=3D"Get it from Microsoft" src=3D"https://static.li=
cdn.com/aero-v1/sc/h/ejpkkpwvqks31a3cjqokb7fbm" class=3D"h-[40px] w-[112px]=
" style=3D"outline: none; text-decoration: none; -ms-interpolation-mode: bi=
cubic; height: 40px; width: 112px;" width=3D"112" height=3D"40"> </a> </td>=
 </tr> <tr> <td class=3D"text-center pb-2" style=3D"-webkit-text-size-adjus=
t: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspac=
e: 0pt; padding-bottom: 16px; text-align: center;"> <h2 class=3D"text-md te=
xt-teal-80" style=3D"margin: 0; font-weight: 500; font-size: 16px; color: #=
114951;"> Also available on mobile </h2> </td> </tr> <tr> <td class=3D"text=
-center" data-test-id=3D"footer-app-store-icon" style=3D"-webkit-text-size-=
adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-=
rspace: 0pt; text-align: center;"> <a href=3D"https://itunes.apple.com/us/a=
pp/linkedin/id288429040?pt=3D10746&amp;ct=3Dst_appsite_flagship&amp;mt=3D8"=
 target=3D"_blank" rel=3D"noopener noreferrer" style=3D"color: #0a66c2; cur=
sor: pointer; display: inline-block; text-decoration: none; -webkit-text-si=
ze-adjust: 100%; -ms-text-size-adjust: 100%;"> <img alt=3D"Download on the =
App Store" src=3D"https://static.licdn.com/aero-v1/sc/h/76yzkd0h5kiv27lrd4y=
aenylk" class=3D"h-[40px] w-[120px] pr-1" style=3D"outline: none; text-deco=
ration: none; -ms-interpolation-mode: bicubic; height: 40px; width: 120px; =
padding-right: 8px;" width=3D"120" height=3D"40"> </a> <a href=3D"https://p=
lay.google.com/store/apps/details?id=3Dcom.linkedin.android&amp;referrer=3D=
st_appsite_flagship" target=3D"_blank" rel=3D"noopener noreferrer" style=3D=
"color: #0a66c2; cursor: pointer; display: inline-block; text-decoration: n=
one; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;"> <img alt=
=3D"Get it on Google Play" src=3D"https://static.licdn.com/aero-v1/sc/h/142=
qudwblp58zwmc9vkqfplug" class=3D"h-[40px] w-[134px]" style=3D"outline: none=
; text-decoration: none; -ms-interpolation-mode: bicubic; height: 40px; wid=
th: 134px;" width=3D"134" height=3D"40"> </a> </td> </tr> <tr> <td class=3D=
"py-2" style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;=
 mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding-top: 16px; padding-b=
ottom: 16px;"><hr class=3D"border-none bg-[#e0dfdd] h-[1px]" style=3D"heigh=
t: 1px; border-style: none; background-color: #e0dfdd;"></td> </tr> </tbody=
> </table> <table role=3D"presentation" valign=3D"top" border=3D"0" cellspa=
cing=3D"0" cellpadding=3D"0" width=3D"100%" class=3D"text-xs" style=3D"-web=
kit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0=
pt; mso-table-rspace: 0pt; font-size: 12px;"> <tbody> <tr> <td class=3D"pb-=
1 m-0" data-test-id=3D"email-footer__intended" style=3D"-webkit-text-size-a=
djust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-r=
space: 0pt; margin: 0px; padding-bottom: 8px;"> This email was intended for=
 Abanoub Nashaat (CS Student || Aspiring Software Developer) </td> </tr> <t=
r> <td class=3D"pb-1 m-0" style=3D"-webkit-text-size-adjust: 100%; -ms-text=
-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin: 0=
px; padding-bottom: 8px;"> <a href=3D"https://www.linkedin.com/help/linkedi=
n/answer/4788?lang=3Den&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_=
digest_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&am=
p;midSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-SecurityH=
elp-0-footerglimmer&amp;trkEmail=3Deml-email_job_alert_digest_01-SecurityHe=
lp-0-footerglimmer-null-fojfqf~m71tz67j~zy-null-null&amp;eid=3Dfojfqf-m71tz=
67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0M=
zkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRl=
ZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=3D"_blank" class=3D"text-inh=
erit underline" style=3D"cursor: pointer; display: inline-block; text-decor=
ation: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; co=
lor: inherit; text-decoration-line: underline;">Learn why we included this.=
</a> </td> </tr> <tr> <td class=3D"pb-1 m-0" style=3D"-webkit-text-size-adj=
ust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rsp=
ace: 0pt; margin: 0px; padding-bottom: 8px;">You are receiving Job Alert em=
ails.</td> </tr> <tr> <td class=3D"pb-1 m-0" style=3D"-webkit-text-size-adj=
ust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rsp=
ace: 0pt; margin: 0px; padding-bottom: 8px;"> <a href=3D"https://www.linked=
in.com/comm/jobs/alerts?lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_dige=
st_01%3BYvx9hvzeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;mi=
dSig=3D1HngcRkFijlXE1&amp;trk=3Deml-email_job_alert_digest_01-null-0-null&a=
mp;trkEmail=3Deml-email_job_alert_digest_01-null-0-null-null-fojfqf~m71tz67=
j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJl=
Y2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDd=
kZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D=
" target=3D"_blank" trackingmodule=3D"footer" trackingsuffix=3D"manage_aler=
t_mercado" style=3D"color: #0a66c2; cursor: pointer; display: inline-block;=
 text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-size-adjus=
t: 100%;"> Manage job alerts </a> =C2=A0=C2=A0=C2=B7=C2=A0=C2=A0 <a href=3D=
"https://www.linkedin.com/job-alert-email-unsubscribe?savedSearchId=3D17406=
84387&amp;lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hv=
zeT82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRk=
FijlXE1&amp;ek=3Demail_job_alert_digest_01&amp;e=3Dfojfqf-m71tz67j-zy&amp;e=
id=3Dfojfqf-m71tz67j-zy&amp;m=3Dunsubscribe&amp;ts=3DfooterGlimmer&amp;li=
=3D0&amp;t=3Dplh" target=3D"_blank" class=3D"text-inherit underline" style=
=3D"cursor: pointer; display: inline-block; text-decoration: none; -webkit-=
text-size-adjust: 100%; -ms-text-size-adjust: 100%; color: inherit; text-de=
coration-line: underline;">Unsubscribe</a> =C2=A0=C2=A0=C2=B7=C2=A0=C2=A0 <=
a href=3D"https://www.linkedin.com/help/linkedin/answer/67?lang=3Den&amp;li=
pi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT82uLzB%2F=
LbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFijlXE1&amp;=
trk=3Deml-email_job_alert_digest_01-help-0-footerglimmer&amp;trkEmail=3Deml=
-email_job_alert_digest_01-help-0-footerglimmer-null-fojfqf~m71tz67j~zy-nul=
l-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJlY2JjM2Jk=
MjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZDdkZmUwN2F=
kM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3D" target=
=3D"_blank" class=3D"text-inherit underline" style=3D"cursor: pointer; disp=
lay: inline-block; text-decoration: none; -webkit-text-size-adjust: 100%; -=
ms-text-size-adjust: 100%; color: inherit; text-decoration-line: underline;=
">Help</a> </td> </tr> <tr> <td class=3D"pb-1" style=3D"-webkit-text-size-a=
djust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-r=
space: 0pt; padding-bottom: 8px;"> <a href=3D"https://www.linkedin.com/comm=
/feed/?lipi=3Durn%3Ali%3Apage%3Aemail_email_job_alert_digest_01%3BYvx9hvzeT=
82uLzB%2FLbOwrQ%3D%3D&amp;midToken=3DAQHb-hvPD2eSGA&amp;midSig=3D1HngcRkFij=
lXE1&amp;trk=3Deml-email_job_alert_digest_01-footer-0-logoGlimmer&amp;trkEm=
ail=3Deml-email_job_alert_digest_01-footer-0-logoGlimmer-null-fojfqf~m71tz6=
7j~zy-null-null&amp;eid=3Dfojfqf-m71tz67j-zy&amp;otpToken=3DMWIwMTE2ZTIxMzJ=
lY2JjM2JkMjQwNGVkNDUxN2U0YjE4NmM3ZDA0MzkxYTk4ZTYxNzljNTA4Njk0ZjVkNTRmMmYyZD=
dkZmUwN2FkM2RmZGUwMGIwZTA3Yjc3MDdlMGRlZGU1ZGIwYTZjMzIwODk3YmVlYTQ4YiwxLDE%3=
D" target=3D"_blank" style=3D"color: #0a66c2; cursor: pointer; display: inl=
ine-block; text-decoration: none; -webkit-text-size-adjust: 100%; -ms-text-=
size-adjust: 100%;"> <img src=3D"https://static.licdn.com/aero-v1/sc/h/9ehe=
6n39fa07dc5edzv0rla4e" alt=3D"LinkedIn" class=3D"block h-[14px] w-[56px] im=
age-rendering-crisp" style=3D"outline: none; text-decoration: none; image-r=
endering: -moz-crisp-edges; image-rendering: -o-crisp-edges; image-renderin=
g: -webkit-optimize-contrast; image-rendering: crisp-edges; -ms-interpolati=
on-mode: nearest-neighbor; display: block; height: 14px; width: 56px;" widt=
h=3D"56" height=3D"14"> </a> </td> </tr> <tr> <td data-test-copyright-text =
style=3D"-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-ta=
ble-lspace: 0pt; mso-table-rspace: 0pt;"> =C2=A9 2025 LinkedIn Corporation,=
 1&zwnj;000 West Maude Avenue, Sunnyvale, CA 94085. <span data-test-tradema=
rks-text> LinkedIn and the LinkedIn logo are registered trademarks of Linke=
dIn. </span> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </=
td> </tr> </tbody> </table> <img alt role=3D"presentation" src=3D"https://w=
ww.linkedin.com/emimp/ip_Wm05cVpuRm1MVzAzTVhSNk5qZHFMWHA1OlpXMWhhV3hmYW05aV=
gyRnNaWEowWDJScFoyVnpkRjh3TVE9PTo=3D.gif" style=3D"outline: none; text-deco=
ration: none; -ms-interpolation-mode: bicubic; width: 1px; height: 1px;" wi=
dth=3D"1" height=3D"1"> </body> </html>
------=_Part_2278920_1046509514.1739359903669--

