{"name": "darkops-backend", "type": "module", "version": "1.0.0", "scripts": {"dev": "tsx watch src/index.ts", "generate": "bun drizzle-kit generate", "migrate": "bun drizzle-kit migrate", "studio": "drizzle-kit studio", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@getcronit/pylon": "^2.9.4", "@graphql-tools/load-files": "^7.0.1", "@graphql-tools/merge": "^9.0.24", "@graphql-tools/schema": "^10.0.23", "@hono/node-server": "^1.13.8", "@hono/zod-openapi": "^0.18.4", "@hono/zod-validator": "^0.4.2", "@neondatabase/serverless": "^0.10.4", "@scalar/hono-api-reference": "^0.5.172", "@types/bcrypt": "^5.0.2", "axios": "^1.6.7", "bcrypt": "^5.1.1", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "express-session": "^1.18.1", "googleapis": "^144.0.0", "graphql": "^16.11.0", "graphql-request": "^7.1.2", "graphql-yoga": "^5.13.4", "hono": "^4.7.0", "hono-pino": "^0.7.2", "jsonwebtoken": "^9.0.2", "mjml": "^4.15.3", "mongoose": "^8.10.1", "nodemailer": "^6.10.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "redis": "^5.0.1", "resend": "^4.4.1", "stoker": "^1.4.2", "zod": "^3.24.1", "exceljs": "^4.4.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.8", "@types/mjml": "^4.7.4", "@types/node": "^20.17.17", "@types/nodemailer": "^6.4.17", "drizzle-kit": "^0.30.4", "tsx": "^4.19.2", "vitest": "^3.1.2"}}