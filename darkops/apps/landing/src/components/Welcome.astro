---
import astroLogo from '../assets/astro.svg';
import background from '../assets/background.svg';
const { title } = Astro.props;


const name = 'Abanoub Nashaat';
const age = 22;
const isAdmin = true;
const isActive = true;
const isLoggedIn = true;
const isGuest = false;


---

<div id="container">
	<img id="background" src={background.src} alt="" fetchpriority="high" />
	<main>
		<section id="hero">
			<a href="https://astro.build"
				><img src={astroLogo.src} width="115" height="48" alt="Astro Homepage" /></a
			>
			<div class="card-container">
				<div class="profile-card box">
					<div class="card-header">
						<h2>Profile Information</h2>
					</div>
					<div class="card-content">
						<div class="info-row">
							<span class="label">Name:</span>
							<span class="value">{name}</span>
						</div>
						<div class="info-row">
							<span class="label">Age:</span>
							<span class="value">{age}</span>
						</div>
					</div>
				</div>
				
				<div class="status-card box">
					<div class="card-header">
						<h2>Account Status</h2>
					</div>
					<div class="card-content">
						<div class="status-item">
							<span class="status-label">Admin</span>
							<span class="status-indicator" class:list={[{ active: isAdmin }]}></span>
						</div>
						<div class="status-item">
							<span class="status-label">Active</span>
							<span class="status-indicator" class:list={[{ active: isActive }]}></span>
						</div>
						<div class="status-item">
							<span class="status-label">Logged In</span>
							<span class="status-indicator" class:list={[{ active: isLoggedIn }]}></span>
						</div>
						<div class="status-item">
							<span class="status-label">Guest</span>
							<span class="status-indicator" class:list={[{ active: isGuest }]}></span>
						</div>
					</div>
				</div>
			</div>
		</section>
	</main>
</div>

<style>
	#background {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: -1;
		filter: blur(100px);
	}

	#container {
		font-family: Inter, Roboto, 'Helvetica Neue', 'Arial Nova', 'Nimbus Sans', Arial, sans-serif;
		height: 100%;
	}

	main {
		height: 100%;
		display: flex;
		justify-content: center;
	}

	#hero {
		display: flex;
		align-items: start;
		flex-direction: column;
		justify-content: center;
		padding: 16px;
		width: 100%;
		max-width: 800px;
	}

	h1 {
		font-size: 22px;
		margin-top: 0.25em;
	}

	.card-container {
		display: flex;
		flex-wrap: wrap;
		gap: 24px;
		width: 100%;
		margin-top: 24px;
	}

	.profile-card, .status-card {
		flex: 1 1 300px;
		overflow: hidden;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
		transition: transform 0.3s, box-shadow 0.3s;
	}

	.profile-card:hover, .status-card:hover {
		transform: translateY(-5px);
		box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
	}

	.card-header {
		padding-bottom: 12px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		margin-bottom: 16px;
	}
   
	.card-header h2 {
		margin: 0;
		font-size: 18px;
		background: linear-gradient(83.21deg, #3245ff 0%, #bc52ee 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	 	background-clip: text;
		font-weight: 600;
		text-align: center;
	}

	.card-content {
		display: flex;
		flex-direction: column;
		gap: 12px;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.label {
		color: #6b7280;
		font-size: 14px;
	}

	.value {
		font-weight: 500;
		color: #111827;
	}

	.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.status-label {
		color: #6b7280;
		font-size: 14px;
	}

	.status-indicator {
		width: 18px;
		height: 18px;
		border-radius: 50%;
		background-color: #e5e7eb;
		position: relative;
	}

	.status-indicator.active {
		background: linear-gradient(83.21deg, #3245ff 0%, #bc52ee 100%);
	}

	.status-indicator.active::after {
		content: "";
		position: absolute;
		top: 5px;
		left: 5px;
		width: 8px;
		height: 8px;
		border-radius: 50%;
		background-color: white;
	}

	#links {
		display: flex;
		gap: 16px;
	}

	#links a {
		display: flex;
		align-items: center;
		padding: 10px 12px;
		color: #111827;
		text-decoration: none;
		transition: color 0.2s;
	}

	#links a:hover {
		color: rgb(78, 80, 86);
	}

	#links a svg {
		height: 1em;
		margin-left: 8px;
	}

	#links a.button {
		color: white;
		background: linear-gradient(83.21deg, #3245ff 0%, #bc52ee 100%);
		box-shadow:
			inset 0 0 0 1px rgba(255, 255, 255, 0.12),
			inset 0 -2px 0 rgba(0, 0, 0, 0.24);
		border-radius: 10px;
	}

	#links a.button:hover {
		color: rgb(230, 230, 230);
		box-shadow: none;
	}

	pre {
		font-family:
			ui-monospace, 'Cascadia Code', 'Source Code Pro', Menlo, Consolas, 'DejaVu Sans Mono',
			monospace;
		font-weight: normal;
		background: linear-gradient(14deg, #d83333 0%, #f041ff 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		margin: 0;
	}

	h2 {
		margin: 0 0 1em;
		font-weight: normal;
		color: #111827;
		font-size: 20px;
	}

	p {
		color: #4b5563;
		font-size: 16px;
		line-height: 24px;
		letter-spacing: -0.006em;
		margin: 0;
	}

	code {
		display: inline-block;
		background:
			linear-gradient(66.77deg, #f3cddd 0%, #f5cee7 100%) padding-box,
			linear-gradient(155deg, #d83333 0%, #f041ff 18%, #f5cee7 45%) border-box;
		border-radius: 8px;
		border: 1px solid transparent;
		padding: 6px 8px;
	}

	.box {
		padding: 16px;
		background: rgba(255, 255, 255, 1);
		border-radius: 16px;
		border: 1px solid white;
	}

	#news {
		position: absolute;
		bottom: 16px;
		right: 16px;
		max-width: 300px;
		text-decoration: none;
		transition: background 0.2s;
		backdrop-filter: blur(50px);
	}

	#news:hover {
		background: rgba(255, 255, 255, 0.55);
	}

	@media screen and (max-height: 368px) {
		#news {
			display: none;
		}
	}

	@media screen and (max-width: 768px) {
		#container {
			display: flex;
			flex-direction: column;
		}

		#hero {
			display: block;
			padding-top: 10%;
		}

		#links {
			flex-wrap: wrap;
		}

		#links a.button {
			padding: 14px 18px;
		}

		#news {
			right: 16px;
			left: 16px;
			bottom: 2.5rem;
			max-width: 100%;
		}

		h1 {
			line-height: 1.5;
		}
		
		.card-container {
			flex-direction: column;
		}
	}
</style>
